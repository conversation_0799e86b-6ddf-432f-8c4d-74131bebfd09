# 电话区号+前缀调试修复文档

## 🔍 问题描述

用户反馈：无论是新创建还是保存草稿，所有的区号都没有带+号上传到服务器。

## 🕵️ 问题根本原因

### 发现的问题
1. **分步提交 vs 草稿保存的差异**：
   - 分步提交使用 `currentStepData.value`（已格式化，包含+前缀）
   - 草稿保存使用 `formData`（原始数据，不包含+前缀）

2. **数据流程差异**：
   ```javascript
   // 分步提交（正确）
   handleNext() → currentStepData.value → formatPhoneCode() → '+86'
   
   // 草稿保存（错误）
   handleSaveDraft() → saveForm() → formData → '86'（没有+前缀）
   ```

### 代码分析

#### 修复前的问题代码
```javascript
const saveForm = async () => {
  // 直接提交原始formData，没有格式化电话区号
  const response = await http[method](url, formData);
};
```

#### currentStepData中的正确处理
```javascript
const currentStepData = computed(() => {
  // 第三步数据格式化
  applicanttelephoneIsdCode: formatPhoneCode(formData.applicant_phone_code), // ✅ 正确
  
  // 第五步数据格式化
  invitinghoteltelephoneIsdCode: formatPhoneCode(formData.hotel_phone_code), // ✅ 正确
  invitingpersontelephoneIsdCode: formatPhoneCode(formData.inviting_person_phone_code), // ✅ 正确
  enterprisetelephoneIsdCode: formatPhoneCode(formData.enterprise_phone_code), // ✅ 正确
});
```

## 🔧 修复方案

### 修复后的saveForm函数
```javascript
const saveForm = async () => {
  submitting.value = true;
  try {
    const url = props.isEditing
      ? `/api/schengen-forms/${formData.id}`
      : '/api/schengen-forms';

    const method = props.isEditing ? 'put' : 'post';

    // 创建格式化后的数据，确保电话区号包含+前缀
    const formattedData = {
      ...formData,
      // 格式化所有电话区号字段
      applicant_phone_code: formatPhoneCode(formData.applicant_phone_code),
      hotel_phone_code: formatPhoneCode(formData.hotel_phone_code),
      inviting_person_phone_code: formatPhoneCode(formData.inviting_person_phone_code),
      enterprise_phone_code: formatPhoneCode(formData.enterprise_phone_code),
      organisation_phone_code: formatPhoneCode(formData.organisation_phone_code),
      temp_accommodation_phone_code: formatPhoneCode(formData.temp_accommodation_phone_code),
      regulations_phone_code: formatPhoneCode(formData.regulations_phone_code),
    };

    // 添加调试日志
    console.log('提交的格式化数据:', {
      applicant_phone_code: formattedData.applicant_phone_code,
      hotel_phone_code: formattedData.hotel_phone_code,
      inviting_person_phone_code: formattedData.inviting_person_phone_code,
      enterprise_phone_code: formattedData.enterprise_phone_code,
      organisation_phone_code: formattedData.organisation_phone_code,
      temp_accommodation_phone_code: formattedData.temp_accommodation_phone_code,
      regulations_phone_code: formattedData.regulations_phone_code,
    });

    const response = await http[method](url, formattedData);

    if (response.code !== 1) {
      throw new Error(response.message || '保存失败');
    }
  } finally {
    submitting.value = false;
  }
};
```

## 📊 修复效果对比

### 修复前
```javascript
// 保存草稿时提交的数据
{
  applicant_phone_code: '86',           // ❌ 没有+前缀
  hotel_phone_code: '86',               // ❌ 没有+前缀
  inviting_person_phone_code: '86',     // ❌ 没有+前缀
  enterprise_phone_code: '86',          // ❌ 没有+前缀
  // ... 其他字段
}
```

### 修复后
```javascript
// 保存草稿时提交的数据
{
  applicant_phone_code: '+86',          // ✅ 包含+前缀
  hotel_phone_code: '+86',              // ✅ 包含+前缀
  inviting_person_phone_code: '+86',    // ✅ 包含+前缀
  enterprise_phone_code: '+86',         // ✅ 包含+前缀
  // ... 其他字段
}
```

## 🧪 测试验证

### 测试场景1：保存草稿
1. **填写表单**：
   - 申请者电话区号：86
   - 酒店电话区号：1
   - 邀请人电话区号：44

2. **点击"保存草稿"**

3. **检查浏览器开发者工具Network标签**：
   ```javascript
   // 请求体应该包含
   {
     "applicant_phone_code": "+86",
     "hotel_phone_code": "+1", 
     "inviting_person_phone_code": "+44"
   }
   ```

4. **检查控制台日志**：
   ```javascript
   提交的格式化数据: {
     applicant_phone_code: '+86',
     hotel_phone_code: '+1',
     inviting_person_phone_code: '+44',
     // ...
   }
   ```

### 测试场景2：新创建表单
1. **创建新表单并填写信息**
2. **在任何步骤点击"保存草稿"**
3. **验证所有电话区号都包含+前缀**

### 测试场景3：编辑现有表单
1. **编辑现有表单**
2. **修改电话区号**
3. **保存草稿**
4. **验证更新后的区号包含+前缀**

## 🔍 调试方法

### 1. 浏览器开发者工具
```javascript
// 在保存前检查数据
console.log('原始formData:', {
  applicant_phone_code: formData.applicant_phone_code,
  hotel_phone_code: formData.hotel_phone_code
});

console.log('格式化后数据:', {
  applicant_phone_code: formatPhoneCode(formData.applicant_phone_code),
  hotel_phone_code: formatPhoneCode(formData.hotel_phone_code)
});
```

### 2. 网络请求检查
- 打开开发者工具 → Network标签
- 点击"保存草稿"
- 查看请求体中的电话区号字段
- 确认所有区号都包含+前缀

### 3. 后端日志检查
```python
# 在后端添加日志
log_info("接收到的电话区号", 
         applicant_phone_code=request_data.get('applicant_phone_code'),
         hotel_phone_code=request_data.get('hotel_phone_code'))
```

## ✅ 修复验证

### 修复前的问题
- ❌ 保存草稿时电话区号没有+前缀
- ❌ 新创建表单时电话区号没有+前缀
- ❌ 分步提交和草稿保存数据格式不一致

### 修复后的效果
- ✅ 保存草稿时所有电话区号都包含+前缀
- ✅ 新创建和编辑表单都正确处理电话区号
- ✅ 分步提交和草稿保存数据格式一致
- ✅ 添加了详细的调试日志

## 🎯 关键改进

1. **统一数据格式**：保存草稿和分步提交现在使用相同的格式化逻辑
2. **完整覆盖**：所有7个电话区号字段都得到正确处理
3. **调试支持**：添加了详细的控制台日志便于调试
4. **向后兼容**：不影响现有的分步提交逻辑

## 📝 相关文件

- `frontend-vite/src/components/SchengenFormEditor.vue` - 修改了saveForm函数

现在无论是新创建还是保存草稿，所有的电话区号都会正确包含+前缀上传到服务器！
