# 护照号查询申根申请表接口文档

## 📋 接口概述

根据护照号查询该护照号对应的所有申根申请表列表，返回申请号、护照号、创建人名字等信息。

## 🔗 接口信息

### 基本信息
- **接口路径**: `/api/schengen-forms/by-passport/{passport_number}`
- **请求方法**: `GET`
- **认证要求**: 无需票据验证
- **接口标签**: 申根签证

### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `passport_number` | string | 是 | 护照号码 |

## 📊 数据库查询

### 查询逻辑
```sql
SELECT
    sva.application_id,
    sva.passport_number,
    u.name as creator_name,
    sva.created_at,
    sva.status,
    sva.is_draft,
    sva.first_name,
    sva.last_name
FROM schengen_visa_applications sva
LEFT JOIN users u ON sva.user_id = u.id
WHERE sva.passport_number = %s
ORDER BY sva.created_at DESC
```

### 涉及表结构

#### schengen_visa_applications 表
- `application_id`: 申请ID (varchar(50))
- `passport_number`: 护照号码 (varchar(50))
- `user_id`: 用户ID (int8)
- `status`: 申请状态 (varchar(20), 默认'draft')
- `is_draft`: 是否草稿 (bool, 默认false)
- `first_name`: 名 (varchar(100))
- `last_name`: 姓 (varchar(100))
- `created_at`: 创建时间 (timestamp)

#### users 表
- `id`: 用户ID (serial4)
- `name`: 用户姓名 (varchar)

## 📤 响应格式

### 成功响应
```json
{
  "code": 1,
  "message": "查询成功",
  "data": [
    {
      "application_id": "SCHENGEN_1752401846181_673626",
      "passport_number": "*********",
      "creator_name": "张三",
      "applicant_name": "GONG GAOHU",
      "created_at": "2025-07-13 18:17:26",
      "status": "draft",
      "is_draft": true
    },
    {
      "application_id": "SCHENGEN_1752401846182_673627",
      "passport_number": "*********",
      "creator_name": "李四",
      "applicant_name": "GONG GAOHU",
      "created_at": "2025-07-12 15:30:45",
      "status": "submitted",
      "is_draft": false
    }
  ]
}
```

### 无数据响应
```json
{
  "code": 1,
  "message": "未找到该护照号的申请记录",
  "data": []
}
```

### 错误响应
```json
{
  "detail": "查询失败: 具体错误信息"
}
```

## 📋 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `application_id` | string | 申请号 |
| `passport_number` | string | 护照号码 |
| `creator_name` | string | 创建人姓名（来自users表的name字段） |
| `applicant_name` | string | 申请人姓名（last_name + first_name组合） |
| `created_at` | string | 创建时间（格式：YYYY-MM-DD HH:MM:SS） |
| `status` | string | 申请状态（draft/submitted等） |
| `is_draft` | boolean | 是否为草稿 |

## 🧪 测试用例

### 测试用例1：查询存在的护照号
**请求**:
```
GET /api/schengen-forms/by-passport/*********
```

**预期响应**:
```json
{
  "code": 1,
  "message": "查询成功",
  "data": [
    {
      "application_id": "SCHENGEN_1752401846181_673626",
      "passport_number": "*********",
      "creator_name": "张三",
      "applicant_name": "GONG GAOHU",
      "created_at": "2025-07-13 18:17:26",
      "status": "draft",
      "is_draft": true
    }
  ]
}
```

### 测试用例2：查询不存在的护照号
**请求**:
```
GET /api/schengen-forms/by-passport/NOTEXIST123
```

**预期响应**:
```json
{
  "code": 1,
  "message": "未找到该护照号的申请记录",
  "data": []
}
```

### 测试用例3：护照号包含特殊字符
**请求**:
```
GET /api/schengen-forms/by-passport/EP-3804229
```

**预期响应**: 正常查询，支持包含连字符的护照号

## 🔍 使用场景

### 1. 客服查询
客服人员根据用户提供的护照号快速查找该用户的所有申请记录。

### 2. 重复申请检查
在用户新建申请时，检查该护照号是否已有申请记录，避免重复申请。

### 3. 申请历史查看
用户或管理员查看某个护照号的所有申请历史记录。

### 4. 数据统计分析
统计某个护照号的申请次数、状态分布等信息。

## 📝 注意事项

### 1. 数据安全
- 接口无需认证，但建议在生产环境中根据需要添加适当的权限控制
- 护照号属于敏感信息，使用时需注意数据保护

### 2. 性能考虑
- 查询按创建时间倒序排列，最新的申请排在前面
- 如果数据量大，建议在passport_number字段上添加索引

### 3. 数据完整性
- 创建人姓名可能为空，返回"未知用户"
- 申请人姓名可能为空，返回"未填写"
- 创建时间可能为空，返回空字符串

## 🔧 扩展建议

### 1. 分页支持
如果单个护照号的申请记录很多，可以添加分页参数：
```
GET /api/schengen-forms/by-passport/{passport_number}?page=1&size=10
```

### 2. 状态过滤
添加状态过滤参数：
```
GET /api/schengen-forms/by-passport/{passport_number}?status=draft
```

### 3. 时间范围过滤
添加时间范围过滤：
```
GET /api/schengen-forms/by-passport/{passport_number}?start_date=2025-01-01&end_date=2025-12-31
```

## 📊 日志记录

接口会记录以下日志信息：
- 查询开始：记录护照号
- 查询成功：记录护照号和找到的记录数量
- 查询失败：记录护照号和错误信息

## ✅ 接口特点

- ✅ **无需认证**：简化调用流程
- ✅ **完整信息**：返回申请号、护照号、创建人名字
- ✅ **时间排序**：按创建时间倒序，最新记录在前
- ✅ **错误处理**：完善的异常处理和错误信息返回
- ✅ **日志记录**：详细的操作日志便于调试和监控
- ✅ **数据安全**：LEFT JOIN确保即使用户信息缺失也能正常返回

现在您可以使用这个接口根据护照号查询对应的所有申根申请表列表了！
