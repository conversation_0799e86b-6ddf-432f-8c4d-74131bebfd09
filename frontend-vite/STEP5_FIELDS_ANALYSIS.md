# 第五步住宿信息字段完整性分析

## 🔍 前端字段 vs 后端字段对比

### 1. 酒店信息 (Hotel) ✅ 完整支持
```javascript
// 前端字段
hotel_name, hotel_address, hotel_postal_code, hotel_email,
hotel_street, hotel_city, hotel_country, hotel_phone_code, hotel_phone

// 后端数据库字段
name_of_inviting_hotel, address_of_inviting_hotel, postal_code_of_inviting_hotel,
email_inviting_hotel, street_inviting_hotel, city_inviting_hotel, country_inviting_hotel,
inviting_hotel_telephone_isd_code, inviting_hotel_telephone_number
```
**状态**: ✅ 完全支持

### 2. 邀请人信息 (InvitingPerson) ✅ 完整支持
```javascript
// 前端字段
inviting_person_surname, inviting_person_given_name, inviting_person_address,
inviting_person_postal_code, inviting_person_email, inviting_person_street,
inviting_person_city, inviting_person_country, inviting_person_phone_code, inviting_person_phone

// 后端数据库字段
sur_name_of_contact_inviting_person, first_name_of_contact_inviting_person,
address_of_inviting_person, postal_code_of_inviting_person, email_inviting_person,
street_inviting_person, city_inviting_person, country_inviting_person,
inviting_person_telephone_isd_code, inviting_person_telephone_number
```
**状态**: ✅ 完全支持

### 3. 邀请企业信息 (Invitingenterprise) ✅ 完整支持
```javascript
// 前端字段
enterprise_name, enterprise_address, enterprise_postal_code, enterprise_email,
enterprise_street, enterprise_city, enterprise_country, enterprise_phone_code, enterprise_phone

// 后端数据库字段
name_of_enterprise, address_of_enterprise, postal_code_of_enterprise, email_enterprise,
street_enterprise, city_enterprise, country_enterprise,
enterprise_telephone_isd_code, enterprise_telephone_number
```
**状态**: ✅ 完全支持

### 4. 邀请机构信息 (InvitingOrganisation) ⚠️ 部分支持
```javascript
// 前端字段
organisation_name, organisation_address, organisation_postal_code, organisation_email,
organisation_street, organisation_city, organisation_country, organisation_phone_code, organisation_phone

// 后端数据库字段
name_of_organisation, address_of_organisation,
organisation_telephone_isd_code, organisation_telephone_number
```
**状态**: ⚠️ 缺少字段：
- `organisation_postal_code` ❌
- `organisation_email` ❌
- `organisation_street` ❌
- `organisation_city` ❌
- `organisation_country` ❌

### 5. 临时住宿信息 (Temporary accommodation) ✅ 完整支持
```javascript
// 前端字段
temp_accommodation_address, temp_accommodation_street, temp_accommodation_city,
temp_accommodation_postal_code, temp_accommodation_country, temp_accommodation_email,
temp_accommodation_phone_code, temp_accommodation_phone

// 后端处理方式
使用酒店字段存储：address_of_inviting_hotel, street_inviting_hotel, city_inviting_hotel,
postal_code_of_inviting_hotel, country_inviting_hotel, email_inviting_hotel,
temp_accommodation_telephone_isd_code, temp_accommodation_telephone_number
```
**状态**: ✅ 完全支持（复用酒店字段）

### 6. 瑞士居留规定信息 (RegulationsstayinSwitzerland) ✅ 完整支持
```javascript
// 前端字段
regulations_address, regulations_street, regulations_city, regulations_postal_code,
regulations_country, regulations_email, regulations_phone_code, regulations_phone

// 后端处理方式
使用酒店字段存储：address_of_inviting_hotel, street_inviting_hotel, city_inviting_hotel,
postal_code_of_inviting_hotel, country_inviting_hotel, email_inviting_hotel,
regulations_telephone_isd_code, regulations_telephone_number
```
**状态**: ✅ 完全支持（复用酒店字段）

## 🚨 发现的问题

### 1. 机构信息字段不完整
数据库表 `schengen_accommodation_info` 中机构相关字段不完整：

**缺失字段**：
- `postal_code_of_organisation` ❌
- `email_organisation` ❌
- `street_organisation` ❌
- `city_organisation` ❌
- `country_organisation` ❌

### 2. 默认值设置不完整
在GET接口的默认值设置中，缺少一些字段的默认值。

## 🔧 解决方案

### 方案1：数据库扩展（推荐）
为机构信息添加完整的字段：
```sql
ALTER TABLE schengen_accommodation_info 
ADD COLUMN postal_code_of_organisation VARCHAR(20),
ADD COLUMN email_organisation VARCHAR(255),
ADD COLUMN street_organisation VARCHAR(255),
ADD COLUMN city_organisation VARCHAR(100),
ADD COLUMN country_organisation VARCHAR(10);
```

### 方案2：字段复用（临时方案）
机构信息复用企业字段或酒店字段存储，在前端进行字段映射。

### 方案3：前端适配（不推荐）
前端隐藏机构信息中不支持的字段，只显示 `name` 和 `address`。

## 📊 当前后端支持状态总结

| 住宿类型 | 支持状态 | 缺失字段数 | 备注 |
|---------|---------|-----------|------|
| 酒店 | ✅ 完整 | 0 | 所有字段都支持 |
| 邀请人 | ✅ 完整 | 0 | 所有字段都支持 |
| 邀请企业 | ✅ 完整 | 0 | 所有字段都支持 |
| 邀请机构 | ⚠️ 部分 | 5 | 缺少详细地址字段 |
| 临时住宿 | ✅ 完整 | 0 | 复用酒店字段 |
| 瑞士居留规定 | ✅ 完整 | 0 | 复用酒店字段 |

## 🎯 建议的修复优先级

### 高优先级
1. **添加机构信息的缺失字段**到数据库表
2. **更新后端INSERT/UPDATE逻辑**处理新字段
3. **更新GET接口**返回新字段

### 中优先级
1. **完善默认值设置**确保所有字段都有默认值
2. **添加字段验证**确保数据完整性

### 低优先级
1. **优化字段映射逻辑**提高代码可维护性
2. **添加字段文档**便于后续维护

## 🧪 测试建议

### 测试场景1：机构信息完整性
```javascript
// 测试数据
{
  "inviting_party_type": "InvitingOrganisation",
  "organisation_name": "Test Organisation",
  "organisation_address": "123 Main St",
  "organisation_postal_code": "12345",
  "organisation_email": "<EMAIL>",
  "organisation_street": "Main Street",
  "organisation_city": "Test City",
  "organisation_country": "CHN",
  "organisation_phone_code": "+86",
  "organisation_phone": "13800138000"
}
```

### 测试场景2：字段映射验证
验证每种住宿类型的字段都能正确保存和读取。

### 测试场景3：默认值验证
验证新创建的表单中所有字段都有正确的默认值。

## ✅ 修复后的预期效果

1. **完整支持**所有6种住宿类型的所有字段
2. **数据一致性**前端字段与后端字段完全对应
3. **用户体验**用户可以填写完整的住宿信息
4. **数据完整性**所有住宿信息都能正确保存和读取

修复完成后，第五步住宿信息将支持所有前端字段，用户体验将大幅提升！
