# API 接口文档

## 1. 用户登录接口

### `/user/login`

**请求方式**: `POST`

**接口描述**: 用户登录验证接口，返回 JWT token 用于后续 API 调用的身份验证

**请求参数**:

```json
{
  "username": "string", // 用户名，必填
  "password": "string" // 密码，必填
}
```

**响应格式**:

- 成功响应 (HTTP 200):

```json
{
  "code": 1,
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...", // JWT token
  "username": "admin", // 用户名
  "permission": "admin" // 用户权限 (admin/kefu/vip)
}
```

- 失败响应 (HTTP 401):

```json
{
  "detail": "用户名或密码错误"
}
```

- 服务器错误 (HTTP 500):

```json
{
  "detail": "登录失败: 具体错误信息"
}
```

**使用说明**:

- 成功登录后，客户端需要保存返回的 token
- 后续 API 调用需要在请求头中添加: `Authorization: Bearer <token>`
- Token 有效期为 18 小时
- 用户权限等级: `admin` > `kefu` > `vip`

---

## 2. VFS 签证数据查询接口

### `/get_vfs_date`

**请求方式**: `POST`

**接口描述**: 获取 VFS 签证中心的可预约日期信息

**请求参数**:

```json
{} // 空对象，无需传递参数
```

**响应格式**:

- 成功响应 (HTTP 200):

```json
{
  "code": 1,
  "data": [
    {
      "country": "匈牙利", // 国家名称
      "missionCode": "hun", // 国家代码
      "visaCategoryCode": "S4", // 签证类别代码
      "centerCode": "HNCNCD", // 签证中心代码
      "center": "成都匈牙利签证申请中心", // 签证中心名称
      "type": " 科学研究", // 签证类型
      "date": "2025-07-24", // 可预约日期 (YYYY-MM-DD格式)
      "update": "2025-07-22 14:15:16" // 数据更新时间
    },
    {
      "country": "德国",
      "missionCode": "deu",
      "visaCategoryCode": "T1",
      "centerCode": "DESHA",
      "center": "上海德国签证申请中心",
      "type": " 旅游",
      "date": "暂无位置", // 无可预约日期时显示
      "update": "2025-07-22 14:10:32"
    }
  ]
}
```

- 失败响应 (HTTP 500):

```json
{
  "code": 0,
  "message": "获取数据失败: 具体错误信息"
}
```

**数据字段说明**:

- `country`: 签证国家中文名称
- `missionCode`: 国家的标准代码 (如 hun=匈牙利, deu=德国)
- `visaCategoryCode`: 签证类别代码 (T1=旅游, B1=商务, S4=科研等)
- `centerCode`: VFS 签证中心的唯一标识码
- `center`: 签证申请中心的完整名称
- `type`: 签证类型的中文描述
- `date`: 最近可预约日期，格式为"YYYY-MM-DD"，无位置时为"暂无位置"
- `update`: 数据最后更新时间，格式为"YYYY-MM-DD HH:MM:SS"

**使用说明**:

- 该接口返回实时的签证预约数据
- 数据每隔一定时间自动更新
- `date`字段为"暂无位置"表示该签证类型当前无可预约日期
- 可根据`missionCode`和`centerCode`进行数据筛选和分组

---

## 错误码说明

| HTTP 状态码 | 说明                       |
| ----------- | -------------------------- |
| 200         | 请求成功                   |
| 401         | 未授权，token 无效或已过期 |
| 403         | 权限不足                   |
| 404         | 资源不存在                 |
| 422         | 请求参数验证失败           |
| 500         | 服务器内部错误             |

## 通用响应格式

所有接口的响应都遵循以下格式：

**成功响应**:

```json
{
  "code": 1,
  "data": {}, // 响应数据
  "message": "" // 可选的成功消息
}
```

**错误响应**:

```json
{
  "code": 0,
  "message": "错误描述",
  "detail": "详细错误信息" // 可选
}
```

## 认证说明

除了登录接口外，所有 API 调用都需要在请求头中包含 JWT token：

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

Token 过期或无效时，接口会返回 401 状态码，客户端需要重新登录获取新的 token。
