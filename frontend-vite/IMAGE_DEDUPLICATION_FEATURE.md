# 图片去重功能说明

## 功能概述

在 `NewOrder.vue` 组件中，为护照照片和头像照片上传添加了去重校验功能，确保用户不会上传相同的图片作为护照照片和头像照片。

## 实现原理

### 1. 哈希值计算

使用 Web Crypto API 的 SHA-256 算法计算文件的哈希值：

```javascript
const calculateFileHash = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target.result;
        const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        resolve(hashHex);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
};
```

### 2. 去重检查逻辑

```javascript
const checkImageDuplication = async (file, clientIndex, uploadType) => {
  try {
    const fileHash = await calculateFileHash(file);
    const client = clients.value[clientIndex];
    
    // 检查当前客户的护照照片和头像照片是否重复
    if (uploadType === 'passport' && client.avatar_image) {
      // 如果要上传护照照片，检查是否与已有头像重复
      const existingAvatarHash = client.avatar_image_hash;
      if (existingAvatarHash && existingAvatarHash === fileHash) {
        return {
          isDuplicate: true,
          message: '护照照片不能与头像照片相同，请选择不同的图片'
        };
      }
    } else if (uploadType === 'avatar' && client.passport_image) {
      // 如果要上传头像照片，检查是否与已有护照照片重复
      const existingPassportHash = client.passport_image_hash;
      if (existingPassportHash && existingPassportHash === fileHash) {
        return {
          isDuplicate: true,
          message: '头像照片不能与护照照片相同，请选择不同的图片'
        };
      }
    }
    
    return {
      isDuplicate: false,
      hash: fileHash
    };
  } catch (error) {
    console.error('图片去重检查失败:', error);
    // 如果去重检查失败，允许上传但给出警告
    ElMessage.warning('图片去重检查失败，请确保护照照片和头像照片不是同一张');
    return {
      isDuplicate: false,
      hash: null
    };
  }
};
```

## 数据结构更新

### 客户数据结构

为每个客户添加了哈希值字段：

```javascript
const clients = ref([
  {
    name: '',
    surname_pinyin: '',
    firstname_pinyin: '',
    gender: '',
    dob: '',
    passport: '',
    passport_expire: '',
    passport_image: '',
    passport_image_hash: '', // 护照照片哈希值
    avatar_image: '',
    avatar_image_hash: '', // 头像照片哈希值
    nationality: '',
    passport_date: '',
    sign_location: '',
    bornplace: '',
    marital_status: '',
  },
]);
```

## 集成点

### 1. 护照上传 (`customUpload`)

在护照上传函数开始处添加去重检查：

```javascript
const customUpload = async (options, index) => {
  uploading.value = true;
  try {
    let file = options.file;

    // 图片去重检查
    const duplicationCheck = await checkImageDuplication(file, index, 'passport');
    if (duplicationCheck.isDuplicate) {
      ElMessage.error(duplicationCheck.message);
      uploading.value = false;
      return;
    }

    // ... 继续原有的上传逻辑
    
    // 上传成功后保存哈希值
    if (res.code === 1) {
      if (res.filename) {
        clients.value[index].passport_image = res.filename;
        // 保存护照照片的哈希值用于去重检查
        if (duplicationCheck.hash) {
          clients.value[index].passport_image_hash = duplicationCheck.hash;
        }
      }
    }
  } catch (error) {
    // 错误处理
  } finally {
    uploading.value = false;
  }
};
```

### 2. 头像上传 (`handleAvatarUpload`)

在头像上传函数开始处添加去重检查：

```javascript
const handleAvatarUpload = async (options, index) => {
  uploading.value = true;
  try {
    let file = options.file;

    // 图片去重检查
    const duplicationCheck = await checkImageDuplication(file, index, 'avatar');
    if (duplicationCheck.isDuplicate) {
      ElMessage.error(duplicationCheck.message);
      uploading.value = false;
      return;
    }

    // ... 继续原有的上传逻辑
    
    // 上传成功后保存哈希值
    if (res.code === 1 && res.filename) {
      clients.value[index].avatar_image = res.filename;
      // 保存头像照片的哈希值用于去重检查
      if (duplicationCheck.hash) {
        clients.value[index].avatar_image_hash = duplicationCheck.hash;
      }
    }
  } catch (error) {
    // 错误处理
  } finally {
    uploading.value = false;
  }
};
```

### 3. 重新上传功能

更新重新上传函数，确保清除哈希值：

```javascript
// 重新上传护照
const reuploadPassport = index => {
  clients.value[index].passport_image = '';
  clients.value[index].passport_image_hash = ''; // 清除哈希值
  ElMessage.info('请重新上传护照照片');
};

// 重新上传头像
const reuploadAvatar = index => {
  clients.value[index].avatar_image = '';
  clients.value[index].avatar_image_hash = ''; // 清除哈希值
  ElMessage.info('请重新上传头像照片');
};
```

## 支持的上传方式

去重检查支持以下所有上传方式：

1. **点击上传**: 通过 `el-upload` 组件的点击上传
2. **拖拽上传**: 通过拖拽文件到上传区域
3. **重新上传**: 通过重新上传按钮

## 用户体验

### 成功场景
- 用户上传两张不同的图片 → 正常上传，显示成功消息
- 用户重新上传不同的图片 → 正常上传，更新哈希值

### 失败场景
- 用户尝试上传相同的图片 → 显示错误消息，阻止上传
- 错误消息明确指出问题："护照照片不能与头像照片相同，请选择不同的图片"

### 容错处理
- 如果哈希计算失败 → 显示警告但允许上传
- 确保系统的可用性不受去重功能影响

## 技术特点

1. **高精度**: 使用 SHA-256 算法，即使文件名不同但内容相同也能检测出来
2. **高性能**: 哈希计算在客户端进行，不增加服务器负担
3. **兼容性**: 使用标准的 Web Crypto API，现代浏览器都支持
4. **用户友好**: 实时检查，立即反馈，避免用户等待上传完成才发现问题

## 测试

提供了独立的测试页面 `image-deduplication-test.html` 用于验证去重功能：

1. 上传两张不同的图片 - 应该显示成功
2. 上传两张相同的图片 - 应该显示重复警告
3. 查看哈希值对比 - 相同图片的哈希值应该相同

## 注意事项

1. **浏览器兼容性**: 需要支持 Web Crypto API 的现代浏览器
2. **文件大小**: 大文件的哈希计算可能需要一些时间
3. **内存使用**: 哈希计算需要将整个文件读入内存
4. **安全性**: SHA-256 提供了足够的安全性，碰撞概率极低

## 扩展功能

可以考虑的扩展功能：

1. **跨客户去重**: 检查不同客户之间是否使用了相同的图片
2. **历史去重**: 与历史订单中的图片进行去重检查
3. **相似度检测**: 使用图像相似度算法检测相似但不完全相同的图片
4. **批量检查**: 为管理员提供批量检查重复图片的功能
