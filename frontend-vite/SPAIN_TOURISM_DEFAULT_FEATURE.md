# 西班牙签证默认Tourism功能说明

## 功能概述

在 `NewOrder.vue` 组件中，为西班牙签证选择添加了默认Tourism类型的功能。当用户选择西班牙签证时，系统会自动确保选择Tourism作为签证类型，提升用户体验和操作一致性。

## 实现原理

### 1. 签证选择数据结构

签证选择使用 Element Plus 的 Cascader 组件，数据结构为四级：

```
国家 (spain) -> 领馆 (SHANGHAI/BEIJING/...) -> 签证类型 (schengen) -> 具体类型 (tourism/business)
```

实际选择的值格式：
```javascript
["spain", "SHANGHAI", "schengen", "tourism"]
```

### 2. 自动选择Tourism逻辑

```javascript
const autoSelectSpainTourism = (value) => {
  if (!Array.isArray(value) || value.length === 0) {
    return value;
  }

  const processedValue = value.map(item => {
    if (!Array.isArray(item) || item.length < 2) {
      return item;
    }

    // 检查是否是西班牙签证
    const countryCode = item[0];
    
    if (countryCode && countryCode.toLowerCase().includes('spain')) {
      console.log('检测到西班牙签证选择:', item);
      
      // 如果选择了西班牙但没有选择到Tourism，自动补全Tourism
      if (item.length >= 3 && !item.includes('Tourism')) {
        // 检查是否已经有完整的4级选择
        if (item.length === 4) {
          // 如果第4级不是Tourism，替换为Tourism
          const autoCompleted = [item[0], item[1], item[2], 'Tourism'];
          console.log('西班牙签证自动选择Tourism:', autoCompleted);
          ElMessage.success('已自动选择旅游签证类型');
          return autoCompleted;
        } else if (item.length === 3) {
          // 如果只有3级，添加Tourism作为第4级
          const autoCompleted = [...item, 'Tourism'];
          console.log('西班牙签证自动添加Tourism:', autoCompleted);
          ElMessage.success('已自动选择旅游签证类型');
          return autoCompleted;
        }
      }
    }
    
    return item;
  });

  return processedValue;
};
```

### 3. 集成到变化处理

```javascript
const handleVisaTypeChange = value => {
  console.log('=== Cascader Change Event ===');
  console.log('Cascader 原始值:', JSON.stringify(value, null, 2));

  // 处理西班牙签证默认Tourism
  const processedValue = autoSelectSpainTourism(value);
  
  // 赋值处理后的值
  visaType.value = processedValue;

  console.log('赋值后 visaType.value:', JSON.stringify(visaType.value, null, 2));
  console.log('=== End Cascader Change Event ===');
};
```

## 触发条件

### 1. 替换其他类型为Tourism

**用户操作**: 选择 "西班牙" -> "上海" -> "申根签证" -> "商务签证"
**系统行为**: 自动替换为 ["spain", "SHANGHAI", "schengen", "Tourism"]
**用户提示**: "已自动选择旅游签证类型"

### 2. 添加Tourism作为第4级

**用户操作**: 选择 "西班牙" -> "北京" -> "申根签证"
**系统行为**: 自动补全为 ["spain", "BEIJING", "schengen", "Tourism"]
**用户提示**: "已自动选择旅游签证类型"

### 3. 不触发的情况

- **非西班牙签证**: 其他国家的签证选择不受影响
- **已选择Tourism**: 如果用户已经选择了Tourism，不进行修改
- **不完整选择**: 如果选择少于3级，不进行自动补全

## 用户体验

### 优势

1. **统一标准**: 确保西班牙签证统一使用Tourism类型
2. **减少错误**: 避免用户选择错误的签证类型
3. **提升效率**: 自动处理常见选择，减少用户操作
4. **智能提示**: 清晰的消息提示用户系统的自动操作

### 用户反馈

- **自动选择提示**: "已自动选择旅游签证类型"
- **控制台日志**: 详细的处理过程记录
- **非破坏性**: 不影响其他国家的签证选择

## 兼容性

### 多选支持

功能支持 Cascader 的多选模式，可以同时处理多个签证选择：

```javascript
// 输入示例
[
  ["spain", "SHANGHAI", "schengen", "business"], // 西班牙商务 - 会被替换为Tourism
  ["italy", "BEIJING", "schengen", "business"],  // 意大利商务 - 不受影响
  ["spain", "GUANGZHOU", "schengen"]             // 西班牙3级 - 会添加Tourism
]

// 输出结果
[
  ["spain", "SHANGHAI", "schengen", "Tourism"],  // 替换为Tourism
  ["italy", "BEIJING", "schengen", "business"],  // 保持不变
  ["spain", "GUANGZHOU", "schengen", "Tourism"]  // 添加Tourism
]
```

### 数据格式兼容

- 支持标准的 Cascader 数据格式
- 兼容现有的签证选择逻辑
- 不影响其他国家的签证选择

## 测试

### 测试用例

1. **替换其他类型**
   - 输入: `[["spain", "SHANGHAI", "schengen", "business"]]`
   - 预期: `[["spain", "SHANGHAI", "schengen", "Tourism"]]`

2. **添加Tourism**
   - 输入: `[["spain", "BEIJING", "schengen"]]`
   - 预期: `[["spain", "BEIJING", "schengen", "Tourism"]]`

3. **非西班牙签证**
   - 输入: `[["italy", "SHANGHAI", "schengen", "business"]]`
   - 预期: `[["italy", "SHANGHAI", "schengen", "business"]]` (不变)

4. **已选择Tourism**
   - 输入: `[["spain", "SHANGHAI", "schengen", "Tourism"]]`
   - 预期: `[["spain", "SHANGHAI", "schengen", "Tourism"]]` (不变)

5. **混合选择**
   - 输入: `[["spain", "SHANGHAI", "schengen", "business"], ["italy", "BEIJING", "schengen", "business"]]`
   - 预期: `[["spain", "SHANGHAI", "schengen", "Tourism"], ["italy", "BEIJING", "schengen", "business"]]`

### 测试页面

提供了独立的测试页面 `spain-tourism-default-test.html` 用于验证Tourism默认选择功能。

## 配置说明

### Cascader 配置

```javascript
const cprops = {
  multiple: true,    // 支持多选
  value: 'code',     // 使用 code 作为值
  label: 'name',     // 使用 name 作为显示标签
};
```

### 签证选项结构

```javascript
{
  code: 'spain',
  name: '西班牙',
  children: [
    {
      code: 'SHANGHAI',
      name: '上海',
      children: [
        {
          code: 'schengen',
          name: '申根签证',
          children: [
            { code: 'Tourism', name: '旅游签证' },
            { code: 'business', name: '商务签证' },
          ],
        },
      ],
    },
  ],
}
```

## 业务逻辑

### 处理流程

1. **检测西班牙签证**: 通过国家代码识别西班牙签证选择
2. **检查Tourism状态**: 判断是否已经选择了Tourism
3. **自动处理**: 根据选择层级进行相应的自动处理
4. **用户反馈**: 提供清晰的操作反馈

### 决策逻辑

```
if (是西班牙签证) {
  if (选择了4级 && 第4级不是Tourism) {
    替换第4级为Tourism
  } else if (选择了3级 && 没有Tourism) {
    添加Tourism作为第4级
  } else if (已经有Tourism) {
    保持不变
  }
} else {
  保持不变（非西班牙签证）
}
```

## 注意事项

1. **大小写敏感**: 国家代码检查使用 `toLowerCase()` 确保兼容性
2. **数组长度检查**: 确保选择数组有足够的元素再进行处理
3. **非破坏性**: 不影响其他国家的签证选择逻辑
4. **日志记录**: 详细的控制台日志便于调试和监控
5. **Tourism标识**: 使用 'Tourism' 作为标准的旅游签证标识

## 扩展功能

可以考虑的扩展功能：

1. **其他国家默认**: 为其他国家设置默认的签证类型
2. **用户偏好**: 允许用户自定义默认签证类型
3. **业务规则**: 根据不同的业务场景设置不同的默认值
4. **配置化**: 将默认规则配置化，便于维护和修改
