-- =====================================================
-- PostgreSQL 数据库脚本：为机构信息添加缺失字段
-- 解决第五步住宿信息中机构类型字段不完整的问题
-- =====================================================

-- 开始事务（确保原子性）
BEGIN;

-- 为 schengen_accommodation_info 表添加机构信息的缺失字段
-- 这些字段用于存储邀请机构的详细信息

-- 1. 添加机构邮政编码字段
ALTER TABLE schengen_accommodation_info 
ADD COLUMN postal_code_of_organisation VARCHAR(20);

-- 2. 添加机构邮箱字段
ALTER TABLE schengen_accommodation_info 
ADD COLUMN email_organisation VARCHAR(255);

-- 3. 添加机构街道地址字段
ALTER TABLE schengen_accommodation_info 
ADD COLUMN street_organisation VARCHAR(255);

-- 4. 添加机构城市字段
ALTER TABLE schengen_accommodation_info 
ADD COLUMN city_organisation VARCHAR(100);

-- 5. 添加机构国家字段
ALTER TABLE schengen_accommodation_info 
ADD COLUMN country_organisation VARCHAR(10);

-- 提交事务
COMMIT;

\echo '=== 机构信息字段添加完成 ==='

-- =====================================================
-- 验证脚本：检查新字段是否已添加
-- =====================================================

-- 查看表结构，确认新字段已添加
\d schengen_accommodation_info

-- 查询新添加字段的信息
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'schengen_accommodation_info' 
AND column_name IN (
    'postal_code_of_organisation',
    'email_organisation', 
    'street_organisation',
    'city_organisation',
    'country_organisation'
)
ORDER BY column_name;

-- =====================================================
-- 测试脚本：验证可以插入和查询新字段
-- =====================================================

-- 测试插入包含新字段的数据（可选，注释掉以避免测试数据）
/*
-- 测试插入
INSERT INTO schengen_accommodation_info (
    application_id,
    inviting_party_id,
    name_of_organisation,
    address_of_organisation,
    postal_code_of_organisation,
    email_organisation,
    street_organisation,
    city_organisation,
    country_organisation,
    organisation_telephone_isd_code,
    organisation_telephone_number
) VALUES (
    'TEST_ORG_FIELDS_999999',
    5,  -- InvitingOrganisation
    'Test Organisation',
    'Main Address',
    '12345',
    '<EMAIL>',
    'Main Street',
    'Test City',
    'CHN',
    '+86',
    '13800138000'
);

-- 验证插入成功
SELECT 
    application_id,
    name_of_organisation,
    postal_code_of_organisation,
    email_organisation,
    street_organisation,
    city_organisation,
    country_organisation
FROM schengen_accommodation_info 
WHERE application_id = 'TEST_ORG_FIELDS_999999';

-- 清理测试数据
DELETE FROM schengen_accommodation_info 
WHERE application_id = 'TEST_ORG_FIELDS_999999';
*/

-- =====================================================
-- 回滚脚本（如果需要撤销更改）
-- =====================================================
/*
-- 注意：回滚将删除新添加的字段和其中的所有数据
-- 请谨慎使用

BEGIN;

-- 删除新添加的机构信息字段
ALTER TABLE schengen_accommodation_info 
DROP COLUMN IF EXISTS postal_code_of_organisation;

ALTER TABLE schengen_accommodation_info 
DROP COLUMN IF EXISTS email_organisation;

ALTER TABLE schengen_accommodation_info 
DROP COLUMN IF EXISTS street_organisation;

ALTER TABLE schengen_accommodation_info 
DROP COLUMN IF EXISTS city_organisation;

ALTER TABLE schengen_accommodation_info 
DROP COLUMN IF EXISTS country_organisation;

COMMIT;

\echo 'Rollback completed - Organisation fields removed'
*/

-- =====================================================
-- 字段说明文档
-- =====================================================

/*
## 新增字段说明

### postal_code_of_organisation
- **类型**: VARCHAR(20)
- **用途**: 存储邀请机构的邮政编码
- **对应前端字段**: organisation_postal_code
- **示例**: "100000", "12345"

### email_organisation
- **类型**: VARCHAR(255)
- **用途**: 存储邀请机构的邮箱地址
- **对应前端字段**: organisation_email
- **示例**: "<EMAIL>"

### street_organisation
- **类型**: VARCHAR(255)
- **用途**: 存储邀请机构的街道地址
- **对应前端字段**: organisation_street
- **示例**: "123 Main Street", "中山路123号"

### city_organisation
- **类型**: VARCHAR(100)
- **用途**: 存储邀请机构的城市
- **对应前端字段**: organisation_city
- **示例**: "Beijing", "Shanghai", "New York"

### country_organisation
- **类型**: VARCHAR(10)
- **用途**: 存储邀请机构的国家代码
- **对应前端字段**: organisation_country
- **示例**: "CHN", "USA", "GBR"

## 字段映射关系

| 前端字段 | 后端数据库字段 | 数据类型 |
|---------|---------------|----------|
| organisation_name | name_of_organisation | VARCHAR |
| organisation_address | address_of_organisation | VARCHAR |
| organisation_postal_code | postal_code_of_organisation | VARCHAR(20) |
| organisation_email | email_organisation | VARCHAR(255) |
| organisation_street | street_organisation | VARCHAR(255) |
| organisation_city | city_organisation | VARCHAR(100) |
| organisation_country | country_organisation | VARCHAR(10) |
| organisation_phone_code | organisation_telephone_isd_code | VARCHAR |
| organisation_phone | organisation_telephone_number | VARCHAR |

## 影响评估

### 正面影响
- ✅ 完整支持机构信息的所有前端字段
- ✅ 提升用户体验，用户可以填写完整的机构信息
- ✅ 数据结构更加清晰和完整
- ✅ 便于后续功能扩展和维护

### 注意事项
- 🔄 需要更新后端代码以处理新字段
- 🔄 需要更新INSERT/UPDATE/SELECT语句
- 🔄 现有数据中新字段将为NULL，需要在代码中处理

### 兼容性
- ✅ 向后兼容：现有功能不受影响
- ✅ 新字段允许NULL值，不会破坏现有数据
- ✅ 现有的机构记录仍然可以正常读取

## 执行步骤

1. **备份数据库**（推荐）
2. **在测试环境先执行此脚本**
3. **验证字段添加成功**
4. **更新后端代码**
5. **测试完整功能**
6. **在生产环境执行**

## 验证方法

1. 检查表结构确认新字段已添加
2. 测试插入包含新字段的数据
3. 验证前端可以正确保存和读取机构信息
4. 确认所有住宿类型都能正常工作
*/
