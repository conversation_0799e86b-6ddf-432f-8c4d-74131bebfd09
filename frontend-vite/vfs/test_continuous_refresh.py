#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持续刷新URN功能
"""

import json
import time
import threading
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_continuous_refresh_logic():
    """测试持续刷新逻辑"""
    print("=" * 60)
    print("测试持续刷新URN逻辑")
    print("=" * 60)
    
    # 模拟订单数据
    mock_orders = [
        {
            "order_id": "ORDER001",
            "visa_info": {
                "mission_code": "ita",
                "center_code": "SHI",
                "visa_code": "SSFTME"
            },
            "clients": [{"name": "张三", "passport": "E12345678"}],
            "status": "pending"
        },
        {
            "order_id": "ORDER002", 
            "visa_info": {
                "mission_code": "fra",
                "center_code": "FRBE",
                "visa_code": "TOU"
            },
            "clients": [{"name": "李四", "passport": "E87654321"}],
            "status": "pending"
        },
        {
            "order_id": "ORDER003",
            "visa_info": {
                "mission_code": "ita",
                "center_code": "SHI", 
                "visa_code": "SSFTME"
            },
            "clients": [{"name": "王五", "passport": "E11111111"}],
            "status": "pending",
            "urn": "URN123456",  # 已有URN
            "updateUrnTime": int(time.time()) - 100  # 最近更新
        }
    ]
    
    def mock_get_pending_orders():
        """模拟获取待处理订单"""
        # 过滤出真正需要处理的订单
        pending = []
        current_time = int(time.time())
        
        for order in mock_orders:
            # 检查URN是否存在或过期
            if (not order.get('urn') or
                not order.get('updateUrnTime') or
                current_time - order.get('updateUrnTime') > 10000):
                pending.append(order)
        
        return pending
    
    def mock_refresh_urn_for_mission(mission_code, center_code, visa_code):
        """模拟刷新指定签证类型的URN"""
        print(f"  🔄 处理签证类型: {mission_code}/{center_code}/{visa_code}")
        
        # 模拟处理结果
        import random
        success = random.choice([True, False])
        
        if success:
            print(f"    ✅ 成功处理")
            return True
        else:
            print(f"    ❌ 处理失败")
            return False
    
    def mock_continuous_refresh():
        """模拟持续刷新逻辑"""
        round_count = 0
        max_rounds = 3  # 测试3轮
        
        while round_count < max_rounds:
            round_count += 1
            print(f"\n🔄 第 {round_count} 轮刷新开始...")
            
            # 获取待处理订单
            pending_orders = mock_get_pending_orders()
            
            if not pending_orders:
                print("  📋 没有待处理的订单，等待5分钟后重试")
                print("  ⏰ (测试模式：跳过等待)")
                time.sleep(1)  # 测试时缩短等待时间
                continue
            
            # 按签证类型分组
            orders_by_visa = {}
            for order in pending_orders:
                visa_info = order.get('visa_info', {})
                mission_code = visa_info.get('mission_code')
                center_code = visa_info.get('center_code')
                visa_code = visa_info.get('visa_code')
                
                if mission_code and center_code and visa_code:
                    key = f"{mission_code}--{center_code}--{visa_code}"
                    if key not in orders_by_visa:
                        orders_by_visa[key] = []
                    orders_by_visa[key].append(order)
            
            print(f"  📊 找到 {len(orders_by_visa)} 种签证类型需要处理")
            
            # 处理每种签证类型
            processed_any = False
            for visa_key, orders in orders_by_visa.items():
                mission_code, center_code, visa_code = visa_key.split('--')
                print(f"  📝 签证类型: {mission_code} {center_code} {visa_code}, 订单数: {len(orders)}")
                
                result = mock_refresh_urn_for_mission(mission_code, center_code, visa_code)
                if result:
                    processed_any = True
                
                time.sleep(0.5)  # 模拟处理间隔
            
            if not processed_any:
                print("  ⚠️  本轮没有成功处理任何订单，等待5分钟后重试")
                print("  ⏰ (测试模式：跳过等待)")
                time.sleep(1)
            else:
                print("  ✅ 本轮处理完成，等待30秒后开始下一轮")
                print("  ⏰ (测试模式：缩短等待)")
                time.sleep(1)
        
        print(f"\n🏁 测试完成，共进行了 {round_count} 轮刷新")
    
    # 执行测试
    print("📋 模拟订单数据:")
    for i, order in enumerate(mock_orders, 1):
        visa_info = order.get('visa_info', {})
        urn_status = "有URN" if order.get('urn') else "无URN"
        print(f"  {i}. {order['order_id']} - {visa_info['mission_code']}/{visa_info['center_code']}/{visa_info['visa_code']} - {urn_status}")
    
    print(f"\n📊 待处理订单:")
    pending = mock_get_pending_orders()
    for order in pending:
        visa_info = order.get('visa_info', {})
        print(f"  - {order['order_id']} - {visa_info['mission_code']}/{visa_info['center_code']}/{visa_info['visa_code']}")
    
    print(f"\n🚀 开始持续刷新测试...")
    mock_continuous_refresh()

def test_timing_logic():
    """测试时间逻辑"""
    print("\n" + "=" * 60)
    print("测试时间逻辑")
    print("=" * 60)
    
    current_time = int(time.time())
    
    # 测试用例
    test_cases = [
        {
            "name": "无URN的订单",
            "urn": None,
            "updateUrnTime": None,
            "should_process": True
        },
        {
            "name": "URN刚更新的订单",
            "urn": "URN123456",
            "updateUrnTime": current_time - 100,  # 100秒前
            "should_process": False
        },
        {
            "name": "URN过期的订单",
            "urn": "URN123456", 
            "updateUrnTime": current_time - 10001,  # 10001秒前，超过10000秒
            "should_process": True
        }
    ]
    
    print("⏰ URN有效期测试 (10000秒):")
    for case in test_cases:
        # 模拟判断逻辑
        needs_refresh = False
        if (not case["urn"] or
            not case["updateUrnTime"] or
            current_time - case["updateUrnTime"] > 10000):
            needs_refresh = True
        
        status = "✅" if needs_refresh == case["should_process"] else "❌"
        action = "需要处理" if needs_refresh else "跳过"
        print(f"  {status} {case['name']}: {action}")

def test_wait_strategy():
    """测试等待策略"""
    print("\n" + "=" * 60)
    print("测试等待策略")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "没有待处理订单",
            "has_orders": False,
            "processed_any": False,
            "wait_time": 300,  # 5分钟
            "description": "等待5分钟后重试"
        },
        {
            "name": "有订单但处理失败",
            "has_orders": True,
            "processed_any": False,
            "wait_time": 300,  # 5分钟
            "description": "等待5分钟后重试"
        },
        {
            "name": "有订单且处理成功",
            "has_orders": True,
            "processed_any": True,
            "wait_time": 30,   # 30秒
            "description": "等待30秒后开始下一轮"
        }
    ]
    
    print("⏱️  等待策略测试:")
    for scenario in scenarios:
        print(f"  📋 场景: {scenario['name']}")
        print(f"    - 有订单: {scenario['has_orders']}")
        print(f"    - 处理成功: {scenario['processed_any']}")
        print(f"    - 等待时间: {scenario['wait_time']}秒")
        print(f"    - 说明: {scenario['description']}")
        print()

if __name__ == "__main__":
    test_continuous_refresh_logic()
    test_timing_logic()
    test_wait_strategy()
