# -*- coding: utf-8 -*-
import requests
import re
import random
import time
import json
from datetime import datetime, timedelta
from curl_cffi import requests
from RedisClientAWS import RedisClient
import threading
from pypinyin import pinyin, Style
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


delegate = json.loads(redis_client.get("login_proxy"))

scan_data = json.loads(redis_client.get("scanData"))
scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']


def refresh_scan_data():
    while True:
        time.sleep(10)
        global scan_data
        global scan_country
        scan_data = json.loads(redis_client.get("scanData"))
        scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']
        global delegate
        delegate = json.loads(redis_client.get("login_proxy"))


threading.Thread(target=refresh_scan_data).start()


# 注册
# 扩展的常见中国姓和名
first_names = [
    "伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟",
    "涛", "明", "超", "秀英", "平", "刚", "桂英", "鑫", "涛", "建国", "志强", "敏", "小红",
    "婷婷", "玉兰", "梅", "明华", "英杰", "俊杰", "嘉豪", "嘉怡", "子豪", "子轩", "子涵",
    "宇轩", "宇航", "宇翔", "子琪", "雅静", "雅婷", "雅涵", "文婷", "文静", "文豪", "文轩", "秋白", "南风", "醉山",
    "初彤", "凝海", "紫文",
    "凌晴", "香卉", "雅琴",
    "傲安", "傲之", "初蝶",
    "寻桃", "代芹", "诗霜",
    "春柏", "绿夏", "碧灵",
    "诗柳", "夏柳", "采白",
    "慕梅", "乐安", "冬菱",
    "紫安", "宛凝", "雨雪",
    "易真", "安荷", "静竹",
    "飞雪", "雪兰", "雅霜",
    "从蓉", "冷雪", "靖巧",
    "翠丝", "觅翠", "凡白",
    "乐蓉", "迎波", "丹烟",
    "梦旋", "书双", "念桃",
    "夜天", "海桃", "青香",
    "恨风", "安筠", "觅柔",
    "初南", "秋蝶", "千易",
    "安露", "诗蕊", "山雁",
    "友菱", "香露", "晓兰",
    "涵瑶", "秋柔", "思菱",
    "醉柳", "以寒", "迎夏",
    "向雪", "香莲", "以丹",
    "依凝", "如柏", "雁菱",
    "凝竹", "宛白", "初柔",
    "南蕾", "书萱", "梦槐",
    "香芹", "南琴", "绿海",
    "沛儿", "晓瑶", "听春",
    "易巧", "念云", "晓灵",
    "静枫", "夏蓉", "如南",
    "幼丝", "秋白", "冰安",
    "凝蝶", "紫雪", "念双",
    "念真", "曼寒", "凡霜",
    "白卉", "语山", "冷珍",
    "秋翠", "夏柳", "如之",
    "忆南", "书易", "翠桃",
    "寄瑶", "如曼", "问柳",
    "香梅", "幻桃", "又菡",
    "春绿", "醉蝶", "亦绿",
    "诗珊", "听芹", "新之",
    "博瀚", "博超", "才哲",
    "才俊", "成和", "成弘",
    "昊苍", "昊昊", "昊空",
    "昊乾", "昊穹", "昊然",
    "昊然", "昊天", "昊焱",
    "昊英", "浩波", "浩博",
    "浩初", "浩大", "浩宕",
    "浩荡", "浩歌", "浩广",
    "浩涆", "浩瀚", "浩浩",
    "浩慨", "浩旷", "浩阔",
    "浩漫", "浩淼", "浩渺",
    "浩邈", "浩气", "浩然",
    "浩穰", "浩壤", "浩思",
    "浩言", "皓轩", "和蔼",
    "和安", "和昶", "翔东",
    "昊伟", "楚桥", "智霖",
    "浩杰", "炎承", "思哲",
    "璟新", "楚怀", "继智",
    "昭旺", "俊泽", "子中",
    "羽睿", "嘉雷", "鸿翔",
    "明轩", "棋齐", "轶乐",
    "昭易", "臻翔", "泽鑫",
    "芮军", "浩奕", "宏明",
    "忠贤", "锦辉", "元毅",
    "霈胜", "宇峻", "子博",
    "语霖", "胜佑", "俊涛",
    "浩淇", "乐航", "泽楷",
    "嘉宁", "敬宣", "韦宁",
    "建新", "宇怀", "皓玄",
    "冠捷", "俊铭", "一鸣",
    "堂耀", "轩凝", "舰曦",
    "跃鑫", "梓杰", "筱宇",
    "弘涛", "羿天", "广嘉",
    "陆铭", "志卿", "连彬",
    "景智", "孟昕", "羿然",
    "文渊", "羿楦", "晗昱",
    "晗日", "涵畅", "涵涤",
    "涵亮", "涵忍", "涵容",
    "闹闹", "皮皮", "来福",
    "拉拉", "大奔", "奔奔",
    "麻团", "果果", "美美",
    "格格", "坦克", "崽崽",
    "东东", "琪琪", "仔仔",
    "棉花糖", "蹦蹦", "特工",
    "甜甜", "小丸子", "彬彬",
    "洋洋", "肥婆", "强盗",
    "水货", "狐狸", "安妮",
    "拉登", "雪郎", "仔仔",
    "牛牛", "豆豆", "小小",
    "哈里", "洛奇", "小白",
    "毛毛", "豆豆", "波比",
    "波波", "甜甜", "辣辣",
    "虫虫", "娃娃", "圆圆",
    "滚滚", "扁扁", "跳跳",
    "绿绿", "酷儿", "乐乐",
    "舟舟", "小帅", "顺子",
    "塞虎", "黑虎", "虎子",
    "旺财", "得喜", "来福",
    "富贵儿", "满仓", "阿贵",
    "大奔", "宝来", "丁丁",
    "班班", "爱米", "贝尔",
    "贝宝", "大壮", "枫枫",
    "大赖", "飞飞"
]
last_names = [
    "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈",
    "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许",
    "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏",
    "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦", "章",
    "云", "苏", "潘", "葛", "奚", "范", "彭", "郎", "鲁", "韦",
    "昌", "马", "苗", "凤", "花", "方", "俞", "任", "袁", "柳",
    "酆", "鲍", "史", "唐", "费", "廉", "岑", "薛", "雷", "贺",
    "倪", "汤", "滕", "殷", "罗", "毕", "郝", "邬", "安", "常",
    "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍", "余",
    "元", "卜", "顾", "孟", "平", "黄", "和", "穆", "萧", "尹"
]

english_first_names = [
    "James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda", "William",
    "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica", "Thomas", "Sarah",
    "Charles", "Karen", "Christopher", "Nancy", "Daniel", "Lisa", "Matthew", "Betty", "Anthony",
    "Margaret", "Donald", "Sandra", "Mark", "Ashley", "Paul", "Kimberly", "Steven", "Emily",
    "George", "Donna", "Kenneth", "Michelle", "Andrew", "Dorothy", "Joshua", "Carol", "Kevin",
    "Amanda", "Brian", "Melissa", "Edward", "Deborah"
]


def to_pinyin(hanzi):
    # 将汉字转换为拼音
    return ''.join([item[0] for item in pinyin(hanzi, style=Style.NORMAL)])


def generate_random_10_digits():
    # 生成 10 位随机数字
    digits = "".join([str(random.randint(0, 9)) for _ in range(9)])
    return digits


def generate_email():
    email_formats = []

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{first_name_pinyin}.{last_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        year = random.randint(1970, 2010)
        month = str(random.randint(1, 12)).zfill(2)
        day = str(random.randint(1, 28)).zfill(2)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}{month}{day}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{last_name_pinyin}_{first_name_pinyin}")

    domains = ['nextdomain.space']
    return f"{random.choice(email_formats)}@{random.choice(domains)}"


def get_phone_number():
    url = "http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        print(response.text)
        data = response.json()
        if data.get("phone") != None:
            return data.get("phone")
        else:
            print(data)
            return None
    else:
        print(f'取手机号错误：{response.status_code}')
    return None


def createTask(country):
    try:
        url = "https://api.capmonster.cloud/createTask"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "task": {
                    "type": "TurnstileTaskProxyless",
                    "websiteURL": f"https://visa.vfsglobal.com/chn/zh/{country}/login",
                    "websiteKey": "0x4AAAAAABhlz7Ei4byodYjs",
                },
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0:
                print(f'成功获取task, {data.get("taskId")}')
                return data.get("taskId")
        return None

    except:
        return None


def get_result(id):
    try:
        url = "https://api.capmonster.cloud/getTaskResult"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "taskId": id,
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            print(data)
            if data.get("errorId") == 0 and data.get("status") == "ready":
                print(f'成功获取cap_token, {data.get("solution").get("token")}')
                return data.get("solution").get("token")
        return None

    except:
        return None


def attempt_create_task(country, max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        id = createTask(country)
        if id:
            return id
        time.sleep(retry_interval)
    return None


def attempt_get_result(id, max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        result = get_result(id)
        if result:
            return result
        time.sleep(retry_interval)
    return None


def register(country, capResult):
    emailid = generate_email()
    phone = get_phone_number()
    if phone == None:
        return False
    phone = str(phone)
    print(f"成功获取号码{phone}")

    url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    try:
        url = "https://lift-apicn.vfsglobal.com/user/registration"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        data = {
            "emailid": emailid,
            "password": encryption('Kq123456@'),
            "confirmPassword": encryption('Kq123456@'),
            "instructionAgreed": True,
            "missioncode": country,
            "countrycode": "chn",
            "languageCode": "zh",
            "dialcode": "86",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "contact": phone,
            "cultureCode": "zh-CN",
            "intTransPerDataAgreed": True,
            "processPerDataAgreed": True,
            "termAndConditionAgreed": True,
            "IsSpecialUser": False
            # "otp": "123456",
        }
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            print(data)
            if "already registered with VFS" in response.text or (data.get("error") != None and data.get("error").get("code") == "414"):
                print(f"{emailid}：注册失败：{data.get('error')}")
                return False
            elif data.get("error") == None:
                print(f"成功注册{emailid}")
                redis_client.hset(
                    "waitactive",
                    emailid,
                    json.dumps(
                        {
                            "email": emailid,
                            "token": "",
                            "phone": phone,
                            "missionCode": country,
                            "email_server": "54.177.132.64",
                            "email_type": "local_email",
                            "redis": "aws",
                            "redis_name": f"{country}LoginUser",
                            "countryCode": "chn",
                            "redis_key": emailid,
                            "get_otp_time": int(time.time()),
                        }
                    ),
                )
                return {"email": emailid, "phone": phone}
            else:
                print(f"{emailid}：注册失败：{data.get('error')}")
                return False
        else:
            print(f"{emailid}：注册失败：{response.status_code}")
            return False
    except Exception as e:
        print(e)
        return False


def attempt_register(country, max_retries=3, retry_interval=1):
    for _ in range(max_retries):
        taskid = attempt_create_task(country)
        if taskid == None:
            return False
        time.sleep(3)
        capResult = attempt_get_result(taskid)
        result = register(country, capResult)
        if result:
            return result
        time.sleep(retry_interval)
    return False


while True:
    for country in scan_country:
        scan_option = [
            option
            for option in scan_data.get(country)
            if option["scan"] == True
        ]
        if len(scan_option) > 0:
            loginUser = [item for item in redis_client.hgetall(f"{country}LoginUser")]
            if len(loginUser) < 3:
                print("正在注册")
                threads = []
                for _ in range(2):
                    thread = threading.Thread(target=attempt_register, args=(country,))
                    thread.start()
                    threads.append(thread)
                for thread in threads:
                    thread.join()
                time.sleep(5)
            else:
                print("数量够")
    time.sleep(5)
