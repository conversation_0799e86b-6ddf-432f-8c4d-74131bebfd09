# -*- coding: utf-8 -*-
import threading
import queue
from queue import Queue
from curl_cffi import requests
import time as tm
import json
import random
import re
from RedisClientAWS import RedisClient
from datetime import datetime, time, timedelta
import string

import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

# 创建 RedisClient 实例
redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


delegate = json.loads(redis_client.get("fast_proxy"))

# 正在处理或等待处理的用户集合
processing_users = set()

loginUsers = [item for item in redis_client.hgetall("cheLoginUser") if item.get('updateTokenTime') != None and int(tm.time()) - item.get('updateTokenTime') < 6000 and int(tm.time()) - item.get('updateTokenTime') > 60]


def refreshloginUsers():
    while True:
        global loginUsers
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        loginUsers = [item for item in redis_client.hgetall("cheLoginUser") if item.get('updateTokenTime') != None and int(tm.time()) - item.get('updateTokenTime') < 6000 and int(tm.time()) - item.get('updateTokenTime') > 60]
        tm.sleep(10)


threading.Thread(target=refreshloginUsers).start()


def is_earlier_than(time_a_str, time_b_str):
    # 将时间字符串解析为datetime对象
    time_format = "%d/%m/%Y"
    time_a = datetime.strptime(time_a_str, time_format)
    time_b = datetime.strptime(time_b_str, time_format)

    # 比较时间
    if time_a < time_b:
        return True
    else:
        return False


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def pick_random_url():
    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=020a8d0b-6285-41be-972a-4997494056e8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d842efa5-61cf-4c64-94ba-3b0008aeeadf",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aa1ad9d3-dea9-49f1-a839-64f9227aa91a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7af17a71-e1ee-4e2c-a3fa-17455515b72e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1c8fe9e-1a96-494e-aa6a-800aa2e311e2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70669e7d-abef-4c9a-85f6-3ffb93cc57a6",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de03a1c0-e4e1-4bdb-abd9-82755c0acfa2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f1d752d6-68e0-4cef-a739-a2ed7e68836c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da064818-0822-4853-bf73-8904815bf592",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a55180cb-c0b7-4056-beaf-a09afb7f18b0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=586fe69a-299e-4a7c-a5cc-ef6eaeebd19b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=606ad073-2903-4eaf-b763-c08bd1dd5737",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2a52ffb3-ccb7-4ac0-b6ee-9e32bb4337b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2da9b32e-82b5-45d9-844b-98bc75e06194",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b949bd7c-0117-4d82-a2e9-4403e70093f1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2217904d-c443-436c-9779-f75d46336002",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=40e794bb-1fc2-46f2-bd11-8f4e87d05ef9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=16c5f10e-fbbc-4e77-b805-ef04a67dc829",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a192259d-64dc-46ed-bea3-51ffcf36ee4e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5789ee00-e168-49e5-a554-233df42d204b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec3915f6-8900-4800-83df-2e9cb40d23b8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b5265a1f-afc8-4fc6-a23f-4774f12f5a8c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=355d0c0a-e964-4a88-9b3d-93eddfa10118",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=254f174a-58f3-4e24-a005-a0eb1b4f0e14",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8532ecc9-6a45-40ab-bd57-93b97fd9cca1",
    ]

    return random.choice(list)


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def attempt_change_visa_type(user, max_retries, retry_interval):
    for _ in range(max_retries):
        res = change_visa_type(user)
        if res:
            return res
        tm.sleep(retry_interval)
    return ""


def change_visa_type(user):
    if len(loginUsers) == 0:
        tm.sleep(1)
        return
    account = random.choice(loginUsers)
    try:
        url = f"https://lift-apicn.vfsglobal.com/appointment/updatewaitlistvisa"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "route": "chn/zh/che",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }

        data = {
            "missionCode": "che",
            "countryCode": "chn",
            "centerCode": "GRSG",
            "loginUser": account.get("email"),
            "lOGINUser": user.get('wemail'),
            "urn": user.get('wurn'),
            "visaCategoryCode": "BUSTRA",
        }
        # data[f"{randomize_case('loginUser')}"] = user["loginUser"]
        print(f"正在切换客户类型{user['chnname']}，护照:{user['passportNO']}")
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            print(data)
            if data["IsUpdatedWaitlistVisa"] == True or data.get('error').get('description') == 'The Visa Category has already been updated.':
                return True
            else:
                return False
        elif response.status_code == 401:
            print(response.status_code)
            return False
        elif response.status_code == 400:
            print(response.status_code)
            return False
        else:
            print(response.status_code)
            return False
    except Exception as e:
        print(e)
        return False


def get_urn(user):
    if len(loginUsers) == 0:
        tm.sleep(1)
        return
    account = random.choice(loginUsers)
    try:
        url = f"https://lift-apicn.vfsglobal.com/appointment/application"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "route": "chn/zh/che",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": account['ltsn']
        }

        data = {
            "countryCode": "chn",
            "missionCode": user["missionCode"],
            "loginUser": account.get("email"),
            "lOGinusEr": user["loginUser"],
            "languageCode": "zh-CN",
        }
        # data[f"{randomize_case('loginUser')}"] = user["loginUser"]
        print(f"正在刷新客户{user['chnname']}，护照:{user['passportNO']}")
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            print(data)
            if data["error"] is None:
                datas = data.get('data')
                if len(datas) > 0:
                    for application in datas:
                        if user.get('urn') == None:
                            if application.get('applicants')[0].get('passportNumber') == user.get('passportNO'):
                                user['urn'] = application.get('urn')
                                print(user)
                                redis_client.hset(
                                    "cheUserDatas",
                                    f"{user['centerCode']}-{user['passportNO']}",
                                    json.dumps({**user}),
                                )

                else:
                    user['check_application_time'] = int(tm.time())
                    redis_client.hset(
                        "cheUserDatas",
                        f"{user['centerCode']}-{user['passportNO']}",
                        json.dumps({**user}),
                    )
            elif "No Applicant exists" in data['error']['description']:
                user['loginUser'] = None
                user['urn'] = None
                user['not_our_email'] = False
                user['check_application_time'] = int(tm.time())
                redis_client.hset(
                    "cheUserDatas",
                    f"{user['centerCode']}-{user['passportNO']}",
                    json.dumps({**user}),
                )
        elif response.status_code == 401:
            print(response.status_code)
        else:
            print(response.status_code)
    except Exception as e:
        print(e)
        user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
        if user_id in processing_users:
            processing_users.remove(user_id)
    user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
    if user_id in processing_users:
        processing_users.remove(user_id)


def is_date_between(date, start_date, end_date):
    # Convert "dd/mm/yyyy" format date strings to "mm/dd/yyyy"
    start_date = convert_date_format(start_date)
    end_date = convert_date_format(end_date)

    # Convert date strings to datetime objects
    date_obj = datetime.strptime(date, "%d/%m/%Y")
    start_date_obj = datetime.strptime(start_date, "%m/%d/%Y")
    end_date_obj = datetime.strptime(end_date, "%m/%d/%Y")

    # Check if date is within the range
    return start_date_obj <= date_obj <= end_date_obj


def convert_date_format(date):
    parts = date.split("/")
    return f"{parts[1]}/{parts[0]}/{parts[2]}"


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def process_users():
    while True:
        try:
            user = user_queue.get(timeout=1)  # 设置超时时间为1秒
            get_urn(user)
            user_queue.task_done()
        except queue.Empty:
            tm.sleep(0.1)  # 如果队列为空，则休眠0.1秒


def getNeedProceed(user_list):
    current_time = int(tm.time())

    def filter_condition(user):
        return (
            (
                not user.get("check_application_time")
                or current_time - user.get("check_application_time") > 180
            )
            and user.get('loginUser') != None
            and user.get('not_our_email') == True
        )

    need_proceed = list(filter(filter_condition, user_list))
    return need_proceed


def refresh_proxy():
    while True:
        tm.sleep(60)
        global delegate
        delegate = json.loads(redis_client.get("fast_proxy"))


threading.Thread(target=refresh_proxy).start()


# 首先，创建并启动子线程
threads = []
for _ in range(50):
    thread = threading.Thread(target=process_users)
    thread.daemon = True  # 将线程设置为守护线程，确保主程序退出时子线程也会停止
    thread.start()
    threads.append(thread)

while True:
    cheUserDatas = redis_client.hgetall("cheUserDatas")
    need_proceed = getNeedProceed(cheUserDatas)
    # 获取当前时间
    now = datetime.now()

    # 获取当前时间所在小时的开始时间
    hour_start = now.replace(minute=0, second=0, microsecond=0)

    # 计算当前时间是否在这个小时的前3到5分钟
    time_diff = now - hour_start
    if len(need_proceed) != 0:
        print(f"正在刷新{len(need_proceed)}个客户的URN")
        # 创建一个线程安全的队列来保存需要处理的用户
        for user in need_proceed:
            user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
            if user_id not in processing_users:
                user_queue.put(user)
                processing_users.add(user_id)
    else:
        print('时辰未到')
        processing_users.clear()
    tm.sleep(30)
