#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OCR护照识别功能
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_visa_config_reading():
    """测试签证配置读取"""
    print("=" * 60)
    print("测试签证配置读取")
    print("=" * 60)
    
    # 模拟vfs_center_data数据
    mock_center_data = {
        "ita": json.dumps({
            "missionCode": "ita",
            "missionCodeName": "意大利",
            "data": [{
                "sub": [{
                    "sub": [{
                        "id": 1001,
                        "name": "旅游签证",
                        "missionCode": "ita",
                        "centerCode": "SHI",
                        "code": "TOU",
                        "isApplicantOTPEnabled": True,
                        "iswaitlist": False,
                        "isOCREnable": True,  # 需要OCR
                    }, {
                        "id": 1002,
                        "name": "商务签证",
                        "missionCode": "ita",
                        "centerCode": "SHI",
                        "code": "BUP",
                        "isApplicantOTPEnabled": True,
                        "iswaitlist": True,   # 启用等待列表
                        "isOCREnable": False, # 不需要OCR
                    }],
                    "code": "SVTV",
                    "name": "申根签证"
                }],
                "isoCode": "SHI",
                "centerName": "上海意大利签证申请中心"
            }]
        })
    }
    
    def mock_get_visa_config(mission_code, center_code, visa_code):
        """模拟获取签证配置"""
        center_data_str = mock_center_data.get(mission_code)
        if not center_data_str:
            return None
        
        center_data = json.loads(center_data_str)
        
        for center in center_data.get('data', []):
            if center.get('isoCode') == center_code:
                def find_visa_config(items):
                    for item in items:
                        if item.get('code') == visa_code:
                            return item
                        if 'sub' in item:
                            result = find_visa_config(item['sub'])
                            if result:
                                return result
                    return None
                
                return find_visa_config(center.get('sub', []))
        return None
    
    # 测试用例
    test_cases = [
        ("ita", "SHI", "TOU", "意大利旅游签证"),
        ("ita", "SHI", "BUP", "意大利商务签证"),
        ("ita", "SHI", "XXX", "不存在的签证类型"),
    ]
    
    print("📋 签证配置测试:")
    for mission_code, center_code, visa_code, description in test_cases:
        config = mock_get_visa_config(mission_code, center_code, visa_code)
        
        if config:
            otp_enabled = config.get('isApplicantOTPEnabled', False)
            waitlist_enabled = config.get('iswaitlist', False)
            ocr_enabled = config.get('isOCREnable', False)
            
            print(f"  ✅ {description}:")
            print(f"    - OTP需求: {otp_enabled}")
            print(f"    - 等待列表: {waitlist_enabled}")
            print(f"    - OCR识别: {ocr_enabled}")
        else:
            print(f"  ❌ {description}: 配置未找到")
        print()

def test_ocr_workflow():
    """测试OCR工作流程"""
    print("=" * 60)
    print("测试OCR工作流程")
    print("=" * 60)
    
    # 模拟订单数据
    order_data = {
        "order_id": "ORDER001",
        "visa_info": {
            "mission_code": "ita",
            "center_code": "SHI",
            "visa_code": "TOU"
        },
        "clients": [
            {
                "name": "张三",
                "passport": "E12345678",
                "passport_image": "passport_001.jpg"
            },
            {
                "name": "李四",
                "passport": "E87654321",
                "passport_image": "passport_002.jpg"
            }
        ]
    }
    
    # 模拟账号数据
    account_data = {
        "email": "<EMAIL>",
        "token": "mock_token",
        "ltsn": "mock_ltsn"
    }
    
    def mock_get_passport_image(passport_image):
        """模拟获取护照图片"""
        print(f"    📷 获取护照图片: {passport_image}")
        
        # 模拟不同情况
        if passport_image == "passport_001.jpg":
            return True, "base64_encoded_image_data_001"
        elif passport_image == "passport_002.jpg":
            return False, 404  # 图片不存在
        else:
            return False, "网络错误"
    
    def mock_upload_passport_document(order_data, client_data, account):
        """模拟上传护照文档"""
        passport_image = client_data.get('passport_image')
        client_name = client_data.get('name')
        
        print(f"  🔄 处理客户: {client_name}")
        
        if not passport_image:
            print(f"    ❌ 客户 {client_name} 没有护照图片")
            return False
        
        # 获取护照图片
        status, result = mock_get_passport_image(passport_image)
        if not status:
            if result == 404:
                print(f"    ❌ 客户 {client_name} 护照图片不存在")
            else:
                print(f"    ❌ 获取护照图片失败: {result}")
            return False
        
        print(f"    📤 上传护照图片进行OCR识别...")
        
        # 模拟上传结果
        import random
        success = random.choice([True, False])
        
        if success:
            mock_guid = f"GUID_{random.randint(1000, 9999)}"
            print(f"    ✅ OCR识别成功，GUID: {mock_guid}")
            client_data['GUID'] = mock_guid
            return True
        else:
            print(f"    ❌ OCR识别失败")
            return False
    
    def mock_ocr_workflow(order_data, account_data):
        """模拟完整的OCR工作流程"""
        visa_info = order_data.get('visa_info', {})
        mission_code = visa_info.get('mission_code')
        center_code = visa_info.get('center_code')
        visa_code = visa_info.get('visa_code')
        clients = order_data.get('clients', [])
        
        print(f"📋 订单: {order_data.get('order_id')}")
        print(f"🎫 签证类型: {mission_code}/{center_code}/{visa_code}")
        
        # 检查是否需要OCR（这里假设需要）
        ocr_required = True  # 模拟OCR需求
        
        if not ocr_required:
            print("  ℹ️  该签证类型不需要OCR识别")
            return True
        
        print("  🔍 该签证类型需要OCR护照识别")
        
        # 为所有客户上传护照进行OCR识别
        ocr_success = True
        for client in clients:
            if not mock_upload_passport_document(order_data, client, account_data):
                ocr_success = False
                break
        
        if ocr_success:
            print("  ✅ 所有客户OCR识别完成，可以继续获取URN")
            return True
        else:
            print("  ❌ OCR识别失败，跳过URN获取")
            return False
    
    # 执行测试
    print("🚀 开始OCR工作流程测试...")
    result = mock_ocr_workflow(order_data, account_data)
    
    print(f"\n📊 测试结果: {'成功' if result else '失败'}")

def test_configuration_flags():
    """测试配置标志的使用"""
    print("\n" + "=" * 60)
    print("测试配置标志的使用")
    print("=" * 60)
    
    # 模拟不同的签证配置
    visa_configs = [
        {
            "name": "意大利旅游签证",
            "isApplicantOTPEnabled": True,
            "iswaitlist": False,
            "isOCREnable": True
        },
        {
            "name": "法国商务签证",
            "isApplicantOTPEnabled": True,
            "iswaitlist": True,
            "isOCREnable": False
        },
        {
            "name": "德国学生签证",
            "isApplicantOTPEnabled": False,
            "iswaitlist": False,
            "isOCREnable": True
        }
    ]
    
    print("🔧 配置标志使用测试:")
    for config in visa_configs:
        print(f"\n  📋 {config['name']}:")
        
        # OTP检查
        if config['isApplicantOTPEnabled']:
            print("    🔐 需要获取OTP验证码")
        else:
            print("    ℹ️  不需要OTP验证")
        
        # 等待列表检查
        if config['iswaitlist']:
            print("    📝 启用等待列表功能")
        else:
            print("    ℹ️  不使用等待列表")
        
        # OCR检查
        if config['isOCREnable']:
            print("    📷 需要上传护照进行OCR识别")
        else:
            print("    ℹ️  不需要OCR护照识别")
        
        # 处理流程
        print("    🔄 处理流程:")
        if config['isOCREnable']:
            print("      1. 上传护照图片进行OCR识别")
            print("      2. 获取URN")
        else:
            print("      1. 直接获取URN")
        
        if config['isApplicantOTPEnabled']:
            print("      3. 获取OTP验证码")
            print("      4. 验证OTP")

if __name__ == "__main__":
    test_visa_config_reading()
    test_ocr_workflow()
    test_configuration_flags()
