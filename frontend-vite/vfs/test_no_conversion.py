#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不使用字段转换的订单处理
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_direct_order_processing():
    """测试直接使用订单数据进行处理"""
    print("=" * 60)
    print("测试直接使用订单数据进行处理")
    print("=" * 60)
    
    # 模拟订单数据
    order_data = {
        "order_id": "2025071015550721622",
        "visa_info": {
            "mission_code": "ita",
            "center_code": "SHI",
            "visa_type": "Shanghai VIP",
            "visa_code": "SSFTME"
        },
        "clients": [{
            "name": "龙春忠",
            "surname_pinyin": "LONG",
            "firstname_pinyin": "CHUNZHONG",
            "passport": "*********",
            "dob": "1978-01-10",
            "passport_expire": "2033-09-12",
            "gender": "男",
            "nationality": "CHN"
        }],
        "order_info": {
            "accept_vip": False,
            "accept_next_day": True,
            "travel_date": "",
            "customer": "1",
            "price": "2",
            "remark": "3"
        },
        "status": "pending",
        "created_by": "yuan",
        "created_at": **********,
        "updated_at": **********
    }
    
    # 模拟账号数据
    account_data = {
        "email": "<EMAIL>",
        "phone": "***********",
        "missionCode": "ita"
    }
    
    print("原始订单数据:")
    print(json.dumps(order_data, indent=2, ensure_ascii=False))
    
    print("\n" + "=" * 60)
    print("直接处理订单数据（无字段转换）")
    print("=" * 60)
    
    # 模拟直接处理订单数据
    def mock_process_order_directly(order_data, account_data):
        """模拟直接处理订单数据，不进行字段转换"""
        
        # 直接添加账号信息到订单数据
        order_data['loginUser'] = account_data.get('email')
        order_data['phone'] = account_data.get('phone')
        order_data['email'] = account_data.get('email')
        
        # 获取签证信息
        visa_info = order_data.get('visa_info', {})
        mission_code = visa_info.get('mission_code')
        center_code = visa_info.get('center_code')
        visa_code = visa_info.get('visa_code')
        
        # 获取客户信息
        clients = order_data.get('clients', [])
        if not clients:
            print("❌ 订单没有客户信息")
            return False
        
        main_client = clients[0]
        
        print("✅ 订单处理信息:")
        print(f"  订单ID: {order_data.get('order_id')}")
        print(f"  签证类型: {mission_code}/{center_code}/{visa_code}")
        print(f"  主申请人: {main_client.get('name')}")
        print(f"  护照号: {main_client.get('passport')}")
        print(f"  登录邮箱: {order_data.get('loginUser')}")
        print(f"  手机号: {order_data.get('phone')}")
        
        # 模拟构建申请人数据
        applicant_data = {
            "firstName": main_client.get("name", ""),  # 直接使用原始name字段
            "lastName": main_client.get("surname_pinyin", ""),
            "passportNumber": main_client.get("passport", ""),
            "dateOfBirth": main_client.get("dob", ""),
            "passportExpirtyDate": main_client.get("passport_expire", ""),
            "gender": 1 if main_client.get("gender") == "男" else 2,
            "nationalityCode": main_client.get("nationality", "CHN"),
            "emailId": order_data.get('loginUser'),
            "contactNumber": str(order_data.get('phone', ""))
        }
        
        print(f"\n✅ 申请人数据构建:")
        print(f"  firstName: {applicant_data['firstName']}")
        print(f"  lastName: {applicant_data['lastName']}")
        print(f"  passportNumber: {applicant_data['passportNumber']}")
        print(f"  dateOfBirth: {applicant_data['dateOfBirth']}")
        print(f"  gender: {applicant_data['gender']}")
        
        # 模拟处理多个客户
        if len(clients) > 1:
            print(f"\n✅ 发现 {len(clients)-1} 个子申请人:")
            for i, child in enumerate(clients[1:], 1):
                print(f"  子申请人{i}: {child.get('name')} ({child.get('passport')})")
        
        return True
    
    # 执行处理
    result = mock_process_order_directly(order_data, account_data)
    
    print(f"\n" + "=" * 60)
    print(f"处理结果: {'成功' if result else '失败'}")
    print("=" * 60)
    
    # 验证关键点
    print("\n🔍 关键验证点:")
    print("1. ✅ 直接使用订单数据，无需字段转换")
    print("2. ✅ firstName字段使用原始name值（龙春忠）")
    print("3. ✅ 保持所有原始字段名和数据结构")
    print("4. ✅ 账号信息直接添加到订单数据中")

def test_user_id_generation():
    """测试用户ID生成逻辑"""
    print("\n" + "=" * 60)
    print("测试用户ID生成逻辑")
    print("=" * 60)
    
    # 模拟订单数据
    order_data = {
        "visa_info": {
            "mission_code": "ita",
            "center_code": "SHI", 
            "visa_code": "SSFTME"
        },
        "clients": [{
            "passport": "*********"
        }]
    }
    
    # 模拟生成用户ID的逻辑
    visa_info = order_data.get('visa_info', {})
    mission_code = visa_info.get('mission_code')
    center_code = visa_info.get('center_code')
    visa_code = visa_info.get('visa_code')
    
    clients = order_data.get('clients', [])
    main_passport = clients[0].get('passport', '') if clients else ''
    
    user_id = f"{mission_code}-{center_code}-{visa_code}-{main_passport}"
    
    print(f"生成的用户ID: {user_id}")
    print(f"组成部分:")
    print(f"  mission_code: {mission_code}")
    print(f"  center_code: {center_code}")
    print(f"  visa_code: {visa_code}")
    print(f"  passport: {main_passport}")
    
    print("\n✅ 用户ID生成逻辑正确")

if __name__ == "__main__":
    test_direct_order_processing()
    test_user_id_generation()
