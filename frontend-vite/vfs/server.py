import asyncio
from aiosmtpd.controller import Controller
from email.parser import Parser
from bs4 import BeautifulSoup
import re
from RedisClientITA import RedisClient
import re
import random
import json
import datetime
from urllib.parse import unquote
import requests
from email.header import decode_header
# 创建 RedisClient 实例
redis_client = RedisClient()


def pick_random_qiwei_url():

    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=488747b4-0e7e-4f21-88f0-810bca25a5f4",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5130bf66-0373-417d-818d-4162c2a803cf",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=36243bca-d6e6-4b0f-9c17-f22caa937407",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1f47877e-1eff-467f-9297-a2c192bf370a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4f723aa2-fbce-4eed-95e2-1a7c0f60c306",
    ]
    return random.choice(list)


def decode_mime_words(s):
    if not s:
        return ''
    decoded_fragments = decode_header(s)
    return ''.join([
        fragment.decode(encoding or 'utf-8', errors='replace') if isinstance(fragment, bytes) else fragment
        for fragment, encoding in decoded_fragments
    ])


def extract_parts(message):
    if message.is_multipart():
        parts = []
        for part in message.get_payload():
            parts.append(extract_parts(part))
        return "\n\n".join(filter(None, parts))
    else:
        content_type = message.get_content_type()
        charset = message.get_content_charset() or 'utf-8'
        if content_type in ['text/plain', 'text/html']:
            payload = message.get_payload(decode=True)
            if payload:
                return decode_mime_words(payload.decode(charset, errors="replace"))
        return ""


class CustomSMTPHandler:
    async def handle_DATA(self, server, session, envelope):
        try:
            content = envelope.content.decode("utf-8", errors="replace")  # 解码邮件内容
            # 使用 email.parser 来解析邮件
            message = Parser().parsestr(content)

            # 提取发件人、收件人、标题
            mail_from = message.get("From")
            rcpt_tos = message.get("To")
            subject = decode_mime_words(message.get("Subject"))

            # # 打印发件人、收件人和标题
            # print(f"Received message from: {mail_from}")
            # print(f"Message recipients: {rcpt_tos}")
            # print(f"Subject: {subject}")
            body = extract_parts(message)

            if "Welcome" in subject:
                soup = BeautifulSoup(body, "html.parser")
                links = soup.find_all("a")
                active_url = links[0]["href"]
                if 'awstrack.me' in active_url:
                    active_url = unquote(active_url.split('visa.vfsglobal.com')[1].split('/')[0])
                current_time = datetime.datetime.now()
                formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                print(f"{formatted_time} {mail_from} -> {rcpt_tos} : {active_url}")
                random_numbers = random.choice(range(1, 11))
                redis_client.publish(
                    f"verify_otp_worker_{random_numbers}",
                    json.dumps(
                        {"email": rcpt_tos, "url": active_url, "type": "active"}
                    ),
                )
            elif "One Time Password" in subject:
                password_match = re.search(r"一次性密码是(\d+)", body)
                if not password_match:
                    password_match = re.search(
                        r"The OTP for your application with VFS Global is (\d+)",
                        body,
                    )
                if password_match:
                    one_time_password = password_match.group(1)
                    current_time = datetime.datetime.now()
                    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                    print(
                        f"{formatted_time} {mail_from} -> {rcpt_tos} : {one_time_password}"
                    )
                    random_numbers = random.choice(range(1, 11))
                    redis_client.set(
                        rcpt_tos,
                        one_time_password,
                        30
                    )
                    redis_client.publish(
                        f"verify_otp_worker_{random_numbers}",
                        json.dumps(
                            {
                                "email": rcpt_tos,
                                "code": one_time_password,
                                "type": "otp",
                            }
                        ),
                    )
            else:
                if "vfs" in mail_from:
                    url = pick_random_qiwei_url()
                    postData = {
                        "msgtype": "text",
                        "text": {
                            "content": f"收到奇怪邮件：【{mail_from}】-【{subject}】"
                        },
                    }
                    requests.post(url, json=postData)
                # 打印发件人、收件人和标题
                print(f"Received message from: {mail_from}")
                print(f"Message recipients: {rcpt_tos}")
                print(f"Subject: {subject}")
                print(f"Body: {body}")

            return "250 OK"
        except Exception as e:
            print(e)


def run_server():
    handler = CustomSMTPHandler()
    controller = Controller(handler, hostname="0.0.0.0", port=25)
    controller.start()
    print("SMTP server listening on all interfaces...")

    try:
        asyncio.get_event_loop().run_forever()
    except KeyboardInterrupt:
        print("Server shutting down...")
        controller.stop()


if __name__ == "__main__":
    run_server()
