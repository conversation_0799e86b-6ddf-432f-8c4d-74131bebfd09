# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
import sys
sys.path.append('../backend')
from RedisClient import RedisClient
import random
from datetime import datetime, timedelta
from queue import Queue
import urllib.parse
from twocaptcha import TwoCaptcha
import re
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

solver = TwoCaptcha("a7d7d55f349865778b327a2c37300e85")

# 创建 RedisClient 实例
redis_client = RedisClient()

def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp

def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key

def encryption(t):
    source_rsa_str = redis_client.get('rsa_str')
    rsa_string = format_rsa_string(source_rsa_str)
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()

def get_delegate():
    """获取代理配置"""
    delegate = json.loads(redis_client.get("login_proxy"))
    return delegate

def remove_account(account):
    """移除失效账号"""
    redis_client.hdel("vfs_accounts", account.get("email"))
    print(f"移除失效账号: {account.get('email')}")

def get_phone(phone):
    """获取手机号状态"""
    url = f"http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == "0":
            return True
        elif data.get("msg") == "指定专属码手机号不在线":
            return "remove_phone"
        else:
            return False
    return False

def get_code(phone):
    """获取短信验证码"""
    url = f"http://api.haozhuma.com/sms/?api=getMessage&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        if data.get("yzm"):
            return data.get("yzm")
        else:
            return ""
    return ""

def createTask(country):
    """创建验证码任务"""
    try:
        url = "https://api.capmonster.cloud/createTask"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "task": {
                    "type": "TurnstileTaskProxyless",
                    "websiteURL": f"https://visa.vfsglobal.com/chn/zh/{country}/login",
                    "websiteKey": "0x4AAAAAABhlz7Ei4byodYjs",
                },
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0:
                return data.get("taskId")
        return None
    except:
        return None

def get_result(id):
    """获取验证码结果"""
    try:
        url = "https://api.capmonster.cloud/getTaskResult"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "taskId": id,
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0 and data.get("status") == "ready":
                return data.get("solution").get("token")
        return None
    except:
        return None

def get_otp(user, country, capResult):
    """获取OTP"""
    try:
        print(f"正在获取 {user['email']} 的OTP")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "username": user.get('email'),
            "password": encryption("Kq123456@"),
        }

        delegate = get_delegate()
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari18_0_ios', 'safari18_0', 'safari17_2_ios', 'safari17_0']),
            verify=False,
        )
        if response.status_code == 200:
            res = response.json()
            if res.get("error") != None:
                print(f"账号 {user['email']} 登录失败: {res.get('error')}")
                remove_account(user)
                return False
            elif res.get("accessToken") != None:
                # 直接获取到token
                update_token_time = int(time.time())
                user["token"] = res.get("accessToken")
                user["updateTokenTime"] = update_token_time
                user["phone"] = res.get("contactNumber")
                user['ltsn'] = response.headers.get('set-cookie').split(';')[0] if response.headers.get('set-cookie') else ""
                print(f"账号 {user['email']} 成功获取Token: {res['accessToken']}")
                redis_client.hset("vfs_accounts", user['email'], json.dumps(user))
                return "Token"
            else:
                # 需要OTP验证
                user["phone"] = res.get("contactNumber")
                redis_client.hset("vfs_accounts", user['email'], json.dumps(user))
                print(f"账号 {user.get('email')} 需要OTP验证")
                return "SMS"
        else:
            print(f"账号 {user.get('email')} 获取OTP失败，status_code: {response.status_code}")
        return False
    except Exception as e:
        print(f"获取OTP异常: {e}")
        return False

def get_token(user, country, otp, capResult):
    """通过OTP获取Token"""
    try:
        print(f"正在获取 {user['email']} 的Token")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }

        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "otp": str(otp),
            "username": user["email"],
            "password": encryption("Kq123456@"),
        }

        delegate = get_delegate()
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            if data["error"] is None:
                update_token_time = int(time.time())
                user["token"] = data["accessToken"]
                user["updateTokenTime"] = update_token_time
                user["phone"] = data.get("contactNumber")
                user['ltsn'] = response.headers.get('set-cookie').split(';')[0] if response.headers.get('set-cookie') else ""
                print(f"账号 {user['email']} 成功获取Token: {data['accessToken']}")
                redis_client.hset("vfs_accounts", user['email'], json.dumps(user))
                return True
            else:
                print(f"账号 {user['email']} 获取Token失败: {data.get('error')}")
                remove_account(user)
                return True
        else:
            print(f"获取Token失败，status_code: {response.status_code}")
            return False
    except Exception as e:
        print(f"获取Token异常: {e}")
        return False

def refresh_token_for_user(user):
    """为单个用户刷新Token"""
    try:
        country = user.get('missionCode')
        if not country:
            print(f"账号 {user.get('email')} 缺少国家信息")
            return False

        # 尝试获取验证码
        taskid = None
        for _ in range(3):
            taskid = createTask(country)
            if taskid:
                break
            time.sleep(2)
        
        if not taskid:
            print(f"无法获取验证码任务")
            return False

        time.sleep(3)
        capResult = None
        for _ in range(10):
            capResult = get_result(taskid)
            if capResult:
                break
            time.sleep(2)

        if not capResult:
            # 使用备用验证码服务
            try:
                capResult = solver.turnstile(
                    sitekey="0x4AAAAAABhlz7Ei4byodYjs",
                    url=f"https://visa.vfsglobal.com/chn/zh/{country}/login",
                ).get("code")
            except:
                print("验证码获取失败")
                return False

        # 尝试登录
        login_result = get_otp(user, country, capResult)
        if login_result == "Token":
            return True
        elif login_result == "SMS":
            # 需要短信验证
            if not user.get("phone"):
                print(f"账号 {user.get('email')} 缺少手机号")
                return False

            # 获取短信验证码
            phone_result = get_phone(user.get("phone"))
            if not phone_result:
                print(f"手机号 {user.get('phone')} 获取失败")
                return False

            time.sleep(5)
            otp_code = None
            for _ in range(10):
                otp_code = get_code(user.get("phone"))
                if otp_code and len(otp_code) == 6:
                    break
                time.sleep(5)

            # 取消手机号接收
            cancel_url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={user.get('phone')}"
            requests.get(cancel_url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)

            if not otp_code or len(otp_code) != 6:
                print(f"未获取到有效的OTP验证码")
                return False

            # 验证OTP
            for _ in range(3):
                taskid = createTask(country)
                if taskid:
                    break
                time.sleep(2)
            
            if not taskid:
                return False

            time.sleep(3)
            capResult = None
            for _ in range(10):
                capResult = get_result(taskid)
                if capResult:
                    break
                time.sleep(2)

            if not capResult:
                try:
                    capResult = solver.turnstile(
                        sitekey="0x4AAAAAABhlz7Ei4byodYjs",
                        url=f"https://visa.vfsglobal.com/chn/zh/{country}/login",
                    ).get("code")
                except:
                    return False

            return get_token(user, country, otp_code, capResult)
        else:
            return False

    except Exception as e:
        print(f"刷新Token异常: {e}")
        return False

def get_accounts_need_refresh():
    """获取需要刷新Token的账号"""
    try:
        all_accounts = redis_client.hgetall("vfs_accounts")
        need_refresh = []
        current_time = int(time.time())

        for account_data in all_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)
                
                # 检查是否需要刷新（token过期时间小于3400秒）
                if (not account_data.get("updateTokenTime") or 
                    current_time - account_data.get("updateTokenTime", 0) > 3400):
                    need_refresh.append(account_data)
            except json.JSONDecodeError:
                continue

        return need_refresh
    except Exception as e:
        print(f"获取需要刷新的账号失败: {e}")
        return []

def process_users():
    """处理用户队列"""
    while not user_queue.empty():
        user = user_queue.get()
        try:
            refresh_token_for_user(user)
        except Exception as e:
            print(f"处理用户 {user.get('email')} 时发生错误: {e}")
        finally:
            user_queue.task_done()

def show_refresh_statistics():
    """显示刷新统计"""
    try:
        all_accounts = redis_client.hgetall("vfs_accounts")
        country_stats = {}
        current_time = int(time.time())

        for account_data in all_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)
                
                country = account_data.get('missionCode', 'unknown')
                if country not in country_stats:
                    country_stats[country] = {'total': 0, 'active': 0, 'need_refresh': 0}

                country_stats[country]['total'] += 1

                if (account_data.get('token') and 
                    account_data.get('updateTokenTime') and
                    current_time - account_data.get('updateTokenTime', 0) < 3400):
                    country_stats[country]['active'] += 1
                else:
                    country_stats[country]['need_refresh'] += 1

            except json.JSONDecodeError:
                continue

        print("\n=== VFS账号Token刷新统计 ===")
        for country, stats in country_stats.items():
            print(f"{country}: 总计{stats['total']} (有效{stats['active']}, 需刷新{stats['need_refresh']})")
        print("==============================\n")

        return country_stats
    except Exception as e:
        print(f"获取刷新统计失败: {e}")
        return {}

# 创建队列
user_queue = Queue()

print("VFS Token刷新服务启动...")

while True:
    try:
        # 显示统计信息
        show_refresh_statistics()
        
        # 获取需要刷新的账号
        need_refresh = get_accounts_need_refresh()
        
        if not need_refresh:
            print("当前没有需要刷新Token的账号")
        else:
            print(f"发现 {len(need_refresh)} 个账号需要刷新Token")
            
            # 将需要刷新的账号加入队列
            for user in need_refresh:
                user_queue.put(user)
            
            # 创建多个线程处理
            num_threads = min(10, len(need_refresh))  # 最多10个线程
            threads = []
            
            for _ in range(num_threads):
                thread = threading.Thread(target=process_users)
                thread.start()
                threads.append(thread)
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            print(f"完成 {len(need_refresh)} 个账号的Token刷新")
        
        # 每隔60秒检查一次
        print("等待60秒后进行下一轮检查...")
        time.sleep(60)
        
    except KeyboardInterrupt:
        print("服务已停止")
        break
    except Exception as e:
        print(f"主循环发生错误: {e}")
        time.sleep(10)