#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OTP判断逻辑
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟Redis客户端
class MockRedisClient:
    def __init__(self):
        # 模拟vfs_center_data数据
        self.vfs_center_data = {
            "prt": json.dumps({
                "missionCode": "prt",
                "missionCodeName": "葡萄牙",
                "data": [{
                    "sub": [{
                        "sub": [{
                            "id": 7498,
                            "parentId": 7497,
                            "masterId": 0,
                            "name": "旅游",
                            "missionCode": "prt",
                            "centerCode": "POBE",
                            "code": "TOU",
                            "isApplicantOTPEnabled": True,
                        }, {
                            "id": 7499,
                            "name": "商务/短期职业培训",
                            "missionCode": "prt",
                            "centerCode": "POBE",
                            "code": "BUP",
                            "isApplicantOTPEnabled": True,
                        }],
                        "code": "SVTV",
                        "name": "申根签证"
                    }, {
                        "sub": [{
                            "id": 7509,
                            "name": "国家签证E类",
                            "missionCode": "prt",
                            "centerCode": "POBE",
                            "code": "NET",
                            "isApplicantOTPEnabled": False,  # 这个不需要OTP
                        }],
                        "code": "NVCE",
                        "name": "E类短期居留签证"
                    }],
                    "isoCode": "POBE",
                    "centerName": "成都葡萄牙签证申请中心"
                }]
            }),
            "pol": json.dumps({
                "missionCode": "pol",
                "missionCodeName": "波兰",
                "data": [{
                    "sub": [{
                        "sub": [{
                            "id": 8001,
                            "name": "旅游",
                            "missionCode": "pol",
                            "centerCode": "PLBE",
                            "code": "TOU",
                            "isApplicantOTPEnabled": False,  # 波兰不需要OTP
                        }],
                        "code": "SVTV",
                        "name": "申根签证"
                    }],
                    "isoCode": "PLBE",
                    "centerName": "北京波兰签证申请中心"
                }]
            })
        }
    
    def hget(self, hash_name, field):
        if hash_name == 'vfs_center_data':
            return self.vfs_center_data.get(field)
        return None

# 模拟函数
def get_visa_config(mission_code, center_code, visa_code, redis_client):
    """获取签证类型配置，判断是否需要OTP"""
    try:
        center_data_str = redis_client.hget('vfs_center_data', mission_code)
        if not center_data_str:
            print(f"未找到国家 {mission_code} 的中心数据")
            return None
        
        center_data = json.loads(center_data_str)
        
        # 遍历中心数据查找匹配的签证类型
        for center in center_data.get('data', []):
            if center.get('isoCode') == center_code:
                # 递归查找签证类型配置
                def find_visa_config(items):
                    for item in items:
                        if item.get('code') == visa_code:
                            return item
                        if 'sub' in item:
                            result = find_visa_config(item['sub'])
                            if result:
                                return result
                    return None
                
                visa_config = find_visa_config(center.get('sub', []))
                if visa_config:
                    return visa_config
        
        print(f"未找到签证类型配置: {mission_code}/{center_code}/{visa_code}")
        return None
        
    except Exception as e:
        print(f"获取签证配置失败: {e}")
        return None


def is_otp_required(mission_code, center_code, visa_code, redis_client):
    """判断指定签证类型是否需要OTP"""
    try:
        visa_config = get_visa_config(mission_code, center_code, visa_code, redis_client)
        if visa_config:
            return visa_config.get('isApplicantOTPEnabled', False)
        return False
    except Exception as e:
        print(f"判断OTP需求失败: {e}")
        return False


def test_otp_logic():
    """测试OTP判断逻辑"""
    print("=" * 60)
    print("测试OTP判断逻辑")
    print("=" * 60)
    
    redis_client = MockRedisClient()
    
    # 测试用例
    test_cases = [
        ("prt", "POBE", "TOU", True, "葡萄牙旅游签证"),
        ("prt", "POBE", "BUP", True, "葡萄牙商务签证"),
        ("prt", "POBE", "NET", False, "葡萄牙国家签证E类"),
        ("pol", "PLBE", "TOU", False, "波兰旅游签证"),
        ("ita", "ITBE", "TOU", False, "意大利旅游签证（数据不存在）"),
    ]
    
    for mission_code, center_code, visa_code, expected, description in test_cases:
        result = is_otp_required(mission_code, center_code, visa_code, redis_client)
        status = "✓" if result == expected else "✗"
        print(f"{status} {description}")
        print(f"   参数: {mission_code}/{center_code}/{visa_code}")
        print(f"   期望: {expected}, 实际: {result}")
        
        # 显示详细配置
        config = get_visa_config(mission_code, center_code, visa_code, redis_client)
        if config:
            print(f"   配置: {config.get('name')} - isApplicantOTPEnabled: {config.get('isApplicantOTPEnabled')}")
        else:
            print(f"   配置: 未找到")
        print()
    
    print("=" * 60)


if __name__ == "__main__":
    test_otp_logic()
