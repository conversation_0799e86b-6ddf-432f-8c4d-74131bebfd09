# -*- coding: utf-8 -*-
import requests
import re
import random
import time
import json
from datetime import datetime, timedelta
from curl_cffi import requests
import sys
from RedisClient import RedisClient
import threading
from pypinyin import pinyin, Style
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


delegate = json.loads(redis_client.get("login_proxy"))


def refresh_proxy():
    """刷新代理配置"""
    while True:
        time.sleep(10)
        global delegate
        delegate = json.loads(redis_client.get("login_proxy"))


# 扩展的常见中国姓和名
first_names = [
    "伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟",
    "涛", "明", "超", "秀英", "平", "刚", "桂英", "鑫", "涛", "建国", "志强", "敏", "小红",
    "婷婷", "玉兰", "梅", "明华", "英杰", "俊杰", "嘉豪", "嘉怡", "子豪", "子轩", "子涵",
    "宇轩", "宇航", "宇翔", "子琪", "雅静", "雅婷", "雅涵", "文婷", "文静", "文豪", "文轩", "秋白", "南风", "醉山",
    "初彤", "凝海", "紫文", "凌晴", "香卉", "雅琴", "傲安", "傲之", "初蝶", "寻桃", "代芹", "诗霜",
    "春柏", "绿夏", "碧灵", "诗柳", "夏柳", "采白", "慕梅", "乐安", "冬菱", "紫安", "宛凝", "雨雪",
    "易真", "安荷", "静竹", "飞雪", "雪兰", "雅霜", "从蓉", "冷雪", "靖巧", "翠丝", "觅翠", "凡白",
    "乐蓉", "迎波", "丹烟", "梦旋", "书双", "念桃", "夜天", "海桃", "青香", "恨风", "安筠", "觅柔",
    "初南", "秋蝶", "千易", "安露", "诗蕊", "山雁", "友菱", "香露", "晓兰", "涵瑶", "秋柔", "思菱",
    "醉柳", "以寒", "迎夏", "向雪", "香莲", "以丹", "依凝", "如柏", "雁菱", "凝竹", "宛白", "初柔",
    "南蕾", "书萱", "梦槐", "香芹", "南琴", "绿海", "沛儿", "晓瑶", "听春", "易巧", "念云", "晓灵",
    "静枫", "夏蓉", "如南", "幼丝", "秋白", "冰安", "凝蝶", "紫雪", "念双", "念真", "曼寒", "凡霜",
    "白卉", "语山", "冷珍", "秋翠", "夏柳", "如之", "忆南", "书易", "翠桃", "寄瑶", "如曼", "问柳",
    "香梅", "幻桃", "又菡", "春绿", "醉蝶", "亦绿", "诗珊", "听芹", "新之", "博瀚", "博超", "才哲",
    "才俊", "成和", "成弘", "昊苍", "昊昊", "昊空", "昊乾", "昊穹", "昊然", "昊然", "昊天", "昊焱",
    "昊英", "浩波", "浩博", "浩初", "浩大", "浩宕", "浩荡", "浩歌", "浩广", "浩涆", "浩瀚", "浩浩",
    "浩慨", "浩旷", "浩阔", "浩漫", "浩淼", "浩渺", "浩邈", "浩气", "浩然", "浩穰", "浩壤", "浩思",
    "浩言", "皓轩", "和蔼", "和安", "和昶", "翔东", "昊伟", "楚桥", "智霖", "浩杰", "炎承", "思哲",
]

last_names = [
    "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈",
    "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许",
    "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏",
    "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦", "章",
    "云", "苏", "潘", "葛", "奚", "范", "彭", "郎", "鲁", "韦",
    "昌", "马", "苗", "凤", "花", "方", "俞", "任", "袁", "柳",
    "酆", "鲍", "史", "唐", "费", "廉", "岑", "薛", "雷", "贺",
    "倪", "汤", "滕", "殷", "罗", "毕", "郝", "邬", "安", "常",
    "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍", "余",
    "元", "卜", "顾", "孟", "平", "黄", "和", "穆", "萧", "尹"
]


def to_pinyin(hanzi):
    # 将汉字转换为拼音
    return ''.join([item[0] for item in pinyin(hanzi, style=Style.NORMAL)])


def generate_email():
    email_formats = []

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{first_name_pinyin}.{last_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        year = random.randint(1970, 2010)
        month = str(random.randint(1, 12)).zfill(2)
        day = str(random.randint(1, 28)).zfill(2)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}{month}{day}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{last_name_pinyin}_{first_name_pinyin}")

    domains = ['nextdomain10.xyz']
    return f"{random.choice(email_formats)}@{random.choice(domains)}"


def get_phone_number():
    url = "http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        print(response.text)
        data = response.json()
        if data.get("phone") != None:
            return data.get("phone")
        else:
            print(data)
            return None
    else:
        print(f'取手机号错误：{response.status_code}')
    return None


def createTask(country):
    try:
        url = "https://api.capmonster.cloud/createTask"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "task": {
                    "type": "TurnstileTaskProxyless",
                    "websiteURL": f"https://visa.vfsglobal.com/chn/zh/{country}/login",
                    "websiteKey": "0x4AAAAAABhlz7Ei4byodYjs",
                },
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0:
                print(f'成功获取task, {data.get("taskId")}')
                return data.get("taskId")
        return None

    except:
        return None


def get_result(id):
    try:
        url = "https://api.capmonster.cloud/getTaskResult"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "taskId": id,
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            print(data)
            if data.get("errorId") == 0 and data.get("status") == "ready":
                print(f'成功获取cap_token, {data.get("solution").get("token")}')
                return data.get("solution").get("token")
        return None

    except:
        return None


def attempt_create_task(country, max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        id = createTask(country)
        if id:
            return id
        time.sleep(retry_interval)
    return None


def attempt_get_result(id, max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        result = get_result(id)
        if result:
            return result
        time.sleep(retry_interval)
    return None


def register(country, capResult):
    emailid = generate_email()
    phone = get_phone_number()
    if phone == None:
        return False
    phone = str(phone)
    print(f"成功获取号码{phone}")

    url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    try:
        url = "https://lift-apicn.vfsglobal.com/user/registration"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        data = {
            "emailid": emailid,
            "password": encryption('Kq123456@'),
            "confirmPassword": encryption('Kq123456@'),
            "instructionAgreed": True,
            "missioncode": country,
            "countrycode": "chn",
            "languageCode": "zh",
            "dialcode": "86",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "contact": phone,
            "cultureCode": "zh-CN",
            "intTransPerDataAgreed": True,
            "processPerDataAgreed": True,
            "termAndConditionAgreed": True,
            "IsSpecialUser": False
        }
        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            print(data)
            if "already registered with VFS" in response.text or (data.get("error") != None and data.get("error").get("code") == "414"):
                print(f"{emailid}：注册失败：{data.get('error')}")
                return False
            elif data.get("error") == None:
                print(f"成功注册{emailid}")
                redis_client.hset(
                    "waitactive",
                    emailid,
                    json.dumps(
                        {
                            "email": emailid,
                            "token": "",
                            "phone": phone,
                            "missionCode": country,
                            "email_server": "54.177.132.64",
                            "email_type": "local_email",
                            "redis": "aws",
                            "redis_name": "vfs_accounts",
                            "countryCode": "chn",
                            "redis_key": emailid,
                            "get_otp_time": int(time.time()),
                        }
                    ),
                )
                return {"email": emailid, "phone": phone}
            else:
                print(f"{emailid}：注册失败：{data.get('error')}")
                return False
        else:
            print(f"{emailid}：注册失败：{response.status_code}")
            return False
    except Exception as e:
        print(e)
        return False


def attempt_register(country, max_retries=3, retry_interval=1):
    for _ in range(max_retries):
        taskid = attempt_create_task(country)
        if taskid == None:
            return False
        time.sleep(3)
        capResult = attempt_get_result(taskid)
        result = register(country, capResult)
        if result:
            return result
        time.sleep(retry_interval)
    return False


def get_needed_countries():
    """从vfs_user中获取需要注册账号的国家列表"""
    try:
        # 获取所有vfs_user订单数据 - RedisClient.hgetall返回的是列表
        all_orders = redis_client.hgetall('vfs_user')
        needed_countries = set()

        for order_data in all_orders:
            try:
                # 如果order_data是字符串，需要解析JSON
                if isinstance(order_data, str):
                    order_data = json.loads(order_data)

                # 获取visa_info中的mission_code
                if 'visa_info' in order_data:
                    mission_code = order_data['visa_info'].get('mission_code')
                    if mission_code:
                        # 转换为小写，作为国家代码
                        country_code = mission_code.lower()
                        needed_countries.add(country_code)
                        print(f"订单 {order_data.get('order_id', 'unknown')} 需要国家: {country_code}")

                # 兼容旧格式：visa_types数组
                elif 'visa_types' in order_data:
                    for visa_type in order_data['visa_types']:
                        mission_code = visa_type.get('mission_code')
                        if mission_code:
                            country_code = mission_code.lower()
                            needed_countries.add(country_code)
                            print(f"订单 {order_data.get('order_id', 'unknown')} 需要国家: {country_code}")

                # 兼容primary_visa格式
                elif 'primary_visa' in order_data:
                    mission_code = order_data['primary_visa'].get('mission_code')
                    if mission_code:
                        country_code = mission_code.lower()
                        needed_countries.add(country_code)
                        print(f"订单 {order_data.get('order_id', 'unknown')} 需要国家: {country_code}")

            except json.JSONDecodeError as e:
                print(f"解析订单数据失败: {e}")
                continue
            except Exception as e:
                print(f"处理订单数据失败: {e}")
                continue

        # 排除不需要注册的国家
        excluded_countries = {'spain', 'deu'}  # 西班牙、德国
        needed_countries = needed_countries  # - excluded_countries

        print(f"需要注册账号的国家: {list(needed_countries)}")
        return list(needed_countries)

    except Exception as e:
        print(f"获取需要注册的国家列表失败: {e}")
        return []


def check_country_login_users(country):
    """检查指定国家的登录用户数量"""
    try:
        all_accounts = redis_client.hgetall("vfs_accounts")
        country_accounts = []

        for account_data in all_accounts:
            try:
                # 如果account_data是字符串，需要解析JSON
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                if account_data.get('missionCode') == country:
                    country_accounts.append(account_data)
            except json.JSONDecodeError:
                continue

        return len(country_accounts)
    except:
        return 0


def get_available_accounts_for_country(country):
    """获取指定国家的可用账号列表"""
    try:
        all_accounts = redis_client.hgetall("vfs_accounts")
        country_accounts = []

        for account_data in all_accounts:
            try:
                # 如果account_data是字符串，需要解析JSON
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                if (account_data.get('missionCode') == country and
                    account_data.get('token') and
                        account_data.get('updateTokenTime')):
                    # 检查token是否在有效期内（6000秒内）
                    if int(time.time()) - account_data.get('updateTokenTime', 0) < 6000:
                        country_accounts.append(account_data)
            except json.JSONDecodeError:
                continue

        return country_accounts
    except Exception as e:
        print(f"获取国家 {country} 的可用账号失败: {e}")
        return []


def register_for_country(country):
    """为指定国家注册账号"""
    current_count = check_country_login_users(country)
    if current_count < 3:
        print(f"国家 {country} 当前账号数量: {current_count}, 开始注册...")

        threads = []
        # 计算需要注册的数量
        need_count = min(2, 3 - current_count)

        for _ in range(need_count):
            thread = threading.Thread(target=attempt_register, args=(country,))
            thread.start()
            threads.append(thread)

        for thread in threads:
            thread.join()
    else:
        print(f"国家 {country} 账号数量充足: {current_count}")


def show_accounts_statistics():
    """显示账号统计信息"""
    try:
        all_accounts = redis_client.hgetall("vfs_accounts")
        country_stats = {}

        for account_data in all_accounts:
            try:
                # 如果account_data是字符串，需要解析JSON
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                country = account_data.get('missionCode', 'unknown')

                if country not in country_stats:
                    country_stats[country] = {
                        'total': 0,
                        'active': 0,
                        'inactive': 0
                    }

                country_stats[country]['total'] += 1

                # 检查是否有有效token
                if (account_data.get('token') and
                    account_data.get('updateTokenTime') and
                        int(time.time()) - account_data.get('updateTokenTime', 0) < 6000):
                    country_stats[country]['active'] += 1
                else:
                    country_stats[country]['inactive'] += 1

            except json.JSONDecodeError:
                continue

        print("\n=== VFS账号统计 ===")
        for country, stats in country_stats.items():
            print(f"{country}: 总计{stats['total']} (活跃{stats['active']}, 失效{stats['inactive']})")
        print("==================\n")

        return country_stats

    except Exception as e:
        print(f"获取账号统计失败: {e}")
        return {}


# 启动代理刷新线程
threading.Thread(target=refresh_proxy, daemon=True).start()

print("VFS User Register 服务启动...")
print("从vfs_user订单数据中获取需要注册的国家...")

while True:
    try:
        # 显示当前账号统计
        show_accounts_statistics()

        # 获取需要注册账号的国家列表
        needed_countries = get_needed_countries()

        if not needed_countries:
            print("当前没有需要注册账号的国家")
        else:
            # 为每个需要的国家检查并注册账号
            for country in needed_countries:
                try:
                    register_for_country(country)
                    time.sleep(2)  # 避免过快请求
                except Exception as e:
                    print(f"为国家 {country} 注册账号时发生错误: {e}")

        # 每隔30秒检查一次
        print("等待30秒后进行下一轮检查...")
        time.sleep(30)

    except Exception as e:
        print(f"主循环发生错误: {e}")
        time.sleep(10)
