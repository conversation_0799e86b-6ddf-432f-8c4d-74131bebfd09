# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
import re
import os
from datetime import datetime, timedelta
import imaplib
import email
from email.header import decode_header
import string
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization, hashes
import base64


redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


delegate = json.loads(redis_client.get("worker_proxy"))

center_data = json.loads(redis_client.get("centerData"))

all_missionCode_map = {}
all_centerCode_map = {}
for country_data in center_data:
    all_missionCode_map[country_data.get('missionCode')] = country_data.get('missionCodeName')
    for center in country_data.get('data'):
        all_centerCode_map[f"{country_data.get('missionCode')}-{center.get('isoCode')}"] = center.get('centerName')


def get_centerKey(center):
    return f"{center.get('missionCode')}-{center.get('centerCode')}"


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def pick_random_qiwei_url(zw, center):
    sh_zwlist = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=42b02f70-26d4-4d39-b3e0-e8a4e9d0562d",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=50c9f2f8-7114-4897-af2b-e27a9b804dba",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dbb0e25b-ca08-4454-8898-3a0ae787fd5b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ecf71308-c423-4269-aa69-452e4088466c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b38addc5-1bc4-46e4-b654-652cc83b6f19",
    ]
    zwlist = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6e7591a7-3cf6-4128-aaa6-c4e0d1a6d353",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f6494bee-d127-449e-af2b-15070442a792",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0462e7a1-14f8-4538-aa18-56282204c175",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=342966c0-d9c2-47dc-aa28-44657b485545",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5972903f-6a63-47bf-b0e1-8565d2f63530",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c5539dd1-a547-4225-ab10-bb0e871af9ab",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3cc124d4-0388-44f5-9a9c-c6b55786d11a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=13e9dd9a-a1cb-439a-9bdc-faaf760d0111",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9e81a1f-a5df-4110-ab71-485d8b9166d0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8228068a-ce12-4558-b15c-c3df1561b6e1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9ee5d800-2e77-42a1-8f50-9ba0628b0e55",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5296262e-759e-4c9a-9a7d-a8cae4a73bdd",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2fad30bb-24e4-4869-a0a7-ccf67aa23a7b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5cbfe67a-6c99-433b-b185-9b9852d1ff6a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=60c95926-1697-48c0-a577-ae33667fed0b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8cd589ac-a893-4c28-a016-6fd130a8a9b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8cd589ac-a893-4c28-a016-6fd130a8a9b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9f768e7-eacb-4dc9-9ae1-c55d98f88097",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1c2f752f-4082-4d98-9f37-5561fac6f6b7",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0ff8e975-d689-4839-9932-987e00c16ac0",
    ]
    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=020a8d0b-6285-41be-972a-4997494056e8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d842efa5-61cf-4c64-94ba-3b0008aeeadf",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aa1ad9d3-dea9-49f1-a839-64f9227aa91a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7af17a71-e1ee-4e2c-a3fa-17455515b72e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1c8fe9e-1a96-494e-aa6a-800aa2e311e2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70669e7d-abef-4c9a-85f6-3ffb93cc57a6",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de03a1c0-e4e1-4bdb-abd9-82755c0acfa2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f1d752d6-68e0-4cef-a739-a2ed7e68836c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da064818-0822-4853-bf73-8904815bf592",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a55180cb-c0b7-4056-beaf-a09afb7f18b0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=586fe69a-299e-4a7c-a5cc-ef6eaeebd19b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=606ad073-2903-4eaf-b763-c08bd1dd5737",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2a52ffb3-ccb7-4ac0-b6ee-9e32bb4337b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2da9b32e-82b5-45d9-844b-98bc75e06194",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b949bd7c-0117-4d82-a2e9-4403e70093f1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2217904d-c443-436c-9779-f75d46336002",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=40e794bb-1fc2-46f2-bd11-8f4e87d05ef9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=16c5f10e-fbbc-4e77-b805-ef04a67dc829",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a192259d-64dc-46ed-bea3-51ffcf36ee4e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5789ee00-e168-49e5-a554-233df42d204b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec3915f6-8900-4800-83df-2e9cb40d23b8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b5265a1f-afc8-4fc6-a23f-4774f12f5a8c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=355d0c0a-e964-4a88-9b3d-93eddfa10118",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=254f174a-58f3-4e24-a005-a0eb1b4f0e14",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8532ecc9-6a45-40ab-bd57-93b97fd9cca1",
    ]
    if zw == True:
        if center == "GRSG":
            return pick_random_elements(sh_zwlist, 1)[0]
        return pick_random_elements(zwlist, 1)[0]
    else:
        return pick_random_elements(list, 1)[0]


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def schedule(user):
    random_slot = random.choice(user.get('suit_slot'))
    missionCode = user.get('missionCode')
    account = redis_client.hget(f"{missionCode}LoginUser", user.get('slot_account'))
    if account != None:
        account = json.loads(account)
    if account != None:
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        print(
            f"当前时间:{formatted_time} | 正在下单客户:{user.get('chnname')} | 客户详情: {user}"
        )

        url = (
            f"https://lift-apicn.vfsglobal.com/appointment/schedule"
        )
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "route": f"chn/zh/{missionCode}",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cookie": account.get('ltsn')
        }
        fee = json.loads(redis_client.get('feesData'))[0].get(missionCode)
        if fee:
            fee = int(fee)
            if user.get('child') != None and len(user.get('child')) > 0:
                sum = len(user.get('child')) * fee
            else:
                sum = fee
        else:
            sum = None
        if len(str(random_slot["allocationId"])) < 20:
            data = {
                "missioncode": user["missionCode"],
                "countryCode": "chn",
                "centerCode": user["centerCode"],
                "loginUser": account.get("email"),
                "loginUSEr": user["loginUser"] if user.get('wurn') == None else user.get('wemail'),
                "urn": account.get("urn"),
                "URN": user["urn"] if user.get('wurn') == None else user.get('wurn'),
                "notificationType": "none",
                "paymentdetails": {
                    "paymentmode": "CBANK",
                    "RequestRefNo": "",
                    "clientId": "",
                    "merchantId": "",
                    "amount": sum,
                    "currency": "CNY",
                },
                "allocationId": str(random_slot["allocationId"]),
                "CanVFSReachoutToApplicant": False,
            }
        else:
            data = {
                "missionCode": user["missionCode"],
                "countryCode": "chn",
                "centerCode": user["centerCode"],
                "loginUser": account.get("email"),
                "loginUSEr": user["loginUser"] if user.get('wurn') == None else user.get('wemail'),
                "urn": account.get("urn"),
                "URN": user["urn"] if user.get('wurn') == None else user.get('wurn'),
                "notificationType": "none",
                "paymentdetails": {
                    "paymentmode": "CBANK",
                    "RequestRefNo": "",
                    "clientId": "",
                    "merchantId": "",
                    "amount": sum,
                    "currency": "CNY",
                },
                "allocationId": str(random_slot["allocationId"]),
                "CanVFSReachoutToApplicant": False,
            }
        proxy = "http://kq123-zone-resi-region-in:<EMAIL>:16666"
        proxies = {
            "http": proxy,
            "https": proxy,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            current_time = datetime.now()
            formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
            print(
                f"当前时间:{formatted_time} | 下单客户:{user.get('chnname')} | 下单结果: {data}"
            )
            if data["error"] is None:

                if "status" in data:
                    return
                if data["IsAppointmentBooked"] == True:
                    url = pick_random_qiwei_url(
                        user["remark"] == "现号", user.get("centerCode")
                    )
                    postData = {
                        "msgtype": "text",
                        "text": {
                            "content": f"{formatted_time} | worker下单：{all_missionCode_map[user['missionCode']]}-{all_centerCode_map[get_centerKey(user)]} | VFS注册账户:{user['loginUser'] if user.get('wurn') == None else user.get('wemail')} | VFS注册手机号:{user['phone']} | 日期:{random_slot['date']} | 时间:{random_slot['time']} | 总位置:{random_slot['totalSeats']} | 剩余:{random_slot['remainingSeats']} | 位置ID:{random_slot['allocationId']} | 客户姓名:{user['chnname']} ｜ {user.get('xing')} {user.get('name')} | 护照号:{user['passportNO']}| 来源:{user.get('from')} | 价格:{user.get('price')} | 登记:{user.get('reg')} | 备注:{user['remark']} | URN_CODE:{user['urn'] if user.get('wurn') == None else user.get('wurn')}"  # | 支付链接：{data['URL']}?RequestRefNo={data['RequestRefNo']}&Email={user['loginUser']}&Currency=CNY&Culture=zh-CN&Amount=150&DigitalSignature={data['DigitalSignature']}&RedirectURL="
                        },
                    }
                    requests.post(url, json=postData)
                    if user.get("remark") != "现号":
                        redis_client.hset(
                            "successUserDatas",
                            f"{user['centerCode']}-{user['passportNO']}",
                            json.dumps(
                                {
                                    **user,
                                    "date": random_slot["date"],
                                    "time": random_slot["time"],
                                    "message": data,
                                    "amount": sum,
                                    "update_order_time": int(time.time()),
                                    "RequestRefNo": data["RequestRefNo"],
                                    "old_url": f"https://online.vfsglobal.com/PG-Component/Payment/PGRequest?RequestRefNo={data['RequestRefNo']}&Email={user['loginUser']}&Currency=CNY&Culture=zh-CN&Amount={sum}&DigitalSignature={data['DigitalSignature']}&RedirectURL=",
                                    "url": f"{data.get('URL')}?payLoad={data.get('payLoad')}",
                                }
                            ),
                        )
                        for user_d in redis_client.hgetall(f"{user.get('missionCode')}UserDatas"):
                            if user_d.get("passportNO") == user.get("passportNO"):
                                redis_client.hdel(
                                    f"{user.get('missionCode')}UserDatas",
                                    f"{user_d['centerCode']}-{user_d['passportNO']}",
                                )
                    else:
                        for user_d in redis_client.hgetall(f"{user.get('missionCode')}UserDatas"):
                            if user_d.get("passportNO") == user.get("passportNO"):
                                redis_client.hdel(
                                    f"{user.get('missionCode')}UserDatas",
                                    f"{user_d['centerCode']}-{user_d['passportNO']}",
                                )

            elif "You have already" in data["error"]["description"]:
                if user["remark"] != "现号":
                    redis_client.hset(
                        "errorUserDatas",
                        f"{user['centerCode']}-{user['passportNO']}",
                        json.dumps(
                            {
                                **user,
                                "update_order_time": int(time.time()),
                                "message": data["error"]["description"],
                            }
                        ),
                    )
                    redis_client.hdel(
                        f"{user.get('missionCode')}UserDatas", f"{user['centerCode']}-{user['passportNO']}"
                    )

            elif "retrying" in data["error"]["description"]:
                if user["remark"] != "现号":
                    redis_client.hset(
                        "errorUserDatas",
                        f"{user['centerCode']}-{user['passportNO']}",
                        json.dumps(
                            {
                                **user,
                                "update_order_time": int(time.time()),
                                "message": data["error"]["description"],
                            }
                        ),
                    )
                    redis_client.hdel(
                        f"{user.get('missionCode')}UserDatas", f"{user['centerCode']}-{user['passportNO']}"
                    )

            elif "You have exceeded the maximum no. of allowed appointments" in data["error"]["description"]:
                redis_client.hset(
                    f"{user.get('missionCode')}UserDatas",
                    f"{user['centerCode']}-{user['passportNO']}",
                    json.dumps(
                        {
                            **user,
                            "urn": None
                        }
                    ),
                )
        else:
            current_time = datetime.now()
            formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
            if response.status_code == 429:
                print(
                    f"当前时间:{formatted_time} | 下单客户:{user.get('chnname')} | 下单结果: {response.status_code}"
                )
            else:
                print(
                    f"当前时间:{formatted_time} | 下单客户:{user.get('chnname')} | 下单结果: {response.status_code}"
                )


def ready(_, user):
    user = json.loads(user)
    thread = threading.Thread(target=schedule, args=(user,))
    thread.start()


def refresh_proxy():
    while True:
        time.sleep(60)
        global delegate
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        delegate = json.loads(redis_client.get("worker_proxy"))


threading.Thread(target=refresh_proxy).start()

redis_client.subscribe(f"common_worker", ready)
print(f"worker正在待命")
while True:
    time.sleep(0.1)
