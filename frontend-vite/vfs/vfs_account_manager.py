# -*- coding: utf-8 -*-
"""
VFS账号管理器 - 统一处理账号注册、激活、Token刷新
"""
from curl_cffi import requests
import time
import json
import threading
import sys
from RedisClient import RedisClient
import random
from datetime import datetime, timedelta
# from queue import Queue  # 不再使用队列，改为直接启动线程
import urllib.parse
# from twocaptcha import TwoCaptcha  # 已替换为capmonster
import re
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from pypinyin import pinyin, Style
import logging
from logging.handlers import RotatingFileHandler
import os

# 日志配置


def setup_logger():
    """配置专业的日志系统"""
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 创建logger
    logger = logging.getLogger('VFS_AccountManager')
    logger.setLevel(logging.INFO)

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(threadName)-15s | %(funcName)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件handler - 轮转日志
    file_handler = RotatingFileHandler(
        os.path.join(log_dir, 'vfs_account_manager.log'),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # 控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 添加handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


# 初始化日志和配置
logger = setup_logger()
redis_client = RedisClient()

# 全局锁管理 - 按账号+国家锁定
refresh_locks = {}  # 格式: {email_country: lock_object}
refresh_lock_mutex = threading.Lock()  # 保护refresh_locks的互斥锁

# 全局锁管理
phone_locks = {}  # 手机号锁定字典
phone_lock_mutex = threading.Lock()  # 保护phone_locks的互斥锁
refresh_in_progress = {}  # 正在刷新的账号
refresh_lock_mutex = threading.Lock()  # 保护refresh_in_progress的互斥锁

# 中文姓名库
first_names = [
    "伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟",
    "涛", "明", "超", "秀英", "平", "刚", "桂英", "鑫", "涛", "建国", "志强", "敏", "小红",
    "婷婷", "玉兰", "梅", "明华", "英杰", "俊杰", "嘉豪", "嘉怡", "子豪", "子轩", "子涵",
    "宇轩", "宇航", "宇翔", "子琪", "雅静", "雅婷", "雅涵", "文婷", "文静", "文豪", "文轩"
]

last_names = [
    "赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈",
    "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许",
    "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏",
    "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦", "章"
]


def get_refresh_lock(email, country):
    """获取账号+国家的刷新锁"""
    lock_key = f"{email}_{country}"

    with refresh_lock_mutex:
        if lock_key not in refresh_locks:
            refresh_locks[lock_key] = threading.Lock()
        return refresh_locks[lock_key]


def is_refresh_in_progress(email, country):
    """检查账号+国家是否正在刷新中"""
    lock_key = f"{email}_{country}"

    with refresh_lock_mutex:
        if lock_key in refresh_locks:
            # 尝试获取锁，如果获取不到说明正在刷新
            lock = refresh_locks[lock_key]
            acquired = lock.acquire(blocking=False)
            if acquired:
                lock.release()
                return False
            else:
                return True
        return False


def get_current_timestamp():
    """获取当前时间戳"""
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    """格式化RSA密钥"""
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


def encryption(t):
    """加密函数"""
    source_rsa_str = redis_client.get('rsa_str')
    rsa_string = format_rsa_string(source_rsa_str)
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def get_delegate():
    """获取代理配置"""
    try:
        delegate = json.loads(redis_client.get("login_proxy"))
        return delegate
    except:
        return []


def to_pinyin(hanzi):
    """将汉字转换为拼音"""
    return ''.join([item[0] for item in pinyin(hanzi, style=Style.NORMAL)])


def generate_email():
    """生成随机邮箱"""
    email_formats = []

    # 生成不同格式的邮箱
    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{first_name_pinyin}.{last_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}")

    for _ in range(2):
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        first_name_pinyin = to_pinyin(first_name)
        last_name_pinyin = to_pinyin(last_name)
        month = str(random.randint(1, 12)).zfill(2)
        day = str(random.randint(1, 28)).zfill(2)
        email_formats.append(f"{last_name_pinyin}{first_name_pinyin}{month}{day}")

    domains = ['nextdomain10.xyz']
    return f"{random.choice(email_formats)}@{random.choice(domains)}"


def get_phone_number():
    """获取手机号"""
    url = "http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)
    if response.status_code == 200:
        data = response.json()
        if data.get("phone") != None:
            return data.get("phone")
    return None


def createTask(country):
    """创建验证码任务"""
    try:
        url = "https://api.capmonster.cloud/createTask"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "task": {
                    "type": "TurnstileTaskProxyless",
                    "websiteURL": f"https://visa.vfsglobal.com/chn/zh/{country}/login",
                    "websiteKey": "0x4AAAAAABhlz7Ei4byodYjs",
                },
            },
            verify=False,
            timeout=60,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0:
                return data.get("taskId")
        return None
    except:
        return None


def get_result(id):
    """获取验证码结果"""
    try:
        url = "https://api.capmonster.cloud/getTaskResult"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "taskId": id,
            },
            verify=False,
            timeout=60,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0 and data.get("status") == "ready":
                return data.get("solution").get("token")
        return None
    except:
        return None


def register_account(country, capResult):
    """注册账号"""
    emailid = generate_email()
    phone = get_phone_number()
    if phone == None:
        return False
    phone = str(phone)
    logger.info(f"手机号获取成功 | 号码: {phone}")

    # 取消接收
    cancel_url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    requests.get(cancel_url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)

    try:
        url = "https://lift-apicn.vfsglobal.com/user/registration"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        data = {
            "emailid": emailid,
            "password": encryption('Kq123456@'),
            "confirmPassword": encryption('Kq123456@'),
            "instructionAgreed": True,
            "missioncode": country,
            "countrycode": "chn",
            "languageCode": "zh",
            "dialcode": "86",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "contact": phone,
            "cultureCode": "zh-CN",
            "intTransPerDataAgreed": True,
            "processPerDataAgreed": True,
            "termAndConditionAgreed": True,
            "IsSpecialUser": False
        }

        delegate = get_delegate()
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['firefox133', 'firefox135']),
            verify=False,
            timeout=60,
        )

        if response.status_code == 200:
            data = response.json()
            if "already registered with VFS" in response.text or (data.get("error") != None and data.get("error").get("code") == "414"):
                logger.warning(f"账号注册失败 | 邮箱: {emailid} | 原因: 邮箱已注册 | 错误: {data.get('error')}")
                return False
            elif data.get("error") == None:
                logger.info(f"账号注册成功 | 邮箱: {emailid} | 状态: 等待激活")
                # 存储到waitactive等待激活
                redis_client.hset(
                    "waitactive",
                    emailid,
                    json.dumps(
                        {
                            "email": emailid,
                            "token": "",
                            "phone": phone,
                            "missionCode": country,
                            "email_server": "54.177.132.64",
                            "email_type": "local_email",
                            "redis": "aws",
                            "redis_name": "vfs_accounts",
                            "countryCode": "chn",
                            "redis_key": emailid,
                            "get_otp_time": int(time.time()),
                        }
                    ),
                )
                return {"email": emailid, "phone": phone}
            else:
                logger.error(f"账号注册失败 | 邮箱: {emailid} | 错误: {data.get('error')}")
                return False
        else:
            logger.error(f"账号注册失败 | 邮箱: {emailid} | HTTP状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"账号注册异常 | 邮箱: {emailid} | 异常: {str(e)}")
        return False


def get_phone_status(phone):
    """获取手机号状态"""
    url = f"http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == "0":
            return True
        elif data.get("msg") == "指定专属码手机号不在线":
            return "remove_phone"
        else:
            return False
    return False


def get_sms_code(phone):
    """获取短信验证码"""
    url = f"http://api.haozhuma.com/sms/?api=getMessage&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)
    if response.status_code == 200:
        data = response.json()
        if data.get("yzm"):
            return data.get("yzm")
    return ""


def login_and_get_token(user, country, capResult):
    """登录并获取Token"""
    try:
        logger.info(f"开始登录 | 邮箱: {user['email']} | 国家: {country}")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "username": user.get('email'),
            "password": encryption("Kq123456@"),
        }

        delegate = get_delegate()
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['firefox133', 'firefox135']),
            verify=False,
            timeout=60,
        )

        if response.status_code == 200:
            res = response.json()
            if res.get("error") != None:
                logger.warning(f"登录失败 | 邮箱: {user['email']} | 错误: {res.get('error')}")
                return False
            elif res.get("accessToken") != None:
                # 直接获取到token
                update_token_time = int(time.time())
                user["token"] = res.get("accessToken")
                user["updateTokenTime"] = update_token_time
                user["phone"] = res.get("contactNumber")
                user['ltsn'] = response.headers.get('set-cookie').split(';')[0] if response.headers.get('set-cookie') else ""
                logger.info(f"Token获取成功 | 邮箱: {user['email']} | Token: {res['accessToken'][:20]}... | 手机: {res.get('contactNumber')}")
                return "Token"
            else:
                # 需要OTP验证
                user["phone"] = res.get("contactNumber")
                logger.info(f"需要OTP验证 | 邮箱: {user.get('email')} | 手机: {res.get('contactNumber')}")
                return "SMS"
        else:
            logger.error(f"登录失败 | 邮箱: {user.get('email')} | HTTP状态码: {response.status_code}")
        return False
    except Exception as e:
        logger.error(f"登录异常 | 邮箱: {user['email']} | 异常: {str(e)}")
        return False


def validate_otp(user, country, otp, capResult):
    """验证OTP获取Token"""
    try:
        logger.info(f"开始OTP验证 | 邮箱: {user['email']} | OTP: {otp}")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }

        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "otp": str(otp),
            "username": user["email"],
            "password": encryption("Kq123456@"),
        }

        delegate = get_delegate()
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['firefox133', 'firefox135']),
            verify=False,
            timeout=60,
        )

        if response.status_code == 200:
            data = response.json()
            if data["error"] is None:
                update_token_time = int(time.time())
                user["token"] = data["accessToken"]
                user["updateTokenTime"] = update_token_time
                user["phone"] = data.get("contactNumber")
                user['ltsn'] = response.headers.get('set-cookie').split(';')[0] if response.headers.get('set-cookie') else ""
                logger.info(f"OTP验证成功 | 邮箱: {user['email']} | Token: {data['accessToken'][:20]}...")
                return True
            else:
                logger.warning(f"OTP验证失败 | 邮箱: {user['email']} | 错误: {data.get('error')}")
                return False
        else:
            logger.error(f"OTP验证失败 | 邮箱: {user['email']} | HTTP状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"OTP验证异常 | 邮箱: {user['email']} | 异常: {str(e)}")
        return False


def get_needed_countries():
    """从vfs_user中获取需要注册账号的国家列表"""
    try:
        all_orders = redis_client.hgetall('vfs_user')
        needed_countries = set()

        for order_data in all_orders:
            try:
                if isinstance(order_data, str):
                    order_data = json.loads(order_data)

                if 'visa_info' in order_data:
                    mission_code = order_data['visa_info'].get('mission_code')
                    if mission_code:
                        country_code = mission_code.lower()
                        needed_countries.add(country_code)
                        logger.debug(f"订单需求分析 | 订单: {order_data.get('order_id', 'unknown')} | 需要国家: {country_code}")

            except json.JSONDecodeError:
                continue
            except Exception as e:
                logger.warning(f"处理订单数据失败 | 异常: {str(e)}")
                continue

        # 排除不需要注册的国家
        excluded_countries = {'spain', 'deu'}  # 西班牙、德国
        needed_countries = needed_countries - excluded_countries

        logger.info(f"国家需求分析完成 | 需要注册账号的国家: {list(needed_countries)}")
        return list(needed_countries)

    except Exception as e:
        logger.error(f"获取需要注册的国家列表失败 | 异常: {str(e)}")
        return []


def get_country_account_count(country):
    """获取指定国家的账号数量（包括已激活和等待激活的账号）"""
    try:
        # 统计已激活的账号
        all_accounts = redis_client.hgetall("vfs_accounts")
        activated_count = 0
        for account_data in all_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)
                if account_data.get('missionCode') == country:
                    activated_count += 1
            except json.JSONDecodeError:
                continue

        # 统计等待激活的账号
        wait_active_accounts = redis_client.hgetall("waitactive")
        waiting_count = 0
        current_time = int(time.time())

        for account_data in wait_active_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)
                # 检查是否是目标国家且在24小时内注册的
                if (account_data.get('missionCode') == country and
                        current_time - account_data.get('get_otp_time', 0) < 86400):  # 24小时内
                    waiting_count += 1
            except json.JSONDecodeError:
                continue

        total_count = activated_count + waiting_count
        logger.debug(f"账号统计 | 国家: {country} | 已激活: {activated_count} | 等待激活: {waiting_count} | 总计: {total_count}")
        return total_count
    except Exception as e:
        logger.error(f"获取账号数量失败 | 国家: {country} | 异常: {str(e)}")
        return 0


def get_accounts_need_refresh():
    """获取需要刷新Token的账号"""
    try:
        all_accounts = redis_client.hgetall("vfs_accounts")
        need_refresh = []
        current_time = int(time.time())

        for account_data in all_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                # 检查是否需要刷新（token过期时间小于3400秒）
                if (not account_data.get("updateTokenTime") or
                        current_time - account_data.get("updateTokenTime", 0) > 3400):
                    need_refresh.append(account_data)
            except json.JSONDecodeError:
                continue

        return need_refresh
    except Exception as e:
        logger.error(f"获取需要刷新的账号失败 | 异常: {str(e)}")
        return []


def register_account_for_country(country):
    """为指定国家注册账号"""
    try:
        logger.info(f"开始注册账号 | 国家: {country}")

        # 获取验证码
        taskid = None
        for attempt in range(3):
            taskid = createTask(country)
            if taskid:
                logger.info(f"验证码任务创建成功 | 国家: {country} | 任务ID: {taskid} | 尝试: {attempt + 1}/3")
                break
            logger.warning(f"验证码任务创建失败 | 国家: {country} | 尝试: {attempt + 1}/3")
            time.sleep(2)

        if not taskid:
            logger.error(f"验证码任务创建失败 | 国家: {country} | 已尝试3次")
            return False

        time.sleep(3)
        capResult = None
        for attempt in range(10):
            capResult = get_result(taskid)
            logger.info(f"验证码获取中 | 国家: {country} | 任务ID: {taskid} | 尝试: {attempt + 1}/10")
            if capResult:
                logger.info(f"验证码获取成功 | 国家: {country} | 任务ID: {taskid} | 尝试: {attempt + 1}/10")
                break
            time.sleep(5)

        if not capResult:
            logger.error(f"验证码获取失败 | 国家: {country} | 任务ID: {taskid} | 已尝试10次")
            return False

        # 注册账号
        result = register_account(country, capResult)
        if result:
            logger.info(f"账号注册流程完成 | 国家: {country} | 结果: 成功")
        else:
            logger.warning(f"账号注册流程完成 | 国家: {country} | 结果: 失败")
        return result
    except Exception as e:
        logger.error(f"注册账号异常 | 国家: {country} | 异常: {str(e)}")
        return False


def refresh_account_token(user):
    """刷新账号Token"""
    try:
        country = user.get('missionCode')
        if not country:
            logger.error(f"账号缺少国家信息 | 邮箱: {user.get('email')}")
            return False

        # 获取验证码
        taskid = None
        for _ in range(3):
            taskid = createTask(country)
            if taskid:
                break
            time.sleep(2)

        if not taskid:
            logger.error(f"无法获取验证码任务 | 邮箱: {user.get('email')}")
            return False

        time.sleep(3)
        capResult = None
        for _ in range(10):
            capResult = get_result(taskid)
            if capResult:
                break
            time.sleep(2)

        if not capResult:
            logger.error(f"验证码获取失败 | 邮箱: {user.get('email')}")
            return False

        # 尝试登录
        login_result = login_and_get_token(user, country, capResult)
        if login_result == "Token":
            # 直接获取到Token，更新到vfs_accounts
            redis_client.hset("vfs_accounts", user['email'], json.dumps(user))
            return True
        elif login_result == "SMS":
            # 需要短信验证
            if not user.get("phone"):
                logger.error(f"账号缺少手机号 | 邮箱: {user.get('email')}")
                return False

            logger.info(f"开始OTP验证流程 | 邮箱: {user.get('email')}")

            # 获取短信验证码
            phone_result = get_phone_status(user.get("phone"))
            if not phone_result:
                remove_failed_account(user, "手机号状态检查失败")
                logger.warning(f"手机号状态检查失败 | 手机号: {user.get('phone')}")
                return False

            time.sleep(5)  # 等待短信到达
            otp_code = None

            # 获取OTP验证码
            for otp_attempt in range(10):
                logger.info(f"OTP验证码获取 | 邮箱: {user.get('email')} | 次数: {otp_attempt + 1}")
                otp_code = get_sms_code(user.get("phone"))
                if otp_code and len(otp_code) == 6:
                    logger.info(f"OTP验证码获取成功 | 邮箱: {user.get('email')} | 验证码: {otp_code}")
                    break
                time.sleep(5)  # 每次查询间隔5秒

            # 取消手机号接收
            cancel_url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={user.get('phone')}"
            requests.get(cancel_url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False, timeout=60)

            if not otp_code or len(otp_code) != 6:
                logger.error(f"OTP验证码获取最终失败 | 邮箱: {user.get('email')} | 已重试10次")
                # 删除失效账号
                remove_failed_account(user, "OTP验证码获取重试10次失败")
                return False

            # 获取验证码任务
            taskid = None
            for cap_attempt in range(3):
                taskid = createTask(country)
                if taskid:
                    break
                time.sleep(2)

            if not taskid:
                logger.warning(f"验证码任务创建失败 | 邮箱: {user.get('email')}")
                return False

            time.sleep(5)  # 等待验证码任务处理
            capResult = None
            for cap_result_attempt in range(10):
                capResult = get_result(taskid)
                if capResult:
                    break
                time.sleep(2)

            if not capResult:
                logger.error(f"验证码获取最终失败 | 邮箱: {user.get('email')} | 已重试10次")
                return False

            for otp_attempt in range(10):
                # 验证OTP
                logger.info(f"OTP验证中 | 邮箱: {user.get('email')} | 重试次数: {otp_attempt + 1}")
                otp_validation_result = validate_otp(user, country, otp_code, capResult)
                if otp_validation_result:
                    logger.info(f"OTP验证成功 | 邮箱: {user.get('email')} | 重试次数: {otp_attempt + 1}")
                    # 更新到vfs_accounts
                    redis_client.hset("vfs_accounts", user['email'], json.dumps(user))
                    return True
                time.sleep(1)  # 每次查询间隔5秒

            return False
        else:
            return False

    except Exception as e:
        logger.error(f"刷新Token异常 | 邮箱: {user.get('email', 'unknown')} | 异常: {str(e)}")
        return False


def remove_failed_account(user, reason="登录重试失败"):
    """删除失效账号"""
    try:
        email = user.get('email', 'unknown')
        country = user.get('missionCode', 'unknown')

        # 从vfs_accounts中删除
        redis_client.hdel("vfs_accounts", email)

        # 从waitactive中删除（如果存在）
        redis_client.hdel("waitactive", email)

        logger.warning(f"删除失效账号 | 邮箱: {email} | 国家: {country} | 原因: {reason}")
        return True

    except Exception as e:
        logger.error(f"删除失效账号失败 | 邮箱: {user.get('email', 'unknown')} | 异常: {str(e)}")
        return False


def clean_expired_waitactive():
    """清理过期的等待激活账号（超过24小时）"""
    try:
        # 获取所有等待激活的账号键
        wait_active_keys = redis_client.hgetallkey("waitactive")
        current_time = int(time.time())
        cleaned_count = 0

        for email in wait_active_keys:
            try:
                # 获取单个账号数据
                account_data_str = redis_client.hget("waitactive", email)
                if not account_data_str:
                    continue

                if isinstance(account_data_str, str):
                    account_data = json.loads(account_data_str)
                else:
                    account_data = account_data_str

                # 检查是否超过24小时
                get_otp_time = account_data.get('get_otp_time', 0)
                if current_time - get_otp_time > 86400:  # 24小时
                    redis_client.hdel("waitactive", email)
                    cleaned_count += 1
                    logger.debug(f"清理过期等待激活账号 | 邮箱: {email} | 注册时间: {get_otp_time}")

            except json.JSONDecodeError:
                # 清理无效数据
                redis_client.hdel("waitactive", email)
                cleaned_count += 1
                logger.debug(f"清理无效JSON数据 | 邮箱: {email}")
            except Exception as e:
                logger.warning(f"处理等待激活账号失败 | 邮箱: {email} | 异常: {str(e)}")

        if cleaned_count > 0:
            logger.info(f"清理过期等待激活账号完成 | 清理数量: {cleaned_count}")

    except Exception as e:
        logger.error(f"清理过期等待激活账号失败 | 异常: {str(e)}")


def show_account_statistics():
    """显示账号统计信息"""
    try:
        # 先清理过期的等待激活账号
        clean_expired_waitactive()

        all_accounts = redis_client.hgetall("vfs_accounts")
        wait_active_accounts = redis_client.hgetall("waitactive")
        country_stats = {}
        current_time = int(time.time())

        # 统计已激活账号
        for account_data in all_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                country = account_data.get('missionCode', 'unknown')
                if country not in country_stats:
                    country_stats[country] = {'activated': 0, 'waiting': 0, 'active': 0, 'need_refresh': 0}

                country_stats[country]['activated'] += 1

                if (account_data.get('token') and
                    account_data.get('updateTokenTime') and
                        current_time - account_data.get('updateTokenTime', 0) < 3400):
                    country_stats[country]['active'] += 1
                else:
                    country_stats[country]['need_refresh'] += 1

            except json.JSONDecodeError:
                continue

        # 统计等待激活账号
        for account_data in wait_active_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                country = account_data.get('missionCode', 'unknown')
                if country not in country_stats:
                    country_stats[country] = {'activated': 0, 'waiting': 0, 'active': 0, 'need_refresh': 0}

                country_stats[country]['waiting'] += 1

            except json.JSONDecodeError:
                continue

        # 计算总计
        total_activated = sum(stats['activated'] for stats in country_stats.values())
        total_waiting = sum(stats['waiting'] for stats in country_stats.values())
        total_active = sum(stats['active'] for stats in country_stats.values())
        total_need_refresh = sum(stats['need_refresh'] for stats in country_stats.values())

        logger.info("=" * 60)
        logger.info("VFS账号统计报告")
        logger.info("=" * 60)
        logger.info(f"总体统计 | 已激活: {total_activated} | 等待激活: {total_waiting} | 有效Token: {total_active} | 需刷新: {total_need_refresh}")
        logger.info("-" * 60)

        for country, stats in sorted(country_stats.items()):
            total_country = stats['activated'] + stats['waiting']
            active_rate = (stats['active'] / stats['activated'] * 100) if stats['activated'] > 0 else 0
            logger.info(f"国家: {country.upper():8} | 总计: {total_country:2} | 已激活: {stats['activated']:2} | 等待: {stats['waiting']:2} | 有效: {stats['active']:2} | 有效率: {active_rate:5.1f}%")

        logger.info("=" * 60)

        return country_stats
    except Exception as e:
        logger.error(f"获取账号统计失败 | 异常: {str(e)}")
        return {}


def registration_worker():
    """账号注册工作线程"""
    logger.info("注册工作线程启动 | 检查周期: 60秒 | 目标账号数: 3个/国家")

    # 记录每个国家最后一次注册时间，避免频繁注册
    last_registration_time = {}

    while True:
        try:
            needed_countries = get_needed_countries()
            if not needed_countries:
                logger.debug("当前没有需要注册账号的国家")

            current_time = int(time.time())
            registration_happened = False

            for country in needed_countries:
                current_count = get_country_account_count(country)

                # 检查是否需要注册且距离上次注册已超过10分钟
                last_reg_time = last_registration_time.get(country, 0)
                time_since_last_reg = current_time - last_reg_time

                if current_count < 3:
                    if time_since_last_reg < 600:  # 10分钟内不重复注册
                        logger.debug(f"注册冷却中 | 国家: {country} | 距离上次注册: {time_since_last_reg}秒 | 冷却时间: 600秒")
                        continue

                    need_count = min(2, 3 - current_count)
                    logger.info(f"注册需求检测 | 国家: {country} | 当前账号: {current_count} | 需要注册: {need_count}")

                    # 并行注册账号
                    threads = []
                    for i in range(need_count):
                        thread = threading.Thread(
                            target=register_account_for_country,
                            args=(country,),
                            name=f"Register-{country}-{i+1}"
                        )
                        thread.daemon = True
                        thread.start()
                        threads.append(thread)

                    logger.info(f"注册任务启动 | 国家: {country} | 并发任务数: {need_count}")

                    # 更新最后注册时间
                    last_registration_time[country] = current_time
                    registration_happened = True

                    # 等待注册线程完成，避免过快启动下一批
                    for thread in threads:
                        thread.join(timeout=60)  # 最多等待60秒

                else:
                    logger.debug(f"账号充足 | 国家: {country} | 当前账号数: {current_count}")

            # 如果有注册活动，额外等待一段时间让账号激活
            if registration_happened:
                logger.info("注册任务完成，等待30秒让账号有时间激活...")
                time.sleep(30)  # 等待30秒

            # 每隔60秒检查一次注册需求
            time.sleep(60)

        except Exception as e:
            logger.error(f"注册工作线程异常 | 异常: {str(e)}")
            time.sleep(60)


def process_refresh_task(user):
    """处理单个用户的Token刷新任务"""
    email = user.get('email', 'unknown')
    country = user.get('missionCode', 'unknown')

    # 获取账号+国家的专用锁
    refresh_lock = get_refresh_lock(email, country)

    # 尝试获取锁，如果获取不到说明该账号正在刷新中
    if not refresh_lock.acquire(blocking=False):
        logger.warning(f"Token刷新跳过 | 邮箱: {email} | 国家: {country} | 原因: 该账号正在刷新中")
        return

    try:
        logger.info(f"开始Token刷新 | 邮箱: {email} | 国家: {country}")
        result = refresh_account_token(user)
        if result:
            logger.info(f"Token刷新成功 | 邮箱: {email} | 国家: {country}")
        else:
            logger.warning(f"Token刷新失败 | 邮箱: {email} | 国家: {country}")
    except Exception as e:
        logger.error(f"Token刷新异常 | 邮箱: {email} | 国家: {country} | 异常: {str(e)}")
    finally:
        # 确保释放锁
        refresh_lock.release()
        logger.debug(f"Token刷新锁释放 | 邮箱: {email} | 国家: {country}")


def token_refresh_worker():
    """Token刷新工作线程"""
    logger.info("Token刷新工作线程启动 | 检查周期: 60秒")

    while True:
        try:
            need_refresh = get_accounts_need_refresh()
            if need_refresh:
                # 过滤掉正在刷新中的账号
                filtered_accounts = []
                for user in need_refresh:
                    email = user.get('email', 'unknown')
                    country = user.get('missionCode', 'unknown')

                    if not is_refresh_in_progress(email, country):
                        filtered_accounts.append(user)
                    else:
                        logger.debug(f"跳过正在刷新的账号 | 邮箱: {email} | 国家: {country}")

                if filtered_accounts:
                    logger.info(f"Token刷新需求检测 | 总需求: {len(need_refresh)} | 过滤后: {len(filtered_accounts)}")

                    # 为过滤后的用户启动刷新线程
                    threads = []
                    for i, user in enumerate(filtered_accounts):
                        email = user.get('email', f'user_{i}')
                        thread = threading.Thread(
                            target=process_refresh_task,
                            args=(user,),
                            name=f"Refresh-{email.split('@')[0]}"
                        )
                        thread.daemon = True
                        thread.start()
                        threads.append(thread)

                    logger.info(f"Token刷新任务启动 | 并发任务数: {len(filtered_accounts)}")
                else:
                    logger.debug("所有需要刷新的账号都在刷新中，跳过本轮")
            else:
                logger.debug("当前没有需要刷新Token的账号")

            # 每隔60秒检查一次刷新需求（增加间隔，减少冲突）
            time.sleep(60)

        except Exception as e:
            logger.error(f"Token刷新工作线程异常 | 异常: {str(e)}")
            time.sleep(60)


def statistics_worker():
    """统计信息工作线程"""
    logger.info("统计信息工作线程启动 | 显示周期: 60秒")

    while True:
        try:
            show_account_statistics()
            # 每隔60秒显示一次统计信息
            time.sleep(60)
        except Exception as e:
            logger.error(f"统计工作线程异常 | 异常: {str(e)}")
            time.sleep(60)


def main():
    """主函数 - 启动异步工作线程"""
    logger.info("=" * 60)
    logger.info("VFS账号管理器启动")
    logger.info("功能: 异步账号注册 + 异步Token刷新")
    logger.info("版本: v2.0 (异步版本)")
    logger.info("=" * 60)

    try:
        # 启动注册工作线程
        registration_thread = threading.Thread(target=registration_worker, name="RegistrationWorker")
        registration_thread.daemon = True
        registration_thread.start()
        logger.info("注册工作线程已启动")

        # 启动Token刷新工作线程
        refresh_thread = threading.Thread(target=token_refresh_worker, name="TokenRefreshWorker")
        refresh_thread.daemon = True
        refresh_thread.start()
        logger.info("Token刷新工作线程已启动")

        # 启动统计信息工作线程
        stats_thread = threading.Thread(target=statistics_worker, name="StatisticsWorker")
        stats_thread.daemon = True
        stats_thread.start()
        logger.info("统计信息工作线程已启动")

        logger.info("所有工作线程启动完成，系统进入运行状态")
        logger.info("按 Ctrl+C 停止服务")

        # 主线程保持运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务...")
        logger.info("VFS账号管理器已停止")
    except Exception as e:
        logger.error(f"主线程异常 | 异常: {str(e)}")
        logger.error("VFS账号管理器异常退出")


if __name__ == "__main__":
    main()
