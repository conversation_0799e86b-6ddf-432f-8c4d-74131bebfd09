#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VFS URN Manager 使用示例
"""

import json
import time
from RedisClient import RedisClient

def example_trigger_refresh():
    """示例：触发URN刷新"""
    print("=" * 60)
    print("示例：触发URN刷新")
    print("=" * 60)
    
    redis_client = RedisClient()
    
    # 发送刷新消息到Redis频道
    # 消息格式: mission_code--center_code--visa_code
    refresh_messages = [
        "ita--SHI--SSFTME",  # 意大利上海VIP签证
        "prt--POBE--TOU",    # 葡萄牙北京旅游签证
        "fra--FRBE--BUP",    # 法国北京商务签证
    ]
    
    for message in refresh_messages:
        print(f"发送刷新消息: {message}")
        redis_client.publish("vfs_urn_refresh", message)
        time.sleep(1)  # 避免过快发送
    
    print("刷新消息发送完成")

def example_check_orders():
    """示例：检查订单状态"""
    print("\n" + "=" * 60)
    print("示例：检查订单状态")
    print("=" * 60)
    
    redis_client = RedisClient()
    
    # 获取所有订单
    all_orders = redis_client.hgetall('vfs_user')
    
    print(f"总订单数: {len(all_orders)}")
    
    # 统计订单状态
    status_count = {}
    urn_count = 0
    otp_count = 0
    
    for order_data in all_orders:
        try:
            if isinstance(order_data, str):
                order_data = json.loads(order_data)
            
            status = order_data.get('status', 'unknown')
            status_count[status] = status_count.get(status, 0) + 1
            
            if order_data.get('urn'):
                urn_count += 1
            
            if order_data.get('update_otp_time'):
                otp_count += 1
                
        except json.JSONDecodeError:
            continue
    
    print("\n订单状态统计:")
    for status, count in status_count.items():
        print(f"  {status}: {count}")
    
    print(f"\n已有URN的订单: {urn_count}")
    print(f"已完成OTP的订单: {otp_count}")

def example_check_accounts():
    """示例：检查账号状态"""
    print("\n" + "=" * 60)
    print("示例：检查账号状态")
    print("=" * 60)
    
    redis_client = RedisClient()
    
    # 获取所有账号
    all_accounts = redis_client.hgetall('vfs_accounts')
    
    print(f"总账号数: {len(all_accounts)}")
    
    # 统计账号状态
    country_count = {}
    active_count = 0
    current_time = int(time.time())
    
    for account_data in all_accounts:
        try:
            if isinstance(account_data, str):
                account_data = json.loads(account_data)
            
            mission_code = account_data.get('missionCode', 'unknown')
            country_count[mission_code] = country_count.get(mission_code, 0) + 1
            
            # 检查账号是否活跃
            if (account_data.get('token') and
                account_data.get('updateTokenTime') and
                current_time - account_data.get('updateTokenTime') < 6000):
                active_count += 1
                
        except json.JSONDecodeError:
            continue
    
    print(f"\n活跃账号数: {active_count}")
    print("\n按国家分布:")
    for country, count in sorted(country_count.items()):
        print(f"  {country}: {count}")

def example_add_test_order():
    """示例：添加测试订单"""
    print("\n" + "=" * 60)
    print("示例：添加测试订单")
    print("=" * 60)
    
    redis_client = RedisClient()
    
    # 创建测试订单
    test_order = {
        "order_id": f"TEST{int(time.time())}",
        "visa_info": {
            "mission_code": "prt",
            "center_code": "POBE",
            "visa_type": "旅游签证",
            "visa_code": "TOU"
        },
        "clients": [{
            "name": "测试用户",
            "surname_pinyin": "TEST",
            "firstname_pinyin": "USER",
            "passport": "T12345678",
            "dob": "1990-01-01",
            "passport_expire": "2030-12-31",
            "gender": "男",
            "nationality": "CHN"
        }],
        "order_info": {
            "accept_vip": False,
            "accept_next_day": True,
            "travel_date": "",
            "customer": "测试客户",
            "price": "0",
            "remark": "测试订单",
            "date_ranges": [{
                "start_date": "2025-08-01",
                "end_date": "2025-08-31"
            }]
        },
        "status": "pending",
        "created_by": "test_script",
        "created_at": int(time.time()),
        "updated_at": int(time.time())
    }
    
    # 保存到Redis
    order_id = test_order["order_id"]
    redis_client.hset('vfs_user', order_id, json.dumps(test_order, ensure_ascii=False))
    
    print(f"测试订单已创建: {order_id}")
    print(f"签证类型: {test_order['visa_info']['mission_code']}/{test_order['visa_info']['center_code']}/{test_order['visa_info']['visa_code']}")
    
    # 触发刷新
    refresh_message = f"{test_order['visa_info']['mission_code']}--{test_order['visa_info']['center_code']}--{test_order['visa_info']['visa_code']}"
    print(f"触发刷新: {refresh_message}")
    redis_client.publish("vfs_urn_refresh", refresh_message)

def main():
    """主函数"""
    print("VFS URN Manager 使用示例")
    print("=" * 60)
    
    try:
        # 检查当前状态
        example_check_orders()
        example_check_accounts()
        
        # 添加测试订单（可选）
        # example_add_test_order()
        
        # 触发刷新
        # example_trigger_refresh()
        
    except Exception as e:
        print(f"示例执行失败: {e}")

if __name__ == "__main__":
    main()
