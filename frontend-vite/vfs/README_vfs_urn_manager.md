# VFS URN Manager 使用说明

## 概述

`vfs_urn_manager.py` 是重构后的 VFS 签证申请 URN 刷新管理器，用于自动化处理签证申请的 URN（Unique Reference Number）获取和 OTP（One-Time Password）验证。

## 主要功能

1. **URN 持续刷新** - 持续自动为待处理订单获取 URN（有效期 10000 秒）
2. **智能等待策略** - 无订单或处理失败时等待 5 分钟，成功处理后等待 30 秒
3. **OCR 护照识别** - 根据配置自动上传护照图片进行 OCR 识别
4. **智能 OTP 获取** - 根据 vfs_center_data 配置自动判断是否需要获取 OTP 验证码
5. **多渠道 OTP 支持** - 支持 EMAIL 和 SMS 两种 OTP 获取方式
6. **自动 OTP 验证** - 获取验证码后自动完成验证流程
7. **等待列表支持** - 根据配置启用等待列表功能
8. **多线程处理** - 支持并发处理多个订单
9. **统计信息** - 定期显示订单和账号统计信息
10. **错误处理** - 完善的异常处理和重试机制

## 数据格式

### vfs_user 订单数据格式

```json
{
  "order_id": "2025071015550721622",
  "visa_info": {
    "mission_code": "ita",
    "center_code": "SHI",
    "visa_type": "Shanghai VIP",
    "visa_code": "SSFTME"
  },
  "clients": [
    {
      "name": "龙春忠",
      "surname_pinyin": "LONG",
      "firstname_pinyin": "CHUNZHONG",
      "passport": "*********",
      "dob": "1978-01-10",
      "passport_expire": "2033-09-12",
      "gender": "男",
      "nationality": "CHN"
    }
  ],
  "order_info": {
    "accept_vip": false,
    "accept_next_day": true,
    "travel_date": "",
    "customer": "1",
    "price": "2",
    "remark": "3",
    "date_ranges": [
      {
        "start_date": "2025-07-22",
        "end_date": "2025-07-25"
      }
    ]
  },
  "status": "pending",
  "created_by": "yuan",
  "created_at": **********,
  "updated_at": **********,
  "urn": "URN123456",
  "updateUrnTime": **********,
  "get_otp_time": **********,
  "update_otp_time": **********,
  "otp_validated": true,
  "otp_validation_time": **********
}
```

### vfs_accounts 账号数据格式

```json
{
  "email": "<EMAIL>",
  "token": "EAAAAH0xTqdpnPLgWCS0c+yo...",
  "phone": "***********",
  "missionCode": "che",
  "email_server": "*************",
  "email_type": "local_email",
  "redis": "aws",
  "redis_name": "vfs_accounts",
  "countryCode": "chn",
  "redis_key": "<EMAIL>",
  "get_otp_time": **********,
  "updateTokenTime": **********,
  "ltsn": "lt_sn=a267f712-13a2-408c-820e-dcbb5ce809ca"
}
```

## 使用方法

### 1. 直接运行（推荐）

```bash
cd frontend-vite/vfs
python vfs_urn_manager.py
```

程序将自动开始持续刷新 URN，无需手动触发。

### 2. 程序化调用

```python
from vfs_urn_manager import refresh_urn_for_mission, manual_refresh_all

# 刷新特定签证类型
refresh_urn_for_mission("ita", "SHI", "SSFTME")

# 手动刷新所有待处理订单
manual_refresh_all()
```

## 持续刷新机制

### 自动运行模式

程序启动后会自动进入持续刷新模式：

1. **扫描待处理订单** - 检查所有状态为 `pending` 且需要 URN 的订单
2. **按签证类型分组** - 将订单按 `mission_code--center_code--visa_code` 分组
3. **并发处理** - 为每种签证类型分配处理线程
4. **智能等待** - 根据处理结果决定等待时间

### 等待策略

- **无订单或全部失败**: 等待 5 分钟后重试
- **有成功处理**: 等待 30 秒后开始下一轮
- **异常情况**: 等待 1 分钟后重试

### URN 有效期判断

订单需要刷新 URN 的条件：

- 没有 URN (`urn` 字段为空)
- 没有更新时间 (`updateUrnTime` 字段为空)
- URN 已过期 (距离上次更新超过 10000 秒)

### 测试持续刷新

可以使用测试脚本验证持续刷新逻辑：

```bash
python test_continuous_refresh.py
```

## OCR 护照识别功能

### 功能概述

系统会根据 `vfs_center_data` 中的 `isOCREnable` 字段自动判断是否需要进行护照 OCR 识别。

### 判断逻辑

程序会检查签证类型配置中的 `isOCREnable` 字段：

- `true` - 需要上传护照图片进行 OCR 识别
- `false` - 不需要 OCR 识别，直接获取 URN

### OCR 处理流程

1. **检查配置** - 读取签证类型的 `isOCREnable` 配置
2. **获取护照图片** - 从 `clients[].passport_image` 字段获取图片文件名
3. **下载图片** - 调用 `http://120.27.241.45:5005/api/passport_image/{passport_image}` 获取图片
4. **上传识别** - 调用 VFS API 上传图片进行 OCR 识别
5. **保存结果** - 将识别结果的 GUID 保存到客户数据中

### OCR API 详情

**图片获取接口**:

```
GET http://120.27.241.45:5005/api/passport_image/{passport_image}
```

**护照上传接口**:

```
POST https://lift-apicn.vfsglobal.com/appointment/UploadApplicantDocument
```

**请求数据**:

```json
{
  "urn": "",
  "countryCode": "chn",
  "missionCode": "ita",
  "loginUser": "<EMAIL>",
  "languageCode": "zh-CN",
  "centerCode": "SHI",
  "visaCategoryCode": "TOU",
  "fileBytes": "base64_encoded_image",
  "selfiImageFileBytes": ""
}
```

### 错误处理

- **图片不存在** (404) - 记录警告，跳过该客户
- **OCR 识别失败** - 记录错误，停止处理该订单
- **网络错误** - 记录错误，重试机制

## OTP 判断逻辑

程序会根据 `vfs_center_data` 中的配置自动判断每种签证类型是否需要获取 OTP 验证码。

### 判断流程

1. **读取配置** - 从 `vfs_center_data` Redis hash 表中读取指定国家的配置
2. **查找签证类型** - 在配置的层级结构中查找匹配的签证类型
3. **检查标志** - 查看 `isApplicantOTPEnabled` 字段的值
4. **返回结果** - 返回是否需要 OTP 的布尔值

### 配置示例

```json
{
  "missionCode": "prt",
  "missionCodeName": "葡萄牙",
  "data": [
    {
      "sub": [
        {
          "sub": [
            {
              "code": "TOU",
              "name": "旅游",
              "isApplicantOTPEnabled": true
            },
            {
              "code": "NET",
              "name": "国家签证E类",
              "isApplicantOTPEnabled": false
            }
          ]
        }
      ],
      "isoCode": "POBE"
    }
  ]
}
```

### 测试 OTP 逻辑

可以使用提供的测试脚本验证 OTP 判断逻辑：

```bash
python test_otp_logic.py
```

### 测试 OCR 功能

可以使用测试脚本验证 OCR 护照识别功能：

```bash
python test_ocr_functionality.py
```

### 测试直接处理

可以使用测试脚本验证直接处理订单数据的逻辑：

```bash
python test_no_conversion.py
```

### 检查 OTP 状态

可以使用状态检查工具查看订单的 OTP 验证状态：

```bash
# 检查所有订单的OTP状态
python check_otp_status.py

# 检查特定订单的详细信息
python check_otp_status.py ORDER_ID
```

## 数据处理方式

### 直接处理订单数据

程序现在直接使用原始的订单数据进行处理，不再进行字段映射转换：

- **保持原始结构** - 直接使用 `vfs_user` 中的订单数据格式
- **无字段转换** - 不进行字段名映射，保持数据完整性
- **动态添加账号信息** - 在处理时动态添加账号相关字段
- **原始字段名** - 使用 `clients[0].name` 等原始字段名

### 处理流程

1. 从 `vfs_user` 读取订单数据
2. 动态添加账号信息（`loginUser`, `phone`, `email`）
3. 直接使用订单数据构建 VFS API 请求
4. 更新订单状态和结果

## OTP 获取和验证流程

### 完整流程

1. **触发 OTP 生成** - 调用 VFS API 触发 OTP 生成
2. **检查生成方式** - 根据返回的 `OTPGeneratedMethed` 字段判断获取方式
3. **获取验证码** - 根据方式从相应渠道获取验证码
4. **验证 OTP** - 使用获取到的验证码完成验证

### 支持的 OTP 方式

#### EMAIL 方式

- **数据源**: Redis，key 为邮箱地址
- **轮询间隔**: 每 10 秒检查一次
- **最大等待**: 5 分钟（30 次尝试）

#### SMS 方式

- **数据源**: 短信平台 API
- **API 地址**: `http://api.haozhuma.com/sms/`
- **轮询间隔**: 每 10 秒检查一次
- **最大等待**: 5 分钟（30 次尝试）

### 验证流程

获取到验证码后，系统会自动调用验证接口：

```json
{
  "urn": "用户URN",
  "loginUser": "登录邮箱",
  "missionCode": "国家代码",
  "languageCode": "zh-CN",
  "countryCode": "chn",
  "centerCode": "中心代码",
  "OTP": "验证码",
  "otpAction": "VALIDATE"
}
```

验证成功标志：`isOTPValidated: true`

### OTP 验证状态标记

验证完成后，系统会在订单数据中添加以下标记：

- **`otp_validated`** - OTP 验证结果（true/false）
- **`otp_validation_time`** - OTP 验证完成时间戳
- **`update_otp_time`** - OTP 更新时间戳

```json
{
  "order_id": "2025071015550721622",
  "urn": "URN123456",
  "updateUrnTime": **********,
  "otp_validated": true,
  "otp_validation_time": **********,
  "update_otp_time": **********
}
```

## 配置说明

### Redis 配置

- 主机: 172.22.128.62
- 端口: 6379
- 密码: TicketsCache#2023
- 数据库: 0

### 关键 Redis 键

- `vfs_user` - 订单数据
- `vfs_accounts` - 账号数据
- `vfs_center_data` - 签证类型详细配置（用于判断是否需要 OTP）
- `fast_proxy` - 代理列表
- `rsa_str` - RSA 公钥
- `centerData` - 中心数据（国家和中心映射）

### 线程配置

- 工作线程数: 20
- 配置刷新间隔: 60 秒
- 统计信息显示间隔: 300 秒

## 日志说明

程序使用标准 Python logging 模块，日志级别为 INFO。主要日志类型：

- **INFO** - 正常操作信息（启动、处理、统计等）
- **WARNING** - 警告信息（账号不足、OTP 失败等）
- **ERROR** - 错误信息（网络错误、数据解析错误等）

## 监控和统计

程序每 5 分钟自动显示统计信息，包括：

1. **订单统计**

   - 总订单数
   - 待处理订单数
   - 已有 URN 订单数
   - 正在处理用户数
   - 队列中用户数

2. **账号统计**

   - 总账号数
   - 活跃账号数

3. **按国家分布**
   - 每个国家的订单数和账号数

## 错误处理

程序包含完善的错误处理机制：

1. **网络错误** - 自动重试和超时处理
2. **数据格式错误** - 跳过无效数据并记录日志
3. **账号问题** - 自动选择其他可用账号
4. **API 错误** - 根据错误类型进行不同处理

## 注意事项

1. **依赖项** - 确保安装所有必需的 Python 包
2. **Redis 连接** - 确保 Redis 服务器可访问
3. **代理配置** - 确保代理列表在 Redis 中正确配置
4. **RSA 密钥** - 确保 RSA 公钥在 Redis 中正确配置
5. **账号管理** - 定期检查账号状态和 token 有效性

## 故障排除

### 常见问题

1. **无可用账号**

   - 检查 vfs_accounts 中的账号数据
   - 确认账号 token 未过期
   - 检查账号的 missionCode 是否匹配

2. **URN 获取失败**

   - 检查网络连接和代理配置
   - 确认客户信息格式正确
   - 检查 VFS 服务器状态

3. **OTP 获取失败**
   - 检查账号 token 有效性
   - 确认 URN 存在且有效
   - 检查网络连接

### 调试模式

可以通过修改日志级别来获取更详细的调试信息：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 版本历史

- **v2.4** - OCR 护照识别版本
  - **OCR 护照识别**: 根据 `isOCREnable` 配置自动上传护照图片进行识别
  - **等待列表支持**: 根据 `iswaitlist` 配置启用等待列表功能
  - **多配置字段**: 支持读取 `isApplicantOTPEnabled`、`iswaitlist`、`isOCREnable` 三个配置字段
  - **护照图片处理**: 自动从图片服务器获取护照图片并上传到 VFS 进行 OCR
  - **GUID 管理**: 自动保存 OCR 识别结果的 GUID 到客户数据
  - 添加 OCR 功能测试脚本
- **v2.3** - 持续刷新版本
  - **持续自动刷新**: 移除 Redis 消息依赖，程序自动持续刷新 URN
  - **智能等待策略**: 无订单或失败时等待 5 分钟，成功时等待 30 秒
  - **自动运行模式**: 启动后无需手动触发，自动检测和处理订单
  - 添加持续刷新测试脚本
- **v2.2** - 完整 OTP 流程版本
  - 完整的 OTP 获取和验证流程
  - 支持 EMAIL 和 SMS 两种 OTP 获取方式
  - 自动轮询获取验证码（最多等待 5 分钟）
  - 自动完成 OTP 验证流程
  - **OTP 验证状态标记**: 在订单数据中添加验证结果标记
  - 统计信息显示 OTP 验证成功/失败数量
  - **移除字段转换**: 直接使用原始订单数据，不进行字段映射转换
  - 添加 OTP 流程测试脚本和状态检查工具
- **v2.1** - 优化版本
  - URN 有效期调整为 10000 秒
  - 智能 OTP 判断：根据 vfs_center_data 配置自动判断是否需要 OTP
  - 支持从 vfs_center_data 读取详细签证类型配置
  - 添加 OTP 逻辑测试脚本
- **v2.0** - 重构版本，支持新的数据格式
- **v1.0** - 原始版本（common_refresh_urn.py）
