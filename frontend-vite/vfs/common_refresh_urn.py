# -*- coding: utf-8 -*-
import threading
import queue
from queue import Queue
from curl_cffi import requests
import time as tm
import json
import random
import re
from RedisClientAWS import RedisClient
from datetime import datetime, time
import string
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding

redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


delegate = json.loads(redis_client.get("fast_proxy"))

# 正在处理或等待处理的用户集合
processing_users = set()


center_data = json.loads(redis_client.get("centerData"))

all_missionCode_map = {}
all_centerCode_map = {}
for country_data in center_data:
    all_missionCode_map[country_data.get('missionCode')] = country_data.get('missionCodeName')
    for center in country_data.get('data'):
        all_centerCode_map[center.get('isoCode')] = center.get('centerName')


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def is_earlier_than(time_a_str, time_b_str):
    # 将时间字符串解析为datetime对象
    time_format = "%d/%m/%Y"
    time_a = datetime.strptime(time_a_str, time_format)
    time_b = datetime.strptime(time_b_str, time_format)

    # 比较时间
    if time_a < time_b:
        return True
    else:
        return False


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def get_phone(phone):
    url = f"http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=67227&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        print(data)
        if data.get("code") == "0":
            return True
        else:
            return False
    return False


def get_otp(user):
    try:
        loginUsers = [item for item in redis_client.hgetall(f"{user.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(tm.time()) - item.get('updateTokenTime') < 6000]
        print('开始获取otp', user)
        if len(loginUsers) != 0:
            account = random.choice(loginUsers)
            url = "https://lift-apicn.vfsglobal.com/appointment/applicantotp"
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "Authorize": account["token"],
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "route": f"chn/zh/{user['missionCode']}",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
                "cookie": account['ltsn']
            }
            data = {
                "urn": user["urn"],
                "loginUser": account.get("email"),
                "lOGiNUser": user["loginUser"],
                "missionCode": user["missionCode"],
                "countryCode": "chn",
                "centerCode": user["centerCode"],
                "captcha_version": "",
                "captcha_api_key": "",
                "OTP": "",
                "otpAction": "GENERATE",
                "cultureCode": "zh-CN",
            }
            if (not user.get('refresh_urn_times') or int(user.get('refresh_urn_times')) < 4) and user.get('ips') != None and len(user.get('ips')) > 0:
                proxy = random.choice(user.get('ips'))
                if not user.get('refresh_urn_times'):
                    user['refresh_urn_times'] = 1
                else:
                    user['refresh_urn_times'] = int(user['refresh_urn_times']) + 1
                redis_client.hset(
                    f"{user.get('missionCode')}UserDatas",
                    f"{user['centerCode']}-{user['passportNO']}",
                    json.dumps({**user}),
                )
            else:
                proxy = random.choice(delegate)
            proxies = {
                "http": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
                "https": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
            }
            response = requests.post(
                url,
                json=data,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
                verify=False,
            )
            if response.status_code == 200:
                res = response.json()
                print(res)
                if res.get("isOTPGenerated") == True:
                    print(f"客户{user.get('chnname')}成功获取OTP", res)
                    return True
                return False
            else:
                print(response.status_code)
        return False
    except Exception as e:
        print(f"获取otp失败，可以重试{e}")
        user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
        if user_id in processing_users:
            processing_users.remove(user_id)
        return False


def attempt_get_otp(user, max_retries, retry_interval):
    for _ in range(max_retries):
        result = get_otp(user)
        if result:
            return result
        tm.sleep(retry_interval)
    return False


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def get_urn(user):
    loginUsers = [item for item in redis_client.hgetall(f"{user.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(tm.time()) - item.get('updateTokenTime') < 6000 and int(tm.time()) - item.get('updateTokenTime') > 60]
    if len(loginUsers) != 0:
        account = random.choice(loginUsers)
        try:
            r_auth = f"EAAAAN{generate_random_string(597)}="
            url = "https://lift-apicn.vfsglobal.com/appointment/applicants"
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "Authorize": r_auth,
                "authorize": account["token"],
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "route": f"chn/zh/{user['missionCode']}",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
                "cookie": account['ltsn']
            }
            user['loginUser'] = account.get('email')
            user['phone'] = account.get('phone')
            user['email'] = account.get('email')
            if user.get('child') != None and len(user.get('child')) > 0:
                applicantList = [{"urn": "",
                                  "arn": "",
                                  "loginUser": user["loginUser"],
                                  "firstName": u["name"],
                                  "middleName": "",
                                  "lastName": u["xing"],
                                  "salutation": "",
                                  "gender": 1 if u["gender"] == "男" else 2,
                                  "nationalId": None,
                                  "VisaToken": None,
                                  "contactNumber": str(user["phone"]),
                                  "dialCode": "86",
                                  "passportNumber": u["passportNO"],
                                  "confirmPassportNumber": u["passportNO"],
                                  "passportExpirtyDate": u["expiredDT"],
                                  "dateOfBirth": u["birthday"],
                                  "emailId": user["loginUser"],
                                  "nationalityCode": (
                                      "CHN"
                                      if u.get("countryCode") == None
                                      else u.get("countryCode")
                                  ),
                                  "state": None,
                                  "city": None,
                                  "isEndorsedChild": False,
                                  "applicantType": 0,
                                  "addressline1": None,
                                  "addressline2": None,
                                  "pincode": None,
                                  "referenceNumber": None,
                                  "vlnNumber": None,
                                  "applicantGroupId": 0,
                                  "parentPassportNumber": "",
                                  "parentPassportExpiry": "",
                                  "dateOfDeparture": "11/05/2024",
                                  "helloVerifyNumber": "",
                                  "gwfNumber": "",
                                  "entryType": "",
                                  "eoiVisaType": "",
                                  "isAutoRefresh": True,
                                  "passportType": "",
                                  "employerContactNumber": "",
                                  "employerDialCode": "",
                                  "employerEmailId": "",
                                  "employerFirstName": "",
                                  "employerLastName": "",
                                  "familyReunificationCerificateNumber": "",
                                  "vfsReferenceNumber": "",
                                  "PVRequestRefNumber": "",
                                  "PVStatus": "",
                                  "PVStatusDescription": "",
                                  "PVCanAllowRetry": True,
                                  "PVisVerified": False,
                                  "ipAddress": None, } for u in user.get('child')]
            else:
                applicantList = [
                    {
                        "urn": "",
                        "arn": "",
                        "loginUser": user["loginUser"],
                        "firstName": user["name"],
                        "middleName": "",
                        "lastName": user["xing"],
                        "salutation": "",
                        "gender": 1 if user["gender"] == "男" else 2,
                        "nationalId": None,
                        "VisaToken": None,
                        "contactNumber": str(user["phone"]),
                        "dialCode": "86",
                        "passportNumber": user["passportNO"],
                        "confirmPassportNumber": user["passportNO"],
                        "passportExpirtyDate": user["expiredDT"],
                        "dateOfBirth": user["birthday"],
                        "emailId": user["loginUser"],
                        "nationalityCode": (
                            "CHN"
                            if user.get("countryCode") == None
                            else user.get("countryCode")
                        ),
                        "state": None,
                        "city": None,
                        "isEndorsedChild": False,
                        "applicantType": 0,
                        "addressline1": None,
                        "addressline2": None,
                        "pincode": None,
                        "referenceNumber": None,
                        "vlnNumber": None,
                        "applicantGroupId": 0,
                        "parentPassportNumber": "",
                        "parentPassportExpiry": "",
                        "dateOfDeparture": "11/05/2024",
                        "gwfNumber": "",
                        "isAutoRefresh": True,
                        "entryType": "",
                        "helloVerifyNumber": "",
                        "eoiVisaType": "",
                        "passportType": "",
                        "employerContactNumber": "",
                        "employerDialCode": "",
                        "employerEmailId": "",
                        "employerFirstName": "",
                        "employerLastName": "",
                        "familyReunificationCerificateNumber": "",
                        "vfsReferenceNumber": "",
                        "PVRequestRefNumber": "",
                        "PVStatus": "",
                        "PVStatusDescription": "",
                        "PVCanAllowRetry": True,
                        "PVisVerified": False,
                        "ipAddress": None,
                    }
                ]
            data = {
                "countryCode": "chn",
                "missionCode": user["missionCode"],
                "centerCode": user["centerCode"],
                "loginUser": user['loginUser'],
                "visaCategoryCode": user["visaTypeCode"],
                "isEdit": False,
                "feeEntryTypeCode": None,
                "feeExemptionTypeCode": None,
                "feeExemptionDetailsCode": None,
                "applicantList": applicantList,
                "languageCode": "zh-CN",
                "isWaitlist": False,
                "roleName": "Individual",
            }
            # data[f"{randomize_case('loginUser')}"] = user["loginUser"]
            print(f"正在刷新客户{user['chnname']}，护照:{user['passportNO']}，{data}")

            proxy = random.choice(delegate)
            proxies = {
                "http": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
                "https": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
            }
            response = requests.post(
                url,
                json=data,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
                verify=False,
            )
            if response.status_code == 200:
                data = response.json()
                if data["error"] is None:
                    if (
                        redis_client.hget(
                            f"{user.get('missionCode')}UserDatas",
                            f"{user['centerCode']}-{user['passportNO']}",
                        )
                        != None
                    ):
                        print(f"客户{user['chnname']}成功获取URN:{data['urn']}")
                        update_urn_time = int(tm.time())
                        user["urn"] = data["urn"]
                        user["updateUrnTime"] = update_urn_time
                        if user.get('missionCode') != "pol":
                            user["get_otp_time"] = update_urn_time
                        redis_client.hset(
                            f"{user.get('missionCode')}UserDatas",
                            f"{user['centerCode']}-{user['passportNO']}",
                            json.dumps({**user}),
                        )

                        if (
                            redis_client.get(
                                f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}"
                            )
                            != None
                            and user.get('missionCode') != "pol"
                        ):
                            redis_client.hset(
                                "waitotp",
                                user.get("urn"),
                                json.dumps(
                                    {
                                        **user,
                                        "redis": "aws",
                                        "redis_name": f"{user.get('missionCode')}UserDatas",
                                        "redis_key": f"{user['centerCode']}-{user['passportNO']}",
                                        "get_otp_time": int(tm.time()),
                                    }
                                ),
                            )
                            otp_result = attempt_get_otp(user, 10, 0.1)
                            if otp_result == True:
                                redis_client.hset(
                                    "waitotp",
                                    user.get("urn"),
                                    json.dumps(
                                        {
                                            **user,
                                            "redis": "aws",
                                            "redis_name": f"{user.get('missionCode')}UserDatas",
                                            "redis_key": f"{user['centerCode']}-{user['passportNO']}",
                                            "get_otp_time": int(tm.time()),
                                        }
                                    ),
                                )
                            else:
                                user["urn"] = None
                                redis_client.hdel(
                                    "waitotp",
                                    user.get("urn"),
                                )
                                redis_client.hset(
                                    f"{user.get('missionCode')}UserDatas",
                                    f"{user['centerCode']}-{user['passportNO']}",
                                    json.dumps({**user}),
                                )

                elif (
                    "You have exceeded the limit restricted"
                    in data["error"]["description"]
                    or 'Invalid request' in data["error"]["description"]
                ):

                    print(data["error"]["description"])
                else:
                    print(
                        f"{all_missionCode_map[user.get('missionCode')]}:{all_centerCode_map[user.get('centerCode')]}客户{user['chnname']}获取URN失败:{data['error']['description']}"
                    )
                    if (
                        user["remark"] != "现号"
                        and "Invalid inputs" not in data["error"]["description"]
                        and "We are sorry but no appointment slots are currently available. New slots open at regular intervals, please try again later"
                        not in data["error"]["description"]
                        and "Please wait a few moments before saving your details and continuing" not in data["error"]["description"]
                        and "SQL Exception" not in data["error"]["description"]
                    ):
                        url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe"
                        postData = {
                            "msgtype": "text",
                            "text": {
                                "content": f"{all_missionCode_map[user.get('missionCode')]}:{all_centerCode_map[user.get('centerCode')]}客户【{user.get('chnname')}】刷新URN出错，原因:{data['error']['description']}"
                            },
                        }
                        requests.post(url, json=postData)
                        redis_client.hset(
                            "errorUserDatas",
                            f"{user['centerCode']}-{user['passportNO']}",
                            json.dumps(
                                {
                                    **user,
                                    "update_order_time": int(tm.time()),
                                    "message": data["error"]["description"],
                                }
                            ),
                        )
                    if (
                        "Invalid email id" in data["error"]["description"]
                        or "Invalid inputs" in data["error"]["description"]
                        or "Please wait a few moments before saving your details and continuing" in data["error"]["description"]
                        or "We are sorry but no appointment slots are currently available. New slots open at regular intervals, please try again later"
                        in data["error"]["description"]
                    ):
                        user["urn"] = None
                        redis_client.hset(
                            f"{user.get('missionCode')}UserDatas",
                            f"{user['centerCode']}-{user['passportNO']}",
                            json.dumps({**user}),
                        )
                    elif "An applicant with this passport number has already been added" in data["error"]["description"]:
                        print(data['error'])
                    elif (
                        "departure"
                        not in data["error"]["description"]
                        # and "Invalid inputs" not in data["error"]["description"]
                        and "SQL Exception" not in data["error"]["description"]
                        and "tempdb" not in data["error"]["description"]
                    ):
                        redis_client.hdel(
                            f"{user.get('missionCode')}UserDatas",
                            f"{user['centerCode']}-{user['passportNO']}",
                        )
            elif response.status_code == 401:
                print(response.status_code)
            else:
                print(response.status_code)
        except Exception as e:
            print(e)
            user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
            if user_id in processing_users:
                processing_users.remove(user_id)
    user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
    if user_id in processing_users:
        processing_users.remove(user_id)


def is_date_between(date, start_date, end_date):
    # Convert "dd/mm/yyyy" format date strings to "mm/dd/yyyy"
    start_date = convert_date_format(start_date)
    end_date = convert_date_format(end_date)

    # Convert date strings to datetime objects
    date_obj = datetime.strptime(date, "%d/%m/%Y")
    start_date_obj = datetime.strptime(start_date, "%m/%d/%Y")
    end_date_obj = datetime.strptime(end_date, "%m/%d/%Y")

    # Check if date is within the range
    return start_date_obj <= date_obj <= end_date_obj


def convert_date_format(date):
    parts = date.split("/")
    return f"{parts[1]}/{parts[0]}/{parts[2]}"


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def process_users():
    while True:
        try:
            user = user_queue.get(timeout=1)  # 设置超时时间为1秒
            get_urn(user)
            user_queue.task_done()
        except queue.Empty:
            tm.sleep(0.1)  # 如果队列为空，则休眠0.1秒


def refresh_proxy():
    while True:
        tm.sleep(60)
        global delegate
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        processing_users.clear()
        delegate = json.loads(redis_client.get("fast_proxy"))


threading.Thread(target=refresh_proxy).start()


def getNeedProceed(missionCode, centerCode, visaTypeCode):
    current_time = int(tm.time())

    def filter_condition(user):
        return (
            (
                not user.get("urn")
                or not user.get("updateUrnTime")
                or current_time - user.get("updateUrnTime") > 3400
                or (
                    current_time - user.get("updateUrnTime") > 180
                    and (
                        not user.get("update_otp_time")
                        or current_time - user.get("update_otp_time") > 3400
                    )
                )
            )
            and user.get("vip") != 5
            and user.get('centerCode') == centerCode
            and user.get('visaTypeCode') == visaTypeCode
            and user.get('not_our_email') != True
            # and (user.get('centerCode') not in ['GR NN', 'GR HZ'] or redis_client.hget('userDatas', f"GRSG-{user['passportNO']}") == None or (redis_client.hget('userDatas', f"GRSG-{user['passportNO']}") != None and json.loads(redis_client.hget('userDatas', f"GRSG-{user['passportNO']}")).get('wait') != True))
        )
    userDatas = redis_client.hgetall(f"{missionCode}UserDatas")
    need_proceed = list(filter(filter_condition, userDatas))
    return need_proceed


# 首先，创建并启动子线程
threads = []
for _ in range(20):
    thread = threading.Thread(target=process_users)
    thread.daemon = True  # 将线程设置为守护线程，确保主程序退出时子线程也会停止
    thread.start()
    threads.append(thread)


def refresh_urn(_, centerCode_visaTypeCode):
    missionCode = centerCode_visaTypeCode.split('--')[0]
    centerCode = centerCode_visaTypeCode.split('--')[1]
    visaTypeCode = centerCode_visaTypeCode.split('--')[2]
    print(f'正在刷新{missionCode} {centerCode} {visaTypeCode}')
    need_proceed = getNeedProceed(missionCode, centerCode, visaTypeCode)
    # if len(need_proceed) == 0:
    #     need_proceed = getNeedProceed(userDatas, True)
    if len(need_proceed) != 0:
        print(f"正在刷新{len(need_proceed)}个客户的URN")
        # 创建一个线程安全的队列来保存需要处理的用户
        for user in need_proceed:
            user_id = f"{user.get('missionCode')}-{user.get('centerCode')}-{user.get('visaTypeCode')}-{user.get('passportNO')}"
            if user_id not in processing_users:
                user_queue.put(user)
                processing_users.add(user_id)


redis_client.subscribe(f"common_refresh_urn", refresh_urn)
