#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OTP获取和验证流程
"""

import json
import time
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟Redis客户端


class MockRedisClient:
    def __init__(self):
        # 模拟邮箱验证码存储
        self.email_codes = {
            "<EMAIL>": "123456",
            "<EMAIL>": "654321"
        }

    def get(self, key):
        return self.email_codes.get(key)

# 模拟requests响应


class MockResponse:
    def __init__(self, status_code, json_data):
        self.status_code = status_code
        self._json_data = json_data

    def json(self):
        return self._json_data

# 模拟函数


def mock_get_code_from_sms(phone):
    """模拟从短信平台获取验证码"""
    print(f"模拟从短信平台获取验证码，手机号: {phone}")
    # 模拟短信验证码
    if phone == "***********":
        return "888888"
    return ""


def mock_get_code_from_email(email, redis_client):
    """模拟从Redis获取邮箱验证码"""
    print(f"模拟从Redis获取邮箱验证码，邮箱: {email}")
    otp_code = redis_client.get(email)
    if otp_code:
        print(f"从邮箱 {email} 获取到验证码: {otp_code}")
        return otp_code
    return ""


def mock_wait_for_otp_code(user_data, otp_method, account, redis_client):
    """模拟等待获取OTP验证码"""
    print(f"开始等待OTP验证码，方式: {otp_method}")

    max_attempts = 5  # 测试时减少等待次数

    for attempt in range(max_attempts):
        otp_code = ""

        if "EMAIL" in otp_method:
            # 从Redis获取邮箱验证码
            otp_code = mock_get_code_from_email(account.get("email"), redis_client)
        elif "SMS" in otp_method:
            # 从短信平台获取验证码
            otp_code = mock_get_code_from_sms(account.get("phone"))

        if otp_code:
            print(f"客户 {user_data.get('chnname')} 获取到OTP验证码: {otp_code}")
            return otp_code

        # 模拟等待（测试时缩短为1秒）
        print(f"等待OTP验证码，第 {attempt + 1}/{max_attempts} 次尝试")
        time.sleep(1)

    print(f"客户 {user_data.get('chnname')} 等待OTP验证码超时")
    return ""


def mock_validate_otp(user_data, otp_code, account):
    """模拟验证OTP验证码"""
    print(f"模拟验证OTP，用户: {user_data.get('chnname')}, 验证码: {otp_code}")

    # 模拟验证成功的情况
    if otp_code in ["123456", "654321", "888888"]:
        print(f"客户 {user_data.get('chnname')} OTP验证成功")
        return True
    else:
        print(f"客户 {user_data.get('chnname')} OTP验证失败")
        return False


def mock_update_order_with_otp_result(user_data, otp_result):
    """模拟更新订单的OTP验证结果"""
    import time

    order_data = {
        "order_id": user_data.get("order_id", "TEST123"),
        "urn": user_data.get("urn"),
        "updateUrnTime": int(time.time()),
        "update_otp_time": int(time.time()),
        "otp_validated": otp_result,
        "otp_validation_time": int(time.time()) if otp_result else None
    }

    print(f"更新订单数据:")
    print(f"  订单ID: {order_data['order_id']}")
    print(f"  URN: {order_data['urn']}")
    print(f"  OTP验证结果: {order_data['otp_validated']}")
    print(f"  验证时间: {order_data['otp_validation_time']}")

    return order_data


def mock_get_otp(user_data, otp_method="EMAIL"):
    """模拟完整的OTP获取和验证流程"""
    print("=" * 50)
    print(f"开始OTP流程，客户: {user_data.get('chnname')}")
    print("=" * 50)

    # 模拟账号信息
    account = {
        "email": "<EMAIL>",
        "phone": "***********",
        "token": "mock_token"
    }

    redis_client = MockRedisClient()

    try:
        # 1. 模拟触发OTP生成
        print("1. 触发OTP生成...")
        print(f"   客户 {user_data.get('chnname')} 成功触发OTP生成")
        print(f"   OTP生成方式: {otp_method}")

        # 2. 等待获取验证码
        print("2. 等待获取验证码...")
        otp_code = mock_wait_for_otp_code(user_data, otp_method, account, redis_client)

        if otp_code:
            # 3. 验证OTP
            print("3. 验证OTP...")
            validation_result = mock_validate_otp(user_data, otp_code, account)

            # 4. 更新订单状态
            print("4. 更新订单状态...")
            order_data = mock_update_order_with_otp_result(user_data, validation_result)

            if validation_result:
                print(f"✓ 客户 {user_data.get('chnname')} OTP完整流程成功")
                return True
            else:
                print(f"✗ 客户 {user_data.get('chnname')} OTP验证失败")
                return False
        else:
            print(f"✗ 客户 {user_data.get('chnname')} 未能获取到OTP验证码")
            return False

    except Exception as e:
        print(f"✗ OTP流程异常: {e}")
        return False


def test_otp_flow():
    """测试OTP获取和验证流程"""
    print("=" * 60)
    print("测试OTP获取和验证流程")
    print("=" * 60)

    # 测试用例
    test_cases = [
        {
            "user_data": {
                "order_id": "TEST2025001",
                "chnname": "张三",
                "missionCode": "prt",
                "centerCode": "POBE",
                "urn": "URN123456"
            },
            "otp_method": "EMAIL",
            "description": "邮箱OTP测试"
        },
        {
            "user_data": {
                "order_id": "TEST2025002",
                "chnname": "李四",
                "missionCode": "ita",
                "centerCode": "ITBE",
                "urn": "URN789012"
            },
            "otp_method": "SMS",
            "description": "短信OTP测试"
        },
        {
            "user_data": {
                "order_id": "TEST2025003",
                "chnname": "王五",
                "missionCode": "fra",
                "centerCode": "FRBE",
                "urn": "URN345678"
            },
            "otp_method": "EMAIL,SMS",
            "description": "混合OTP测试"
        }
    ]

    success_count = 0
    total_count = len(test_cases)

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['description']}")
        print("-" * 40)

        result = mock_get_otp(test_case["user_data"], test_case["otp_method"])
        if result:
            success_count += 1

        print()

    print("=" * 60)
    print(f"测试完成: {success_count}/{total_count} 成功")
    print("=" * 60)


def test_individual_functions():
    """测试各个独立函数"""
    print("\n" + "=" * 60)
    print("测试各个独立函数")
    print("=" * 60)

    redis_client = MockRedisClient()

    # 测试邮箱验证码获取
    print("1. 测试邮箱验证码获取:")
    email_code = mock_get_code_from_email("<EMAIL>", redis_client)
    print(f"   结果: {email_code}")

    # 测试短信验证码获取
    print("\n2. 测试短信验证码获取:")
    sms_code = mock_get_code_from_sms("***********")
    print(f"   结果: {sms_code}")

    # 测试OTP验证
    print("\n3. 测试OTP验证:")
    user_data = {"chnname": "测试用户"}
    account = {"email": "<EMAIL>"}
    validation_result = mock_validate_otp(user_data, "123456", account)
    print(f"   结果: {validation_result}")


if __name__ == "__main__":
    test_otp_flow()
    test_individual_functions()
