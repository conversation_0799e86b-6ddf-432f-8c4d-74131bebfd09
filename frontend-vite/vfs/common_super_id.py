# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
from datetime import datetime, timedelta
import calendar
import string
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization, hashes
import base64
from queue import Queue


redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def generate_string(length=10):
    """生成随机英文字符串（包含大小写字母）"""
    letters = string.ascii_letters  # 包含所有大小写字母
    return ''.join(random.choice(letters) for _ in range(length))


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


def gen_del():
    return f"http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in-session-{generate_string(10)}:<EMAIL>:33335"


delegate = gen_del()

worker_num = 3


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def pick_random_qiwei_url(zw, center):
    sh_zwlist = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=42b02f70-26d4-4d39-b3e0-e8a4e9d0562d",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=50c9f2f8-7114-4897-af2b-e27a9b804dba",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dbb0e25b-ca08-4454-8898-3a0ae787fd5b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ecf71308-c423-4269-aa69-452e4088466c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b38addc5-1bc4-46e4-b654-652cc83b6f19",
    ]
    zwlist = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6e7591a7-3cf6-4128-aaa6-c4e0d1a6d353",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f6494bee-d127-449e-af2b-15070442a792",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0462e7a1-14f8-4538-aa18-56282204c175",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=342966c0-d9c2-47dc-aa28-44657b485545",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5972903f-6a63-47bf-b0e1-8565d2f63530",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c5539dd1-a547-4225-ab10-bb0e871af9ab",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3cc124d4-0388-44f5-9a9c-c6b55786d11a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=13e9dd9a-a1cb-439a-9bdc-faaf760d0111",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9e81a1f-a5df-4110-ab71-485d8b9166d0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8228068a-ce12-4558-b15c-c3df1561b6e1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9ee5d800-2e77-42a1-8f50-9ba0628b0e55",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5296262e-759e-4c9a-9a7d-a8cae4a73bdd",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2fad30bb-24e4-4869-a0a7-ccf67aa23a7b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5cbfe67a-6c99-433b-b185-9b9852d1ff6a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=60c95926-1697-48c0-a577-ae33667fed0b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8cd589ac-a893-4c28-a016-6fd130a8a9b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8cd589ac-a893-4c28-a016-6fd130a8a9b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9f768e7-eacb-4dc9-9ae1-c55d98f88097",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1c2f752f-4082-4d98-9f37-5561fac6f6b7",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0ff8e975-d689-4839-9932-987e00c16ac0",
    ]
    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=020a8d0b-6285-41be-972a-4997494056e8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d842efa5-61cf-4c64-94ba-3b0008aeeadf",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aa1ad9d3-dea9-49f1-a839-64f9227aa91a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7af17a71-e1ee-4e2c-a3fa-17455515b72e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1c8fe9e-1a96-494e-aa6a-800aa2e311e2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70669e7d-abef-4c9a-85f6-3ffb93cc57a6",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de03a1c0-e4e1-4bdb-abd9-82755c0acfa2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f1d752d6-68e0-4cef-a739-a2ed7e68836c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da064818-0822-4853-bf73-8904815bf592",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a55180cb-c0b7-4056-beaf-a09afb7f18b0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=586fe69a-299e-4a7c-a5cc-ef6eaeebd19b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=606ad073-2903-4eaf-b763-c08bd1dd5737",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2a52ffb3-ccb7-4ac0-b6ee-9e32bb4337b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2da9b32e-82b5-45d9-844b-98bc75e06194",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b949bd7c-0117-4d82-a2e9-4403e70093f1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2217904d-c443-436c-9779-f75d46336002",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=40e794bb-1fc2-46f2-bd11-8f4e87d05ef9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=16c5f10e-fbbc-4e77-b805-ef04a67dc829",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a192259d-64dc-46ed-bea3-51ffcf36ee4e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5789ee00-e168-49e5-a554-233df42d204b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec3915f6-8900-4800-83df-2e9cb40d23b8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b5265a1f-afc8-4fc6-a23f-4774f12f5a8c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=355d0c0a-e964-4a88-9b3d-93eddfa10118",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=254f174a-58f3-4e24-a005-a0eb1b4f0e14",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8532ecc9-6a45-40ab-bd57-93b97fd9cca1",
    ]
    if zw == True:
        if center == "GR SG":
            return pick_random_elements(sh_zwlist, 1)[0]
        return pick_random_elements(zwlist, 1)[0]
    else:
        return pick_random_elements(list, 1)[0]


def random_workday(start_date_str, end_date_str):
    # Convert string dates to datetime objects
    start_date = datetime.strptime(start_date_str, "%d/%m/%Y")
    end_date = datetime.strptime(end_date_str, "%d/%m/%Y")

    # Generate a list of dates between start and end date
    date_list = [
        start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)
    ]

    # Filter out weekends (Saturday=5, Sunday=6)
    workdays = [date for date in date_list if date.weekday() < 5]

    # Return a random workday
    if workdays:
        return random.choice(workdays).strftime("%d/%m/%Y")
    else:
        return None


def extract_allocation_ids_and_time_slots(data):
    results = []
    for item in data:
        raw_date = item["date"]
        counters = item["counters"]
        for counter in counters:
            groups = counter["groups"]
            for group in groups:
                time_slots = group["timeSlots"]
                for slot in time_slots:
                    allocation_id = slot["allocationId"]
                    time = slot["timeSlot"]
                    remainingSeats = slot["remainingSeats"]
                    totalSeats = slot["totalSeats"]
                    results.append(
                        {
                            "allocationId": allocation_id,
                            "time": time,
                            "date": raw_date,
                            "remainingSeats": remainingSeats,
                            "totalSeats": totalSeats,
                        }
                    )
    return results


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def mission_code_to_chn(code):
    if code == "ita":
        return "意大利"
    if code == "deu":
        return "德国"


def center_code_to_chn(code):
    mapping = {
        "BJII": "北京",
        "GGH": "广州",
        "SHI": "上海",
        "CDVIP": "成都",
        "SYA": "沈阳",
        "XIN": "西安",
        "Cho": "重庆",
        "ITA-CHHNG": "杭州",
        "IVAC": "南京",
        "JNN": "济南",
        "FZH": "福州",
        "WUH": "武汉",
        "CGA": "长沙",
        "SHN": "深圳",
        "Kun": "昆明",
        "GR SG": "上海",
        "GRGZ": "广州",
        "PEK": "北京",
        "GRCG": "成都",
        "GRSH": "沈阳",
        "GRZS": "深圳",
    }
    return mapping.get(code)


def is_date_between(date, start_date, end_date):
    # Convert "dd/mm/yyyy" format date strings to "mm/dd/yyyy"
    start_date = convert_date_format(start_date)
    end_date = convert_date_format(end_date)

    # Convert date strings to datetime objects
    date_obj = datetime.strptime(date, "%m/%d/%Y")
    start_date_obj = datetime.strptime(start_date, "%m/%d/%Y")
    end_date_obj = datetime.strptime(end_date, "%m/%d/%Y")

    # Check if date is within the range
    return start_date_obj <= date_obj <= end_date_obj


def convert_date_format(date):
    parts = date.split("/")
    return f"{parts[1]}/{parts[0]}/{parts[2]}"


def get_tomorrow_date():
    tomorrow = datetime.now() + timedelta(days=1)
    day = str(tomorrow.day).zfill(2)
    month = str(tomorrow.month).zfill(2)
    year = str(tomorrow.year)
    return f"{day}/{month}/{year}"


def working_days_until_end_of_month(input_date):
    # 将输入的日期字符串转换为datetime对象
    date_format = "%d/%m/%Y"
    start_date = datetime.strptime(input_date, date_format)

    # 获取当月的最后一天
    last_day = calendar.monthrange(start_date.year, start_date.month)[1]
    end_date = datetime(start_date.year, start_date.month, last_day)

    # 遍历从开始日期到月底的每一天
    current_date = start_date
    working_days = []
    while current_date <= end_date:
        # 检查是否为工作日（周一到周五）
        if current_date.weekday() < 5:
            working_days.append(current_date.strftime(date_format))
        # 移到下一天
        current_date += timedelta(days=1)

    return working_days


def compare_dates(date_str1, date_str2):
    try:
        # 将时间字符串解析为datetime对象
        date1 = datetime.strptime(date_str1, "%d/%m/%Y")
        date2 = datetime.strptime(date_str2, "%d/%m/%Y")

        # 比较datetime对象
        if date1 < date2:
            return True
        else:
            return False
    except ValueError:
        return "无效的日期格式"


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def get_phone(phone):
    url = f"http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        print(data)
        # 如果找到匹配项，打印验证码
        if data.get("code") == "0":
            return True
        elif data.get("msg") == "指定专属码手机号不在线":
            return "remove_phone"
        else:
            return False
    return False


def attempt_get_code(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        otp_code = get_code(phone)
        if otp_code:
            return otp_code
        time.sleep(retry_interval)
    return ""


def get_code(phone):
    url = f"http://api.haozhuma.com/sms/?api=getMessage&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        print(data)
        # 如果找到匹配项，打印验证码
        if data.get("yzm"):
            return data.get("yzm")
        else:
            return ""
    return ""


def get_calendar(random_loginUser):
    try:
        allava = [item for item in redis_client.hgetall('vfs_calendar') if item.get('missionCode') == random_loginUser.get('missionCode') and item.get('date') != '暂无位置']
        if len(allava) == 0:
            return False

        random_scan_area = random.choice(allava)
        data_arr = random_scan_area.get('date').split('-')
        random_scan_area['date'] = f"{data_arr[2]}/{data_arr[1]}/{data_arr[0]}"

        r_auth = f"EAAAAN{generate_random_string(597)}="
        url = "https://lift-apicn.vfsglobal.com/appointment/calendar"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": random_loginUser["token"],
            "sec-ch-ua-mobile": "?0",
            "route": f"chn/zh/{random_loginUser.get('missionCode')}",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cookie": random_loginUser['ltsn']
        }
        delegate = gen_del()
        urn = attempt_urn(random_loginUser, random_scan_area, delegate, r_auth)
        if urn != False:
            urn = urn.get('urn')

            if (
                redis_client.get(
                    f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}"
                )
                != None
                and user.get('missionCode') != "pol"
            ):
                otp_result = attempt_getotp(random_loginUser, random_scan_area, urn)
                if otp_result:
                    print('otp发送成功')
                    time.sleep(5)
                    otp = redis_client.get(random_loginUser.get('email'))
                    if not otp:
                        time.sleep(5)
                        otp = redis_client.get(random_loginUser.get('email'))
                    if not otp:
                        time.sleep(5)
                        otp = redis_client.get(random_loginUser.get('email'))
                    if not otp:
                        time.sleep(5)
                        otp = redis_client.get(random_loginUser.get('email'))
                    if not otp:
                        time.sleep(25)
                        otp = redis_client.get(random_loginUser.get('email'))
                    if not otp:
                        return False
                    res = attempt_verotp(random_loginUser, random_scan_area, urn, otp)
                    if res == False:
                        return False
                    else:
                        print('otp验证成功')
                        random_loginUser['updateSuperId'] = int(time.time())
                        random_loginUser['urn'] = urn
                        redis_client.hset(f"{random_loginUser.get('missionCode')}LoginUser", random_loginUser['email'], json.dumps({**random_loginUser}))
                        return True

            elif user.get('missionCode') == "pol":
                otp_result = attempt_getotp(random_loginUser, random_scan_area, urn)
                if otp_result:
                    print('otp发送成功')
                    time.sleep(5)
                    phone = get_phone(random_loginUser.get('phone'))

                    if not get_phone:
                        print("指定手机号失败")
                        return False

                    if not phone:
                        print("Failed to get OTP after maximum retries.")
                        return False

                    time.sleep(5)
                    otp = attempt_get_code(user.get('phone'), 5, 5)
                    print(f"成功获取验证码{otp}")
                    url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={user.get('phone')}"
                    requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
                    res = attempt_verotp(random_loginUser, random_scan_area, urn, otp)
                    if res == False:
                        return False
                    else:
                        print('otp验证成功')
                        random_loginUser['updateSuperId'] = int(time.time())
                        random_loginUser['urn'] = urn
                        redis_client.hset(f"{random_loginUser.get('missionCode')}LoginUser", random_loginUser['email'], json.dumps({**random_loginUser}))
                        return True
            else:
                random_loginUser['updateSuperId'] = int(time.time())
                random_loginUser['urn'] = urn
                redis_client.hset(f"{random_loginUser.get('missionCode')}LoginUser", random_loginUser['email'], json.dumps({**random_loginUser}))
                return True
        else:
            return False
        data = {
            "countryCode": "chn",
            "missionCode": random_scan_area.get('missionCode'),
            "centerCode": random_scan_area.get('centerCode'),
            "loginUser": random_loginUser["email"],
            "visaCategoryCode": random_scan_area.get('visaCategoryCode'),
            "fromDate": random_scan_area.get('date'),
            "payCode": "",
            "urn": urn
        }

        proxies = {
            "http": delegate,
            "https": delegate,
        }

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
            timeout=20
        )
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if response.status_code == 200:
            data = response.json()
            print(data)
            if data.get('calendars') and len(data.get('calendars')) != 0:
                days = list(dict.fromkeys([item.get('date') for item in data.get('calendars')]))
                earliesDate = convert_date_format(
                    days[0]
                )
                attempt_time_slot(random_loginUser, random_scan_area, earliesDate, urn, delegate, r_auth)
                return True
            else:
                print(
                    f"当前时间：{formatted_time} |  领区：{random_scan_area['centerCode']} | calendar | 结果：{data}"
                )
                return False
        else:
            print(
                f"当前时间：{formatted_time} |  领区：{random_scan_area['centerCode']} | calendar | 结果：{response.status_code}"
            )
            return False
    except Exception as e:
        print(e)
        return False


def get_time(random_loginUser, random_scan_area, earliesDate, urn, delegate, r_auth):
    try:
        url = "https://lift-apicn.vfsglobal.com/appointment/timeslot"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": random_loginUser["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "route": f"chn/zh/{random_loginUser.get('missionCode')}",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cookie": random_loginUser['ltsn']
        }
        data = {
            "countryCode": "chn",
            "missionCode": random_scan_area.get('missionCode'),
            "centerCode": random_scan_area.get('centerCode'),
            "loginUser": random_loginUser["email"],
            "visaCategoryCode": random_scan_area.get('visaCategoryCode'),
            "slotDate": earliesDate,
            "urn": urn
        }
        proxies = {
            "http": delegate,
            "https": delegate,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
            timeout=20
        )
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if response.status_code == 200:
            data = response.json()
            print(data)
            if data.get("slots") is not None:
                timeSlots = data.get("slots")
                if len(timeSlots) > 0:
                    random_loginUser['allocationId'] = timeSlots[0].get('allocationId')
                    random_loginUser['updateSuperId'] = int(time.time())
                    random_loginUser['urn'] = urn
                    random_loginUser['delegate'] = delegate
                    random_loginUser['r_auth'] = r_auth
                    redis_client.hset(f"{random_loginUser.get('missionCode')}LoginUser", random_loginUser['email'], json.dumps({**random_loginUser}))
            return True
        else:
            print(
                f"当前时间：{formatted_time} |  领区：{random_scan_area['centerCode']} | timeslot | 结果：{response.status_code}"
            )
            return False
    except Exception as e:
        print(e)
        return False


def c_urn(random_loginUser, random_scan_area, delegate, r_auth):
    try:
        url = f"https://lift-apicn.vfsglobal.com/appointment/applicants"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": random_loginUser["token"],
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{random_loginUser['missionCode']}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cookie": random_loginUser['ltsn']
        }

        applicantList = [
            {
                "urn": "",
                "arn": "",
                "loginUser": random_loginUser.get('email'),
                "firstName": "ZHENDONG",
                "middleName": "",
                "lastName": "JIN",
                "salutation": "",
                "gender": random.choice([1, 2]),
                "nationalId": None,
                "VisaToken": None,
                "contactNumber":  random_loginUser.get('phone'),
                "dialCode": "86",
                "passportNumber": "*********",
                "confirmPassportNumber": "*********",
                "passportExpirtyDate": "03/03/2032",
                "dateOfBirth": "03/03/1997",
                "emailId":  random_loginUser.get('email'),
                "nationalityCode":  "CHN",
                "state": None,
                "city": None,
                "isEndorsedChild": False,
                "applicantType": 0,
                "addressline1": None,
                "addressline2": None,
                "pincode": None,
                "referenceNumber": "",
                "vlnNumber": None,
                "applicantGroupId": 0,
                "parentPassportNumber": "",
                "parentPassportExpiry": "",
                "dateOfDeparture": "11/05/2025",
                "gwfNumber": "",
                "entryType": "",
                "eoiVisaType": "",
                "passportType": "",
                "employerContactNumber": "",
                "employerDialCode": "",
                "employerEmailId": "",
                "employerFirstName": "",
                "employerLastName": "",
                "familyReunificationCerificateNumber": "",
                "vfsReferenceNumber": "",
                "PVRequestRefNumber": "",
                "PVStatus": "",
                "PVStatusDescription": "",
                "PVCanAllowRetry": True,
                "PVisVerified": False,
                "ipAddress": None,
            }
        ]
        data = {
            "countryCode": "chn",
            "missionCode": random_scan_area["missionCode"],
            "centerCode": random_scan_area["centerCode"],
            "loginUser": random_loginUser.get("email"),
            "visaCategoryCode": random_scan_area["visaCategoryCode"],
            "isEdit": False,
            "feeEntryTypeCode": None,
            "feeExemptionTypeCode": None,
            "feeExemptionDetailsCode": None,
            "applicantList": applicantList,
            "languageCode": "zh-CN",
            "isWaitlist": False,
            "roleName": "Individual",
        }
        proxies = {
            "http": delegate,
            "https": delegate,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
            timeout=20
        )
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if response.status_code == 200:
            data = response.json()
            print(data)
            if not data.get('error'):
                return data
            return False
        elif response.status_code == 401:
            random_loginUser['updateTokenTime'] = None
            redis_client.hset(f"{random_loginUser.get('missionCode')}LoginUser", random_loginUser['email'], json.dumps({**random_loginUser}))
            return False
        else:
            print(
                f"当前时间：{formatted_time} |  领区：{random_scan_area['centerCode']} | 结果：刷新URN出错 {response.status_code}"
            )
            return False
    except Exception as e:
        print(e)
        return False


def get_otp(random_loginUser, random_scan_area, urn):
    try:
        account = random.choice([item for item in redis_client.hgetall(f"{random_loginUser.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000])

        url = f"https://lift-apicn.vfsglobal.com/appointment/applicantotp"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            # "cookie": random_loginUser["ltsn"],
        }
        data = {"urn": urn, "loginUser": account.get("email"), "LoGInUSer": random_loginUser.get("email"),  "missionCode": random_scan_area["missionCode"], "countryCode": "chn", "centerCode": random_scan_area["centerCode"], "captcha_version": "", "captcha_api_key": "", "OTP": "", "otpAction": "GENERATE", "languageCode": "zh-CN", "userAction": None}
        proxies = {
            "http": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
            "https": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
            timeout=20
        )
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if response.status_code == 200:
            data = response.json()
            print(data)
            return True
        else:
            print(
                f"当前时间：{formatted_time} |  领区：{random_scan_area['centerCode']} | 结果：{response.status_code}"
            )
            return False
    except Exception as e:
        print(e)
        return False


def ver_otp(random_loginUser, random_scan_area, urn, otp):
    try:
        account = random.choice([item for item in redis_client.hgetall(f"{random_loginUser.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000])

        url = f"https://lift-apicn.vfsglobal.com/appointment/applicantotp"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            # "cookie": random_loginUser["ltsn"],
        }
        data = {"urn": urn, "loginUser": account.get("email"), "LoGInUSer": random_loginUser.get("email"),  "missionCode": random_scan_area["missionCode"], "countryCode": "chn", "centerCode": random_scan_area["centerCode"], "captcha_version": "", "captcha_api_key": "", "OTP": otp, "otpAction": "VALIDATE", "languageCode": "zh-CN", "userAction": None}
        proxies = {
            "http": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
            "https": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:33335",
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4', 'safari18_0', 'edge101']),
            verify=False,
            timeout=20
        )
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if response.status_code == 200:
            data = response.json()
            print(data)
            return True
        else:
            print(
                f"当前时间：{formatted_time} |  领区：{random_scan_area['centerCode']} | applicantotp | 结果：{response.status_code}"
            )
            return False
    except Exception as e:
        print(e)
        return False


def attempt_urn(random_loginUser, random_scan_area, delegate, r_auth, max_retries=2, retry_interval=1):
    for _ in range(max_retries):
        urn = c_urn(random_loginUser, random_scan_area, delegate, r_auth)
        if urn != False:
            return urn
        time.sleep(retry_interval)
    return False


def attempt_calendar(random_loginUser, max_retries=5, retry_interval=1):
    for _ in range(max_retries):
        phone = get_calendar(random_loginUser)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_time_slot(random_loginUser, random_scan_area, earliesDate, urn, delegate, r_auth, max_retries=5, retry_interval=1):
    for _ in range(max_retries):
        phone = get_time(random_loginUser, random_scan_area, earliesDate, urn, delegate, r_auth)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_getotp(random_loginUser, random_scan_area,  urn, max_retries=5, retry_interval=1):
    for _ in range(max_retries):
        phone = get_otp(random_loginUser, random_scan_area, urn)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_verotp(random_loginUser, random_scan_area,  urn, otp, max_retries=5, retry_interval=1):
    for _ in range(max_retries):
        phone = ver_otp(random_loginUser, random_scan_area, urn, otp)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def process_users():
    while not user_queue.empty():
        user = user_queue.get()
        attempt_calendar(user)
        user_queue.task_done()
        # if user.get('dy') == True:
        #     otp_dy(user)
        #     user_queue.task_done()
        # else:
        #     otp(user)
        #     user_queue.task_done()


def getNeedProceed(user_list):
    current_time = int(time.time())

    def filter_condition(user):
        return (
            user.get('updateTokenTime')
            and (not user.get("updateSuperId") or (user.get('updateTokenTime') > user.get("updateSuperId")) or (int(time.time()) - user.get("updateSuperId") > 1000))
            and (int(time.time()) - user.get('updateTokenTime') > 30)
            and (int(time.time()) - user.get('updateTokenTime') < 3000)
        )

    need_proceed = list(filter(filter_condition, user_list))
    return need_proceed


def refresh_proxy():
    while True:
        time.sleep(60)
        global delegate
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        delegate = json.loads(redis_client.get("login_proxy"))
        print(delegate)


threading.Thread(target=refresh_proxy).start()


while True:
    all_need = []
    scan_data = json.loads(redis_client.get("scanData"))
    scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']

    for country in scan_country:
        scan_option = [
            option
            for option in scan_data.get(country)
            if option["scan"] == True
        ]
        if len(scan_option) > 0:
            loginUsers = redis_client.hgetall(f"{country}LoginUser")
            need_proceed = getNeedProceed(loginUsers)
            all_need = all_need + need_proceed

    if len(all_need) != 0:
        print(f"正在刷新{len(all_need)}个ID")
        # 创建一个线程安全的队列来保存需要处理的用户
        for user in all_need:
            user_queue.put(user)

        num_threads = 30  # 可根据需要调整线程数量
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=process_users)
            thread.start()
            threads.append(thread)

        for thread in threads:
            thread.join()

        # for user in need_proceed:
        #     get_token(user)
    else:
        print("暂无需要刷新的票据")
    time.sleep(10)
