# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
from datetime import datetime, timedelta
import calendar
import string
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding


redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def gen_del():
    return f"http://kq123-zone-resi-region-in-session-{generate_random_string(10)}:<EMAIL>:16666"


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


delegate = json.loads(redis_client.get("fast_proxy"))
scan_data = json.loads(redis_client.get("scanData"))
scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']
temp = []
for country in scan_country:
    if len([
        option
        for option in scan_data.get(country)
        if option["scan"] == True
    ]) != 0:
        temp.append(country)

scan_country = temp


def refresh_scan_data():
    while True:
        time.sleep(10)
        global scan_data
        global scan_country
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        scan_data = json.loads(redis_client.get("scanData"))
        scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']
        temp = []
        for country in scan_country:
            if len([
                option
                for option in scan_data.get(country)
                if option["scan"] == True
            ]) != 0:
                temp.append(country)

        scan_country = temp
        global delegate
        delegate = json.loads(redis_client.get("fast_proxy"))


threading.Thread(target=refresh_scan_data).start()


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def pick_random_qiwei_url(zw, center):
    sh_zwlist = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=42b02f70-26d4-4d39-b3e0-e8a4e9d0562d",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=50c9f2f8-7114-4897-af2b-e27a9b804dba",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dbb0e25b-ca08-4454-8898-3a0ae787fd5b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ecf71308-c423-4269-aa69-452e4088466c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b38addc5-1bc4-46e4-b654-652cc83b6f19",
    ]
    zwlist = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6e7591a7-3cf6-4128-aaa6-c4e0d1a6d353",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f6494bee-d127-449e-af2b-15070442a792",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0462e7a1-14f8-4538-aa18-56282204c175",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=342966c0-d9c2-47dc-aa28-44657b485545",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5972903f-6a63-47bf-b0e1-8565d2f63530",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c5539dd1-a547-4225-ab10-bb0e871af9ab",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3cc124d4-0388-44f5-9a9c-c6b55786d11a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=13e9dd9a-a1cb-439a-9bdc-faaf760d0111",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9e81a1f-a5df-4110-ab71-485d8b9166d0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8228068a-ce12-4558-b15c-c3df1561b6e1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9ee5d800-2e77-42a1-8f50-9ba0628b0e55",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5296262e-759e-4c9a-9a7d-a8cae4a73bdd",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2fad30bb-24e4-4869-a0a7-ccf67aa23a7b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5cbfe67a-6c99-433b-b185-9b9852d1ff6a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=60c95926-1697-48c0-a577-ae33667fed0b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8cd589ac-a893-4c28-a016-6fd130a8a9b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8cd589ac-a893-4c28-a016-6fd130a8a9b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b9f768e7-eacb-4dc9-9ae1-c55d98f88097",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1c2f752f-4082-4d98-9f37-5561fac6f6b7",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0ff8e975-d689-4839-9932-987e00c16ac0",
    ]
    list = [
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=020a8d0b-6285-41be-972a-4997494056e8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=84a39ea8-6354-46ef-991d-bc7e0fea0066",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d842efa5-61cf-4c64-94ba-3b0008aeeadf",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=aa1ad9d3-dea9-49f1-a839-64f9227aa91a",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7af17a71-e1ee-4e2c-a3fa-17455515b72e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1c8fe9e-1a96-494e-aa6a-800aa2e311e2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=70669e7d-abef-4c9a-85f6-3ffb93cc57a6",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de03a1c0-e4e1-4bdb-abd9-82755c0acfa2",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f1d752d6-68e0-4cef-a739-a2ed7e68836c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da064818-0822-4853-bf73-8904815bf592",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a55180cb-c0b7-4056-beaf-a09afb7f18b0",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=586fe69a-299e-4a7c-a5cc-ef6eaeebd19b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=606ad073-2903-4eaf-b763-c08bd1dd5737",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2a52ffb3-ccb7-4ac0-b6ee-9e32bb4337b9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2da9b32e-82b5-45d9-844b-98bc75e06194",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b949bd7c-0117-4d82-a2e9-4403e70093f1",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2217904d-c443-436c-9779-f75d46336002",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=40e794bb-1fc2-46f2-bd11-8f4e87d05ef9",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=16c5f10e-fbbc-4e77-b805-ef04a67dc829",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a192259d-64dc-46ed-bea3-51ffcf36ee4e",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5789ee00-e168-49e5-a554-233df42d204b",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec3915f6-8900-4800-83df-2e9cb40d23b8",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b5265a1f-afc8-4fc6-a23f-4774f12f5a8c",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=355d0c0a-e964-4a88-9b3d-93eddfa10118",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=254f174a-58f3-4e24-a005-a0eb1b4f0e14",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7b816326-5e5c-441d-aced-fc3d186addfe",
        "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8532ecc9-6a45-40ab-bd57-93b97fd9cca1",
    ]
    if zw == True:
        if center == "GR SG":
            return pick_random_elements(sh_zwlist, 1)[0]
        return pick_random_elements(zwlist, 1)[0]
    else:
        return pick_random_elements(list, 1)[0]


def random_workday(start_date_str, end_date_str):
    # Convert string dates to datetime objects
    start_date = datetime.strptime(start_date_str, "%d/%m/%Y")
    end_date = datetime.strptime(end_date_str, "%d/%m/%Y")

    # Generate a list of dates between start and end date
    date_list = [
        start_date + timedelta(days=x) for x in range((end_date - start_date).days + 1)
    ]

    # Filter out weekends (Saturday=5, Sunday=6)
    workdays = [date for date in date_list if date.weekday() < 5]

    # Return a random workday
    if workdays:
        return random.choice(workdays).strftime("%d/%m/%Y")
    else:
        return None


def extract_allocation_ids_and_time_slots(data):
    results = []
    for item in data:
        raw_date = item["date"]
        counters = item["counters"]
        for counter in counters:
            groups = counter["groups"]
            for group in groups:
                time_slots = group["timeSlots"]
                for slot in time_slots:
                    allocation_id = slot["allocationId"]
                    time = slot["timeSlot"]
                    remainingSeats = slot["remainingSeats"]
                    totalSeats = slot["totalSeats"]
                    results.append(
                        {
                            "allocationId": allocation_id,
                            "time": time,
                            "date": raw_date,
                            "remainingSeats": remainingSeats,
                            "totalSeats": totalSeats,
                        }
                    )
    return results


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def is_date_between(date, start_date, end_date):
    # Convert "dd/mm/yyyy" format date strings to "mm/dd/yyyy"
    start_date = convert_date_format(start_date)
    end_date = convert_date_format(end_date)

    # Convert date strings to datetime objects
    date_obj = datetime.strptime(date, "%m/%d/%Y")
    start_date_obj = datetime.strptime(start_date, "%m/%d/%Y")
    end_date_obj = datetime.strptime(end_date, "%m/%d/%Y")

    # Check if date is within the range
    return start_date_obj <= date_obj <= end_date_obj


def convert_date_format(date):
    parts = date.split("/")
    return f"{parts[1]}/{parts[0]}/{parts[2]}"


def get_tomorrow_date():
    tomorrow = datetime.now() + timedelta(days=1)
    day = str(tomorrow.day).zfill(2)
    month = str(tomorrow.month).zfill(2)
    year = str(tomorrow.year)
    return f"{day}/{month}/{year}"


def working_days_until_end_of_month(input_date):
    # 将输入的日期字符串转换为datetime对象
    date_format = "%d/%m/%Y"
    start_date = datetime.strptime(input_date, date_format)

    # 获取当月的最后一天
    last_day = calendar.monthrange(start_date.year, start_date.month)[1]
    end_date = datetime(start_date.year, start_date.month, last_day)

    # 遍历从开始日期到月底的每一天
    current_date = start_date
    working_days = []
    while current_date <= end_date:
        # 检查是否为工作日（周一到周五）
        if current_date.weekday() < 5:
            working_days.append(current_date.strftime(date_format))
        # 移到下一天
        current_date += timedelta(days=1)

    return working_days


def compare_dates(date_str1, date_str2):
    try:
        # 将时间字符串解析为datetime对象
        date1 = datetime.strptime(date_str1, "%d/%m/%Y")
        date2 = datetime.strptime(date_str2, "%d/%m/%Y")

        # 比较datetime对象
        if date1 < date2:
            return True
        else:
            return False
    except ValueError:
        return "无效的日期格式"


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def get_slots():
    while True:
        try:
            country = random.choice(scan_country)
            scan_option = [
                option
                for option in scan_data.get(country)
                if option["scan"] == True
            ]
            if country == "pol":
                userDatas = [item for item in redis_client.hgetall('polUserDatas') if item.get("update_otp_time") != None
                             and int(time.time())
                             - item.get("update_otp_time")
                             < 10000]
                print(userDatas)
                adminDatas = None
                if len(userDatas) > 0:
                    random_user = random.choice(userDatas)
                    adminDatas = [item for item in redis_client.hgetall(f"polLoginUser") if item.get("email") == random_user.get('loginUser')]
                    random_loginUser = random.choice(adminDatas)
                    random_loginUser['urn'] = random_user.get('urn')
            else:
                adminDatas = [item for item in redis_client.hgetall(f"{country}LoginUser") if item.get("urn") and item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000]
            if adminDatas == None or (len(adminDatas) == 0 or len(scan_option) == 0):
                time.sleep(0.1)
                continue
            random_loginUser = random.choice(adminDatas)
            random_scan_area = random.choice(scan_option)

            # 将日期字符串转换为 datetime 对象，并设置时间部分为午夜
            date_objects = [
                datetime.strptime(date_str, "%d/%m/%Y").date()
                for date_str in random_scan_area["scanRange"]
            ]

            # 找到最大日期和最小日期
            max_date = max(date_objects)
            min_date = min(date_objects)

            # 获取今天的日期
            today = datetime.now().date()

            # 如果最小日期早于或等于今天，将最小日期设置为明天
            if min_date <= today:
                min_date = today + timedelta(days=1)

            # 格式化日期为字符串
            max_date_str = max_date.strftime("%d/%m/%Y")
            min_date_str = min_date.strftime("%d/%m/%Y")
            r_auth = f"EAAAAN{generate_random_string(597)}="
            url = f"https://lift-apicn.vfsglobal.com/appointment/calendar"
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "Authorize": r_auth,
                "authorize": random_loginUser["token"],
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "macOS",
                "origin": "https://visa.vfsglobal.com",
                "sec-fetch-site": "same-site",
                "route": f"chn/zh/{random_scan_area.get('missionCode')}",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://visa.vfsglobal.com/",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
                "cookie": random_loginUser['ltsn']
            }

            data = {
                "countryCode": "chn",
                "missionCode": random_scan_area.get('missionCode'),
                "centerCode": random_scan_area.get('centerCode'),
                "loginUser": random_loginUser["email"],
                "visaCategoryCode": random_scan_area.get('visaCategoryCode'),
                "fromDate": min_date_str,
                "payCode": "",
                "urn": random_loginUser.get('urn')
            }
            proxy = gen_del()
            proxies = {
                "http": proxy,
                "https": proxy,
            }
            # proxies = {
            #     "http": "http://scrapeops.country=in:<EMAIL>:8181",
            #     "https": "http://scrapeops.country=in:<EMAIL>:8181"
            # }
            response = requests.post(
                url,
                json=data,
                headers=headers,
                proxies=proxies,
                impersonate=random.choice(['firefox133', 'firefox135']),
                verify=False,
                timeout=20
            )
            current_time = datetime.now()
            formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
            if response.status_code == 200:
                data = response.json()

                if isinstance(data, dict) and data.get('calendars') and len(data.get('calendars')) != 0:
                    redis_client.hset(f"{random_scan_area.get('missionCode')}LoginUser", random_loginUser.get('email'), json.dumps({**random_loginUser, "delegate": proxy, "r_auth": r_auth}))
                    days = list(dict.fromkeys([item.get('date') for item in data.get('calendars')]))
                    # earliesDate = convert_date_format(
                    #     days[0]
                    # )

                    # if redis_client.get(
                    #     f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time"
                    # ) == None or compare_dates(
                    #     earliesDate,
                    #     redis_client.get(
                    #         f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time"
                    #     ),
                    # ):
                    #     url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b38addc5-1bc4-46e4-b654-652cc83b6f19"
                    #     postData = {
                    #         "msgtype": "text",
                    #         "text": {
                    #             "content": f"{random_scan_area.get('centerName')}  {random_scan_area.get('visaCategoryName')} 放号{earliesDate}"
                    #         },
                    #     }
                    #     requests.post(url, json=postData)
                    #     redis_client.set(
                    #         f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time",
                    #         earliesDate,
                    #     )
                    # if redis_client.get(
                    #     f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time"
                    # ) != None:
                    #     earliesDate_old = redis_client.get(
                    #         f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time"
                    #     )
                    #     old_year_month = f"{earliesDate_old.split('/')[2]}-{earliesDate_old.split('/')[1]}"
                    #     if old_year_month == max_date.strftime('%Y-%m'):
                    #         redis_client.set(
                    #             f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time",
                    #             earliesDate,
                    #         )
                    print(
                        f"当前时间：{formatted_time} | 扫描日期区间{min_date.strftime('%Y-%m-%d')}-{max_date.strftime('%Y-%m-%d')} | 国家：{random_scan_area['missionCode']} | 领区：{random_scan_area['centerName']} | {random_scan_area['visaCategoryName']} | 结果：{days}"
                    )
                    redis_client.hset(f"{country}Calendar", f"{random_scan_area['centerCode']}--{max_date.strftime('%Y-%m')}", json.dumps({"center": f"{random_scan_area['centerCode']}--{max_date.strftime('%Y-%m')}", "days": days}))
                    userList = redis_client.hgetall(f"{country}UserDatas")
                    if len(userList) == 0:
                        continue
                    center_code_matched_user = [
                        item
                        for item in userList
                        if item.get("centerCode")
                        == random_scan_area["centerCode"]
                        and item.get("missionCode")
                        == random_scan_area["missionCode"]
                        and item["visaTypeCode"]
                        == random_scan_area["visaCategoryCode"]
                        and (item.get("urn") or (item.get("wurn") and item.get('AVAILABLE') == True))
                        and item.get("vip") != 5
                        and (
                            redis_client.get(
                                f"{item.get('missionCode')}-{item.get('centerCode')}-{item.get('visaTypeCode')}"
                            )
                            == None
                            or (
                                item.get("update_otp_time") != None
                                and int(time.time())
                                - item.get("update_otp_time")
                                < 10000
                            )
                        )
                    ]
                    suit_slots = []
                    for day in days:
                        for user in center_code_matched_user:
                            if is_date_between(
                                day,
                                user.get("start_date"),
                                user.get("end_date"),
                            ):
                                suit_slots.append(day)
                                continue
                    if len(suit_slots) == 0 and random_scan_area.get('seizeSeat') == True:
                        for day in days:
                            suit_slots.append(day)
                    print("本次共匹配到" + str(len(suit_slots)) + "个时间")
                    for index, day in enumerate(
                        pick_random_elements(suit_slots, 3)
                    ):
                        redis_client.publish(
                            f"common_publish_worker",
                            json.dumps({**random_scan_area, "day": convert_date_format(day), "account": random_loginUser.get('email')}),
                        )

                else:
                    # earliesDate = redis_client.get(
                    #     f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time"
                    # )
                    # if earliesDate != None and f"{earliesDate.split('/')[2]}-{earliesDate.split('/')[1]}" == max_date.strftime('%Y-%m'):
                    #     redis_client.delete(
                    #         f"{random_scan_area.get('missionCode')}-{random_scan_area.get('centerCode')}-{random_scan_area.get('visaCategoryCode')}-Time"
                    #     )
                    # redis_client.hset(f"{country}Calendar", f"{random_scan_area['centerCode']}--{max_date.strftime('%Y-%m')}", json.dumps({"center": f"{random_scan_area['centerCode']}--{max_date.strftime('%Y-%m')}", "days": []}))
                    print(
                        f"当前时间：{formatted_time} |  扫描日期区间{min_date.strftime('%Y-%m-%d')}-{max_date.strftime('%Y-%m-%d')} | 国家：{random_scan_area['missionCode']} | 领区：{random_scan_area['centerName']} | {random_scan_area['visaCategoryCode']} | 结果：有0个席位"
                    )
            else:
                print(
                    f"当前时间：{formatted_time} |  扫描日期区间{min_date.strftime('%Y-%m-%d')}-{max_date.strftime('%Y-%m-%d')} | 国家：{random_scan_area['missionCode']} | 领区：{random_scan_area['centerName']} | {random_scan_area['visaCategoryCode']} | 结果：{response.status_code}"
                )
        except Exception as e:
            print(e)


for _ in range(14):
    print("正在扫描")
    thread = threading.Thread(target=get_slots)
    thread.start()
