#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查订单OTP验证状态的工具脚本
"""

import json
import time
from datetime import datetime
from RedisClient import RedisClient

def format_timestamp(timestamp):
    """格式化时间戳为可读格式"""
    if timestamp:
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    return "未设置"

def check_otp_status():
    """检查所有订单的OTP验证状态"""
    print("=" * 80)
    print("VFS 订单 OTP 验证状态检查")
    print("=" * 80)
    
    redis_client = RedisClient()
    
    try:
        # 获取所有订单
        all_orders = redis_client.hgetall('vfs_user')
        
        if not all_orders:
            print("没有找到任何订单数据")
            return
        
        print(f"总订单数: {len(all_orders)}\n")
        
        # 统计数据
        stats = {
            'total': 0,
            'pending': 0,
            'with_urn': 0,
            'otp_validated_true': 0,
            'otp_validated_false': 0,
            'otp_not_attempted': 0,
            'by_country': {}
        }
        
        # 详细订单列表
        detailed_orders = []
        
        for order_data in all_orders:
            try:
                if isinstance(order_data, str):
                    order_data = json.loads(order_data)
                
                stats['total'] += 1
                
                order_id = order_data.get('order_id', 'Unknown')
                status = order_data.get('status', 'unknown')
                visa_info = order_data.get('visa_info', {})
                mission_code = visa_info.get('mission_code', 'unknown')
                center_code = visa_info.get('center_code', 'unknown')
                visa_code = visa_info.get('visa_code', 'unknown')
                
                # 统计状态
                if status == 'pending':
                    stats['pending'] += 1
                
                # 统计国家
                if mission_code not in stats['by_country']:
                    stats['by_country'][mission_code] = {
                        'total': 0,
                        'with_urn': 0,
                        'otp_validated': 0,
                        'otp_failed': 0
                    }
                stats['by_country'][mission_code]['total'] += 1
                
                # URN状态
                urn = order_data.get('urn')
                urn_time = order_data.get('updateUrnTime')
                
                if urn:
                    stats['with_urn'] += 1
                    stats['by_country'][mission_code]['with_urn'] += 1
                
                # OTP状态
                otp_validated = order_data.get('otp_validated')
                otp_validation_time = order_data.get('otp_validation_time')
                update_otp_time = order_data.get('update_otp_time')
                
                otp_status = "未尝试"
                if otp_validated == True:
                    otp_status = "验证成功"
                    stats['otp_validated_true'] += 1
                    stats['by_country'][mission_code]['otp_validated'] += 1
                elif otp_validated == False:
                    otp_status = "验证失败"
                    stats['otp_validated_false'] += 1
                    stats['by_country'][mission_code]['otp_failed'] += 1
                else:
                    stats['otp_not_attempted'] += 1
                
                # 客户信息
                clients = order_data.get('clients', [])
                client_name = clients[0].get('name', 'Unknown') if clients else 'Unknown'
                
                detailed_orders.append({
                    'order_id': order_id,
                    'client_name': client_name,
                    'mission_code': mission_code,
                    'center_code': center_code,
                    'visa_code': visa_code,
                    'status': status,
                    'urn': urn[:20] + '...' if urn and len(urn) > 20 else urn or '无',
                    'urn_time': format_timestamp(urn_time),
                    'otp_status': otp_status,
                    'otp_validation_time': format_timestamp(otp_validation_time),
                    'update_otp_time': format_timestamp(update_otp_time)
                })
                
            except (json.JSONDecodeError, TypeError) as e:
                print(f"解析订单数据失败: {e}")
                continue
        
        # 显示统计信息
        print("📊 统计信息")
        print("-" * 40)
        print(f"总订单数: {stats['total']}")
        print(f"待处理订单: {stats['pending']}")
        print(f"已有URN: {stats['with_urn']}")
        print(f"OTP验证成功: {stats['otp_validated_true']}")
        print(f"OTP验证失败: {stats['otp_validated_false']}")
        print(f"未尝试OTP: {stats['otp_not_attempted']}")
        
        # 按国家统计
        print(f"\n🌍 按国家统计")
        print("-" * 40)
        for country, data in sorted(stats['by_country'].items()):
            print(f"{country}: 总数={data['total']}, URN={data['with_urn']}, OTP成功={data['otp_validated']}, OTP失败={data['otp_failed']}")
        
        # 显示详细订单信息
        print(f"\n📋 详细订单信息")
        print("-" * 120)
        print(f"{'订单ID':<15} {'客户':<10} {'国家':<5} {'中心':<8} {'签证':<8} {'状态':<8} {'URN':<22} {'OTP状态':<8} {'验证时间':<19}")
        print("-" * 120)
        
        for order in detailed_orders:
            print(f"{order['order_id']:<15} {order['client_name']:<10} {order['mission_code']:<5} {order['center_code']:<8} {order['visa_code']:<8} {order['status']:<8} {order['urn']:<22} {order['otp_status']:<8} {order['otp_validation_time']:<19}")
        
        print("-" * 120)
        
    except Exception as e:
        print(f"检查OTP状态失败: {e}")

def check_specific_order(order_id):
    """检查特定订单的详细信息"""
    print("=" * 80)
    print(f"检查订单: {order_id}")
    print("=" * 80)
    
    redis_client = RedisClient()
    
    try:
        order_data_str = redis_client.hget('vfs_user', order_id)
        
        if not order_data_str:
            print(f"未找到订单: {order_id}")
            return
        
        order_data = json.loads(order_data_str)
        
        print("📋 订单基本信息")
        print("-" * 40)
        print(f"订单ID: {order_data.get('order_id')}")
        print(f"状态: {order_data.get('status')}")
        print(f"创建时间: {format_timestamp(order_data.get('created_at'))}")
        print(f"更新时间: {format_timestamp(order_data.get('updated_at'))}")
        
        visa_info = order_data.get('visa_info', {})
        print(f"\n🎫 签证信息")
        print("-" * 40)
        print(f"国家代码: {visa_info.get('mission_code')}")
        print(f"中心代码: {visa_info.get('center_code')}")
        print(f"签证类型: {visa_info.get('visa_type')}")
        print(f"签证代码: {visa_info.get('visa_code')}")
        
        clients = order_data.get('clients', [])
        if clients:
            print(f"\n👤 客户信息")
            print("-" * 40)
            for i, client in enumerate(clients, 1):
                print(f"客户{i}: {client.get('name')} ({client.get('passport')})")
        
        print(f"\n🔑 URN信息")
        print("-" * 40)
        print(f"URN: {order_data.get('urn', '无')}")
        print(f"URN更新时间: {format_timestamp(order_data.get('updateUrnTime'))}")
        
        print(f"\n🔐 OTP信息")
        print("-" * 40)
        print(f"OTP验证状态: {order_data.get('otp_validated', '未设置')}")
        print(f"OTP验证时间: {format_timestamp(order_data.get('otp_validation_time'))}")
        print(f"OTP更新时间: {format_timestamp(order_data.get('update_otp_time'))}")
        print(f"OTP获取时间: {format_timestamp(order_data.get('get_otp_time'))}")
        
    except Exception as e:
        print(f"检查订单失败: {e}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        # 检查特定订单
        order_id = sys.argv[1]
        check_specific_order(order_id)
    else:
        # 检查所有订单
        check_otp_status()

if __name__ == "__main__":
    main()
