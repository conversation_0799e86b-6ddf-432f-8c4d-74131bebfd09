# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
from datetime import datetime, timedelta
import string

import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding


redis_client = RedisClient()


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def generate_random_string(length=31):
    characters = string.ascii_letters + string.digits
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


delegate = json.loads(redis_client.get("fast_proxy"))


scan_data = json.loads(redis_client.get("scanData"))
scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']
temp = []
for country in scan_country:
    if len([
        option
        for option in scan_data.get(country)
        if option["scan"] == True
    ]) != 0:
        temp.append(country)

scan_country = temp
center_data = json.loads(redis_client.get("centerData"))

all_missionCode_map = {}
all_centerCode_map = {}
for country_data in center_data:
    all_missionCode_map[country_data.get('missionCode')] = country_data.get('missionCodeName')
    for center in country_data.get('data'):
        all_centerCode_map[f"{country_data.get('missionCode')}-{center.get('isoCode')}"] = center.get('centerName')


def get_centerKey(center):
    return f"{center.get('missionCode')}-{center.get('centerCode')}"


def refresh_scan_data():
    while True:
        time.sleep(10)
        global scan_data
        global scan_country
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        scan_data = json.loads(redis_client.get("scanData"))
        scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']
        temp = []
        for country in scan_country:
            if len([
                option
                for option in scan_data.get(country)
                if option["scan"] == True
            ]) != 0:
                temp.append(country)

        scan_country = temp
        global delegate
        delegate = json.loads(redis_client.get("fast_proxy"))


threading.Thread(target=refresh_scan_data).start()

# 全局变量来控制线程行为
run_all_threads = True
should_run_thread = None


def is_between_8am_and_8pm():
    # 获取当前时间
    current_time = datetime.now().time()

    # 定义早上8点和晚上8点
    start_time = datetime.strptime("08:00", "%H:%M").time()
    end_time = datetime.strptime("20:00", "%H:%M").time()

    # 判断当前时间是否在这个范围内
    return start_time <= current_time <= end_time


def is_date_between(date, start_date, end_date):
    # Convert "dd/mm/yyyy" format date strings to "mm/dd/yyyy"
    start_date = convert_date_format(start_date)
    end_date = convert_date_format(end_date)

    # Convert date strings to datetime objects
    date_obj = datetime.strptime(date, "%m/%d/%Y")
    start_date_obj = datetime.strptime(start_date, "%m/%d/%Y")
    end_date_obj = datetime.strptime(end_date, "%m/%d/%Y")

    # Check if date is within the range
    return start_date_obj <= date_obj <= end_date_obj


def convert_date_format(date):
    parts = date.split("/")
    return f"{parts[1]}/{parts[0]}/{parts[2]}"


def compare_dates(date_str1, date_str2):
    try:
        # 将时间字符串解析为datetime对象
        date1 = datetime.strptime(date_str1, "%d/%m/%Y")
        date2 = datetime.strptime(date_str2, "%d/%m/%Y")

        # 比较datetime对象
        if date1 < date2:
            return True
        else:
            return False
    except ValueError:
        return "无效的日期格式"


def randomize_case(input_string):
    # 将每个字符随机转换为大写或小写
    randomized_string = "".join(
        char.upper() if random.choice([True, False]) else char.lower()
        for char in input_string
    )
    return randomized_string


def generate_string(length=10):
    """生成随机英文字符串（包含大小写字母）"""
    letters = string.ascii_letters  # 包含所有大小写字母
    return ''.join(random.choice(letters) for _ in range(length))


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def gen_del():
    return f"http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in-session-{generate_string(10)}:<EMAIL>:33335"


def slots(random_scan_area, date_str):
    try:
        random_loginUser = random.choice([item for item in redis_client.hgetall(f"{random_scan_area.get('missionCode')}LoginUser") if item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000])
        # 将日期字符串转换为 datetime 对象，并设置时间部分为午夜
        month, day, year = date_str.split('/')
        min_date_str = f"{day}/{month}/{year}"
        r_auth = f"EAAAAN{generate_random_string(597)}="
        url = "https://lift-apicn.vfsglobal.com/appointment/calendar"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": random_loginUser["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{random_scan_area.get('missionCode')}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": random_loginUser['ltsn'],
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"

        }
        data = {
            "countryCode": "chn",
            "missionCode": random_scan_area.get('missionCode'),
            "centerCode": random_scan_area.get('centerCode'),
            "loginUser": random_loginUser["email"],
            "visaCategoryCode": random_scan_area.get('visaCategoryCode'),
            "fromDate": min_date_str,
            "payCode": "",
            "urn": random_loginUser.get("urn")
        }
        dele = gen_del()
        proxies = {
            "http": dele,
            "https": dele,
        }
        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['chrome136', 'safari17_0', 'safari18_4',  'edge101']),
            verify=False,
            timeout=20
        )
        current_time = datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        if response.status_code == 200:
            data = response.json()
            redis_client.hset(f"{random_scan_area.get('missionCode')}LoginUser", random_loginUser.get('email'), json.dumps({**random_loginUser, "r_auth": r_auth, "delegate": dele}))
            if isinstance(data, dict) and data.get('calendars') and len(data.get('calendars')) != 0:
                days = list(dict.fromkeys([item.get('date') for item in data.get('calendars')]))
                userList = redis_client.hgetall(f"{random_scan_area.get('missionCode')}UserDatas")
                if len(userList) == 0:
                    print('无客户')
                    return
                center_code_matched_user = [
                    item
                    for item in userList
                    if item.get("centerCode")
                    == random_scan_area["centerCode"]
                    and item.get("urn")
                    and item.get("vip") != 5
                    and (
                        redis_client.get(
                            f"{item.get('missionCode')}-{item.get('centerCode')}-{item.get('visaTypeCode')}"
                        )
                        == None
                        or (
                            item.get("update_otp_time") != None
                            and int(time.time())
                            - item.get("update_otp_time")
                            < 10000
                        )
                    )
                ]
                suit_slots = []
                for day in days:
                    for user in center_code_matched_user:
                        if is_date_between(
                            day,
                            user.get("start_date"),
                            user.get("end_date"),
                        ):
                            suit_slots.append(day)
                            continue
                print("本次共匹配到" + str(len(suit_slots)) + "个时间")
                for index, day in enumerate(
                    pick_random_elements(suit_slots, 3)
                ):
                    redis_client.publish(
                        f"common_publish_worker",
                        json.dumps({**random_scan_area, "day": convert_date_format(day), "account": random_loginUser.get('email')}),
                    )
                print(
                    f"当前时间：{formatted_time} |  结果：{days}"
                )
            else:
                print(
                    f"当前时间：{formatted_time}  | 结果：有0个席位"
                )
    except Exception as e:
        print(e)


def get_slots(thread_id):
    global should_run_thread
    while True:
        try:
            if run_all_threads or should_run_thread == thread_id:
                country = random.choice(scan_country)
                scan_option = [
                    option
                    for option in scan_data.get(country)
                    if option["scan"] == True
                ]
                adminUsers = [item for item in redis_client.hgetall(f"{country}LoginUser") if item.get('updateTokenTime') != None and int(time.time()) - item.get('updateTokenTime') < 6000]
                if len(scan_option) > 0 and len(adminUsers) > 0:
                    random_scan_option = random.choice(scan_option)
                    random_loginUser = random.choice(adminUsers)
                    r_auth = f"EAAAAN{generate_random_string(597)}="
                    url = f"https://lift-apicn.vfsglobal.com/appointment/CheckIsSlotAvailable"
                    headers = {
                        "Content-Type": "application/json;charset=UTF-8",
                        "Authorize": r_auth,
                        "authorize": random_loginUser["token"],
                        "sec-ch-ua-mobile": "?0",
                        "sec-ch-ua-platform": "macOS",
                        "origin": "https://visa.vfsglobal.com",
                        "sec-fetch-site": "same-site",
                        "route": f"chn/zh/{random_scan_option.get('missionCode')}",
                        "sec-fetch-mode": "cors",
                        "sec-fetch-dest": "empty",
                        "referer": "https://visa.vfsglobal.com/",
                        "accept-encoding": "gzip, deflate, br",
                        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                        "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
                        "cookie": random_loginUser['ltsn']
                    }
                    data = {
                        "countryCode": "chn",
                        "missionCode": random_scan_option.get("missionCode"),
                        "loginUser": random_loginUser["email"],
                        "vacCode": random_scan_option.get("centerCode"),
                        "payCode": "",
                        "visaCategoryCode": random_scan_option.get("visaCategoryCode"),
                        "roleName": "Individual",
                    }
                    proxy = random.choice(delegate)
                    proxies = {
                        "http": "http://kq123-zone-resi-region-in:<EMAIL>:16666",
                        "https": "http://kq123-zone-resi-region-in:<EMAIL>:16666",
                    }
                    response = requests.post(
                        url,
                        json=data,
                        headers=headers,
                        proxies=proxies,
                        impersonate="firefox135",
                        verify=False,
                    )
                    current_time = datetime.now()
                    formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                    if response.status_code == 200:
                        data = response.json()
                        error = data["error"]
                        if error is None:
                            redis_client.publish(
                                "common_refresh_urn",
                                f"{random_scan_option.get('missionCode')}--{random_scan_option.get('centerCode')}--{random_scan_option.get('visaCategoryCode')}",
                            )
                            print(
                                f"当前时间：{formatted_time} | 扫描国家: {all_missionCode_map[random_scan_option.get('missionCode')]} | 扫描领区：{all_centerCode_map[get_centerKey(random_scan_option)]} | 类型：{random_scan_option.get('visaCategoryName')} | 结果：{data}"
                            )
                            earliesDate = convert_date_format(
                                data.get("earliestDate").split(" ")[0]
                            )
                            date_str = data.get('earliestDate').split(" ")[0]
                            month, day, year = date_str.split('/')
                            new_date_str = f"{year}-{month}-{day}"
                            if redis_client.get(
                                f"{random_scan_option.get('missionCode')}-{random_scan_option.get('centerCode')}-{random_scan_option.get('visaCategoryCode')}-Time"
                            ) == None or compare_dates(
                                earliesDate,
                                redis_client.get(
                                    f"{random_scan_option.get('missionCode')}-{random_scan_option.get('centerCode')}-{random_scan_option.get('visaCategoryCode')}-Time"
                                ),
                            ):
                                url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b38addc5-1bc4-46e4-b654-652cc83b6f19"
                                postData = {
                                    "msgtype": "text",
                                    "text": {
                                        "content": f"【{all_missionCode_map[random_scan_option.get('missionCode')]}】【{all_centerCode_map[get_centerKey(random_scan_option)]}】【{random_scan_option.get('visaCategoryName')}】 放号 :  {data.get('earliestDate')}"
                                    },
                                }
                                requests.post(url, json=postData)
                            redis_client.set(
                                f"{random_scan_option.get('missionCode')}-{random_scan_option.get('centerCode')}-{random_scan_option.get('visaCategoryCode')}-Time",
                                earliesDate,
                            )
                            redis_client.hset('vfs_calendar', f"{random_scan_option.get('missionCode')}-{random_scan_option.get('centerCode')}-{random_scan_option.get('visaCategoryCode')}", json.dumps({"country": all_missionCode_map[random_scan_option.get('missionCode')], "missionCode": random_scan_option.get('missionCode'), "visaCategoryCode": random_scan_option.get('visaCategoryCode'), "centerCode": random_scan_option.get('centerCode'),  "center": all_centerCode_map[get_centerKey(random_scan_option)], "type": random_scan_option.get('visaCategoryName'), "date": new_date_str, "update": formatted_time}))
                            userList = redis_client.hgetall(f"{random_scan_option.get('missionCode')}UserDatas")
                            if len(userList) == 0:
                                continue
                            needOtp = redis_client.get(
                                f"{random_scan_option.get('missionCode')}-{random_scan_option.get('centerCode')}-{random_scan_option.get('visaCategoryCode')}"
                            )
                            center_code_matched_user = [
                                item
                                for item in userList
                                if item["centerCode"] == random_scan_option["centerCode"]
                                and item["visaTypeCode"].strip()
                                == random_scan_option["visaCategoryCode"].strip()
                                and item["urn"]
                                and item.get("vip") != 5
                                and (
                                    needOtp == None
                                    or (
                                        item.get("update_otp_time") != None
                                        and int(time.time()) - item.get("update_otp_time")
                                        < 10000
                                    )
                                )
                            ]
                            suit_user = []
                            for user in center_code_matched_user:
                                if is_date_between(
                                    data.get('earliestDate').split(' ')[0], user["start_date"], user["end_date"]
                                ):
                                    suit_user.append(user)
                            if len(suit_user) > 0:
                                slots(random_scan_option, date_str)
                                print("匹配到" + str(len(suit_user)) + "个客户")
                        elif 'Mission does not exist' in error['description']:
                            print(
                                f"当前时间：{formatted_time} | 扫描国家: {all_missionCode_map[random_scan_option.get('missionCode')]} | 扫描领区：{all_centerCode_map[get_centerKey(random_scan_option)]} | 类型：{random_scan_option.get('visaCategoryName')} | 结果：{error['description']}"
                            )
                        else:
                            redis_client.hset('vfs_calendar', f"{random_scan_option.get('missionCode')}-{random_scan_option.get('centerCode')}-{random_scan_option.get('visaCategoryCode')}", json.dumps({"country": all_missionCode_map[random_scan_option.get('missionCode')], "missionCode": random_scan_option.get('missionCode'), "visaCategoryCode": random_scan_option.get('visaCategoryCode'), "centerCode": random_scan_option.get('centerCode'), "center": all_centerCode_map[get_centerKey(random_scan_option)], "type": random_scan_option.get('visaCategoryName'), "date":  "暂无位置", "update": formatted_time}))
                            redis_client.delete(
                                f"{random_scan_option.get('missionCode')}-{random_scan_option.get('centerCode')}-{random_scan_option.get('visaCategoryCode')}-Time",
                            )
                            print(
                                f"当前时间：{formatted_time} | 扫描国家: {all_missionCode_map[random_scan_option.get('missionCode')]} | 扫描领区：{all_centerCode_map[get_centerKey(random_scan_option)]} | 类型：{random_scan_option.get('visaCategoryName')} | 结果：{error['description']}"
                            )
                    else:
                        print(
                            f"当前时间：{formatted_time} | 扫描国家: {all_missionCode_map[random_scan_option.get('missionCode')]} | 扫描领区：{all_centerCode_map[get_centerKey(random_scan_option)]} | 类型：{random_scan_option.get('visaCategoryName')} | 结果：{response.status_code}"
                        )
            else:
                time.sleep(1)
        except Exception as e:
            print(e)


threads = []
for i in range(30):
    print("正在扫描")
    thread_id = i + 1  # Assign a unique identifier to each thread
    thread = threading.Thread(target=get_slots, args=(thread_id,))
    threads.append(thread_id)
    thread.start()


def update_thread_control():
    global run_all_threads, should_run_thread
    while True:
        if is_between_8am_and_8pm():
            run_all_threads = True
        else:
            run_all_threads = False
            # Pick one thread to run, you can assign its identity to 'should_run_thread'
            # This assumes 'threads' is a list of thread identifiers
            should_run_thread = random.choice(threads)  # Need to import random module
        time.sleep(60)


# 创建并启动控制线程
control_thread = threading.Thread(target=update_thread_control)
control_thread.start()
