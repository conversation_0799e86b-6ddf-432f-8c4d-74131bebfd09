# -*- coding: utf-8 -*-
from curl_cffi import requests
import time
import json
import threading
from RedisClientAWS import RedisClient
import random
from datetime import datetime, timedelta
from queue import Queue
import urllib.parse
from twocaptcha import TwoCaptcha
import re
import base64
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
solver = TwoCaptcha("a7d7d55f349865778b327a2c37300e85")
# 创建 RedisClient 实例
redis_client = RedisClient()
delegate = json.loads(redis_client.get("login_proxy"))

scan_data = json.loads(redis_client.get("scanData"))
scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']


def get_current_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_timestamp


def format_rsa_string(compact_key: str) -> str:
    base64_content = compact_key.replace("|", "\n")
    pem_key = f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"
    return pem_key


source_rsa_str = redis_client.get('rsa_str')
rsa_string = format_rsa_string(source_rsa_str)


def encryption(t):
    public_key = serialization.load_pem_public_key(rsa_string.encode())
    encrypted = public_key.encrypt(
        t.encode(),
        padding.PKCS1v15()
    )
    return base64.b64encode(encrypted).decode()


def refresh_scan_data():
    while True:
        time.sleep(10)
        global scan_data
        global scan_country
        global rsa_string
        source_rsa_str = redis_client.get('rsa_str')
        rsa_string = format_rsa_string(source_rsa_str)
        scan_data = json.loads(redis_client.get("scanData"))
        scan_country = [country for country in scan_data.keys() if country != 'deu' and country != 'ita']
        global delegate
        delegate = json.loads(redis_client.get("login_proxy"))


threading.Thread(target=refresh_scan_data).start()


def pick_random_elements(arr, num):
    if num >= len(arr):
        return arr[:]
    shuffled = arr[:]
    random.shuffle(shuffled)
    return shuffled[:num]


def remove_account(account, country):
    redis_client.hdel(f"{country}LoginUser", account.get("email"))


def get_phone_number_dy():
    url = 'http://api.uomsg.com/zc/data.php?code=getPhone&token=65097bfc4b594ffc9c7ab42c8b941fd7&cardType=%E5%AE%9E%E5%8D%A1'
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        return data
    return None


def get_code_dy(phone):
    url = f"http://api.uomsg.com/zc/data.php?code=getMsg&token=36b06908a52e467cab579a325ebcc951&phone={phone}&keyWord=VFS"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.text
        print(data)
        # 使用正则表达式查找验证码
        # 提取验证码
        otp = extract_otp(data)
        if otp:
            return otp
        else:
            return None
    return None


def extract_otp(message):
    # 使用正则表达式查找 6 位数字的验证码
    match = re.search(r'\b\d{6}\b', message)
    if match:
        return match.group(0)
    return None


def get_phone(phone):
    url = f"http://api.haozhuma.com/sms/?api=getPhone&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        print(data)
        # 如果找到匹配项，打印验证码
        if data.get("code") == "0":
            return True
        elif data.get("msg") == "指定专属码手机号不在线":
            return "remove_phone"
        else:
            return False
    return False


def get_code(phone):
    url = f"http://api.haozhuma.com/sms/?api=getMessage&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
    response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
    if response.status_code == 200:
        data = response.json()
        print(data)
        # 如果找到匹配项，打印验证码
        if data.get("yzm"):
            return data.get("yzm")
        else:
            return ""
    return ""


def get_otp(user, country, capResult):
    try:
        print(f"正在获取{user['email']}的OTP")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "username": user.get('email'),
            "password": encryption("Kq123456@"),
        }

        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari18_0_ios', 'safari18_0', 'safari17_2_ios', 'safari17_0']),
            verify=False,
        )
        if response.status_code == 200:
            print(response.text)
            res = response.json()
            if res.get("error") != None:
                remove_account(user, country)
                print(user)
                return False
            elif res.get("accessToken") != None:
                update_token_time = int(time.time())
                user["token"] = res.get("accessToken")
                user["updateTokenTime"] = update_token_time
                user["phone"] = res.get("contactNumber")
                user['ltsn'] = response.headers.get('set-cookie').split(';')[0]
                print(f"账号{user['email']}成功获取Token:{res['accessToken']}")
                redis_client.hset(
                    f"{country}LoginUser", f"{user['email']}", json.dumps({**user})
                )
                return "Token"
            else:
                user["phone"] = res.get("contactNumber")
                redis_client.hset(
                    f"{country}LoginUser", f"{user['email']}", json.dumps({**user})
                )
                print(f"账号{user.get('email')}成功获取OTP", res)
                return "SMS"
        else:
            print(
                f"账号{user.get('email')}获取OTP失败，status_code: {response.status_code}"
            )
        return False
    except Exception as e:
        print(e)
        pass


def get_token(user, country, otp, capResult):
    try:
        print(f"正在获取{user['email']}的票据")
        url = "https://lift-apicn.vfsglobal.com/user/login"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "origin": "https://visa.vfsglobal.com",
            "referer": "http://visa.vfsglobal.com/",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{country}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }

        data = {
            "missioncode": country,
            "countrycode": "chn",
            "captcha_version": "cloudflare-v1",
            "captcha_api_key": capResult,
            "otp": str(otp),
            "username": user["email"],
            "password": encryption("Kq123456@"),
        }

        proxy = random.choice(delegate)
        proxies = {
            "http": proxy,
            "https": proxy,
        }

        response = requests.post(
            url,
            data=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
        )
        if response.status_code == 200:
            data = response.json()
            print(f"获取结果", data)
            if data["error"] is None:
                update_token_time = int(time.time())
                user["token"] = data["accessToken"]
                user["updateTokenTime"] = update_token_time
                user["phone"] = data.get("contactNumber")
                user['ltsn'] = response.headers.get('set-cookie').split(';')[0]
                print(f"{country} 账号{user['email']}成功获取lt_sn:{user['ltsn']}, 成功获取Token:{data['accessToken']}")
                redis_client.hset(
                    f"{country}LoginUser", f"{user['email']}", json.dumps({**user})
                )
                return True
            else:
                remove_account(user, country)
                print(f"{country} 账号{user['email']}获取Token失败")
                return True
        else:
            print(response.status_code)
            return False
    except Exception as e:
        print(e)
        pass


def otp(user, country, max_retries=5, retry_interval=1):
    try:

        type = attempt_get_otp(user, country, max_retries, retry_interval)
        if type == "Token":
            return True
        get_phone = attempt_get_phone(user.get("phone"), max_retries, retry_interval)

        if not get_phone:
            print("指定手机号失败")
            return False
        time.sleep(5)
        otp_code = attempt_get_code(user.get("phone"), 10, 5)
        url = f"http://api.haozhuma.com/sms/?api=cancelRecv&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={user.get('phone')}"
        requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
        if not otp_code:
            print("Failed to get SMS code after maximum retries.")
            remove_account(user, country)
            return False
        if otp_code == "0":
            print(f"{user.get('email')} ：号商已经屏蔽VFS, 将移除该账号")
            remove_account(user, country)
            return False
        if otp_code == "" or len(otp_code) != 6:
            return False
        if not attempt_validate_otp(user, country, otp_code, max_retries, retry_interval):
            print("Failed to validate OTP after maximum retries.")
            return False

        return True

    except Exception as e:
        print(f"An error occurred in the OTP process: {e}")
        return False


def createTask(country):
    try:
        url = "https://api.capmonster.cloud/createTask"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "task": {
                    "type": "TurnstileTaskProxyless",
                    "websiteURL": f"https://visa.vfsglobal.com/chn/zh/{country}/login",
                    "websiteKey": "0x4AAAAAABhlz7Ei4byodYjs",
                },
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            if data.get("errorId") == 0:
                print(f'成功获取task, {data.get("taskId")}')
                return data.get("taskId")
        return None

    except:
        return None


def get_result(id):
    try:
        url = "https://api.capmonster.cloud/getTaskResult"
        res = requests.post(
            url,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            json={
                "clientKey": "0da9a62496582607429e6fe981283808",
                "taskId": id,
            },
            verify=False,
        )
        if res.status_code == 200:
            data = res.json()
            print(data)
            if data.get("errorId") == 0 and data.get("status") == "ready":
                print(f'成功获取cap_token, {data.get("solution").get("token")}')
                return data.get("solution").get("token")
        return None

    except:
        return None


def attempt_get_otp(user, country, max_retries, retry_interval):

    for _ in range(max_retries):
        taskid = attempt_create_task(country)
        if taskid == None:
            return False
        time.sleep(3)
        capResult = attempt_get_result(taskid)
        if capResult == None:
            capResult = solver.turnstile(
                sitekey="0x4AAAAAABhlz7Ei4byodYjs",
                url=f"https://visa.vfsglobal.com/chn/zh/{country}/login",
            ).get("code")
        phone = get_otp(user, country, capResult)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_get_code(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        otp_code = get_code(phone)
        if otp_code != "":
            return otp_code
        time.sleep(retry_interval)
    return ""


def attempt_get_code_dy(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        otp_code = get_code_dy(phone)
        if otp_code != None:
            return otp_code
        time.sleep(retry_interval)
    return ""


def attempt_validate_otp(user, country, otp_code, max_retries, retry_interval):

    for _ in range(max_retries):
        taskid = attempt_create_task(country)
        if taskid == None:
            return False
        time.sleep(3)
        capResult = attempt_get_result(taskid)
        if capResult == None:
            capResult = solver.turnstile(
                sitekey="0x4AAAAAABhlz7Ei4byodYjs",
                url=f"https://visa.vfsglobal.com/chn/zh/{country}/login",
            ).get("code")
        if get_token(user, country, otp_code, capResult):
            return True
        time.sleep(retry_interval)
    return False


def attempt_get_phone(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        phone = get_phone(phone)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_get_phone_dy(phone, max_retries, retry_interval):
    for _ in range(max_retries):
        phone = get_phone_number_dy(phone)
        if phone:
            return phone
        time.sleep(retry_interval)
    return ""


def attempt_create_task(country, max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        id = createTask(country)
        if id:
            return id
        time.sleep(retry_interval)
    return None


def attempt_get_result(id, max_retries=10, retry_interval=5):
    for _ in range(max_retries):
        result = get_result(id)
        if result:
            return result
        time.sleep(retry_interval)
    return None


# 创建一个线程安全的队列来保存需要处理的用户
user_queue = Queue()
# 定义一个函数用于并发处理用户请求


def process_users(country):
    while not user_queue.empty():
        user = user_queue.get()
        otp(user, country)
        user_queue.task_done()


def getNeedProceed(user_list):
    current_time = int(time.time())

    def filter_condition(user):
        return (
            not user.get("updateTokenTime")
            or current_time - user.get("updateTokenTime") > 3400
        )

    need_proceed = list(filter(filter_condition, user_list))
    return need_proceed


def refresh_proxy():
    while True:
        time.sleep(2)
        global delegate
        delegate = json.loads(redis_client.get("login_proxy"))
        print(delegate)


threading.Thread(target=refresh_proxy).start()

while True:
    for country in scan_country:
        scan_option = [
            option
            for option in scan_data.get(country)
            if option["scan"] == True
        ]
        if len(scan_option) > 0:
            loginUsers = redis_client.hgetall(f"{country}LoginUser")
            need_proceed = getNeedProceed(loginUsers)

            if len(need_proceed) != 0:
                print(f"正在刷新{len(need_proceed)}个账号票据")
                # 创建一个线程安全的队列来保存需要处理的用户
                for user in need_proceed:
                    user_queue.put(user)

                num_threads = 30  # 可根据需要调整线程数量
                threads = []
                for _ in range(num_threads):
                    thread = threading.Thread(target=process_users, args=(country,))
                    thread.start()
                    threads.append(thread)

                for thread in threads:
                    thread.join()

                # for user in need_proceed:
                #     get_token(user)
            else:
                print("暂无需要刷新的票据")
    time.sleep(10)
