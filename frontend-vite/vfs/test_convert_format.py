#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试convert_order_to_user_format函数
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_convert_order_to_user_format():
    """测试订单格式转换函数"""
    print("=" * 60)
    print("测试订单格式转换函数")
    print("=" * 60)
    
    # 模拟订单数据
    order_data = {
        "order_id": "2025071015550721622",
        "visa_info": {
            "mission_code": "ita",
            "center_code": "SHI",
            "visa_type": "Shanghai VIP",
            "visa_code": "SSFTME"
        },
        "clients": [{
            "name": "龙春忠",
            "surname_pinyin": "LONG",
            "firstname_pinyin": "CHUNZHONG",
            "passport": "*********",
            "dob": "1978-01-10",
            "passport_expire": "2033-09-12",
            "gender": "男",
            "nationality": "CHN",
            "passport_date": "2023-09-13",
            "sign_location": "贵州/GUIZHOU",
            "bornplace": "贵州/GUIZHOU",
            "marital_status": "",
            "passport_image": "ab8ed25f594e4a63bb273e3e05c948cc.jpg",
            "avatar_image": "",
            "country": "",
            "region": ""
        }],
        "order_info": {
            "accept_vip": False,
            "accept_next_day": True,
            "travel_date": "",
            "customer": "1",
            "price": "2",
            "remark": "3",
            "date_ranges": [{
                "start_date": "2025-07-22",
                "end_date": "2025-07-25"
            }]
        },
        "status": "pending",
        "created_by": "yuan",
        "created_at": **********,
        "updated_at": **********,
        "urn": "URN123456789",
        "updateUrnTime": **********,
        "get_otp_time": **********,
        "update_otp_time": **********
    }
    
    # 模拟账号数据
    account_data = {
        "email": "<EMAIL>",
        "token": "EAAAAH0xTqdpnPLgWCS0c+yo...",
        "phone": "***********",
        "missionCode": "ita",
        "email_server": "*************",
        "email_type": "local_email",
        "redis": "aws",
        "redis_name": "vfs_accounts",
        "countryCode": "chn",
        "redis_key": "<EMAIL>",
        "get_otp_time": **********,
        "updateTokenTime": **********,
        "ltsn": "lt_sn=a267f712-13a2-408c-820e-dcbb5ce809ca"
    }
    
    # 模拟转换函数
    def mock_convert_order_to_user_format(order_data, account_data):
        """模拟转换函数，保持原始字段名"""
        try:
            visa_info = order_data.get('visa_info', {})
            clients = order_data.get('clients', [])
            order_info = order_data.get('order_info', {})
            
            if not clients:
                print(f"订单 {order_data.get('order_id')} 没有客户信息")
                return None
                
            # 使用第一个客户的信息作为主申请人
            main_client = clients[0]
            
            user_data = {
                'order_id': order_data.get('order_id'),
                'missionCode': visa_info.get('mission_code'),
                'centerCode': visa_info.get('center_code'),
                'visaTypeCode': visa_info.get('visa_code'),
                'chnname': main_client.get('name', ''),
                'name': main_client.get('name', ''),  # 保持原始字段名
                'xing': main_client.get('surname_pinyin', ''),
                'passportNO': main_client.get('passport', ''),
                'birthday': main_client.get('dob', ''),
                'expiredDT': main_client.get('passport_expire', ''),
                'gender': main_client.get('gender', ''),
                'countryCode': main_client.get('nationality', 'CHN'),
                'loginUser': account_data.get('email'),
                'phone': account_data.get('phone'),
                'email': account_data.get('email'),
                'remark': order_info.get('remark', ''),
                'urn': order_data.get('urn'),
                'updateUrnTime': order_data.get('updateUrnTime'),
                'get_otp_time': order_data.get('get_otp_time'),
                'update_otp_time': order_data.get('update_otp_time'),
            }
            
            # 如果有多个客户，添加子申请人信息
            if len(clients) > 1:
                child_data = []
                for client in clients[1:]:
                    child_info = {
                        'name': client.get('name', ''),  # 保持原始字段名
                        'xing': client.get('surname_pinyin', ''),
                        'passportNO': client.get('passport', ''),
                        'birthday': client.get('dob', ''),
                        'expiredDT': client.get('passport_expire', ''),
                        'gender': client.get('gender', ''),
                        'countryCode': client.get('nationality', 'CHN'),
                    }
                    child_data.append(child_info)
                user_data['child'] = child_data
                
            return user_data
            
        except Exception as e:
            print(f"转换订单格式失败: {e}")
            return None
    
    # 执行转换
    print("原始订单数据:")
    print(json.dumps(order_data, indent=2, ensure_ascii=False))
    
    print("\n" + "=" * 60)
    print("转换后的用户数据:")
    print("=" * 60)
    
    user_data = mock_convert_order_to_user_format(order_data, account_data)
    
    if user_data:
        print(json.dumps(user_data, indent=2, ensure_ascii=False))
        
        print("\n" + "=" * 60)
        print("字段映射验证:")
        print("=" * 60)
        
        # 验证关键字段映射
        mappings = [
            ("订单ID", order_data.get('order_id'), user_data.get('order_id')),
            ("国家代码", order_data['visa_info']['mission_code'], user_data.get('missionCode')),
            ("中心代码", order_data['visa_info']['center_code'], user_data.get('centerCode')),
            ("签证代码", order_data['visa_info']['visa_code'], user_data.get('visaTypeCode')),
            ("客户姓名", order_data['clients'][0]['name'], user_data.get('chnname')),
            ("客户姓名(name)", order_data['clients'][0]['name'], user_data.get('name')),  # 验证保持原始字段名
            ("姓氏拼音", order_data['clients'][0]['surname_pinyin'], user_data.get('xing')),
            ("护照号", order_data['clients'][0]['passport'], user_data.get('passportNO')),
            ("生日", order_data['clients'][0]['dob'], user_data.get('birthday')),
            ("护照过期", order_data['clients'][0]['passport_expire'], user_data.get('expiredDT')),
            ("性别", order_data['clients'][0]['gender'], user_data.get('gender')),
            ("国籍", order_data['clients'][0]['nationality'], user_data.get('countryCode')),
            ("登录邮箱", account_data['email'], user_data.get('loginUser')),
            ("手机号", account_data['phone'], user_data.get('phone')),
            ("URN", order_data.get('urn'), user_data.get('urn')),
        ]
        
        for field_name, original, converted in mappings:
            status = "✓" if original == converted else "✗"
            print(f"{status} {field_name}: {original} -> {converted}")
        
        print("\n" + "=" * 60)
        print("重点验证: name字段是否保持原始值")
        print("=" * 60)
        original_name = order_data['clients'][0]['name']
        converted_name = user_data.get('name')
        firstname_pinyin = order_data['clients'][0]['firstname_pinyin']
        
        print(f"原始name字段: {original_name}")
        print(f"firstname_pinyin字段: {firstname_pinyin}")
        print(f"转换后name字段: {converted_name}")
        
        if converted_name == original_name:
            print("✓ name字段正确保持了原始值")
        else:
            print("✗ name字段未保持原始值")
            
    else:
        print("转换失败")

def test_with_multiple_clients():
    """测试多客户情况"""
    print("\n" + "=" * 60)
    print("测试多客户情况")
    print("=" * 60)
    
    # 模拟多客户订单数据
    order_data = {
        "order_id": "MULTI2025001",
        "visa_info": {
            "mission_code": "fra",
            "center_code": "FRBE",
            "visa_code": "TOU"
        },
        "clients": [
            {
                "name": "张三",
                "surname_pinyin": "ZHANG",
                "firstname_pinyin": "SAN",
                "passport": "E12345678",
                "dob": "1980-01-01",
                "passport_expire": "2030-01-01",
                "gender": "男",
                "nationality": "CHN"
            },
            {
                "name": "李四",
                "surname_pinyin": "LI",
                "firstname_pinyin": "SI",
                "passport": "E87654321",
                "dob": "1985-02-02",
                "passport_expire": "2030-02-02",
                "gender": "女",
                "nationality": "CHN"
            }
        ],
        "order_info": {
            "remark": "家庭旅游"
        }
    }
    
    account_data = {
        "email": "<EMAIL>",
        "phone": "***********"
    }
    
    # 这里可以调用实际的转换函数进行测试
    print("多客户订单数据:")
    print(json.dumps(order_data, indent=2, ensure_ascii=False))
    
    print("\n验证要点:")
    print("1. 主申请人应该使用第一个客户的信息")
    print("2. name字段应该保持原始值（张三），而不是firstname_pinyin（SAN）")
    print("3. 子申请人信息应该正确添加到child数组中")

if __name__ == "__main__":
    test_convert_order_to_user_format()
    test_with_multiple_clients()
