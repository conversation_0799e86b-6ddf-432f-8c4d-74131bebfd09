# -*- coding: utf-8 -*-
"""
VFS URN Manager - 重构版本
负责管理VFS签证申请的URN刷新和OTP获取
数据源：vfs_user (订单数据) 和 vfs_accounts (账号数据)
"""

import threading
import queue
from queue import Queue
from curl_cffi import requests
import time as tm
import json
import random
import string
import base64
import logging
from datetime import datetime
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from RedisClient import RedisClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(threadName)-15s | %(funcName)-20s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 初始化Redis客户端
redis_client = RedisClient()

# 全局变量
processing_users = set()  # 正在处理的用户集合
user_queue = Queue()      # 用户处理队列
delegate = []             # 代理列表
rsa_string = ""           # RSA公钥字符串

# 国家和中心代码映射
center_data = []
all_missionCode_map = {}
all_centerCode_map = {}


def get_current_timestamp():
    """获取当前时间戳"""
    now = datetime.now()
    return now.strftime("%Y-%m-%dT%H:%M:%S")


def format_rsa_string(compact_key: str) -> str:
    """格式化RSA公钥字符串"""
    if not compact_key:
        return ""
    base64_content = compact_key.replace("|", "\n")
    return f"-----BEGIN PUBLIC KEY-----\n{base64_content}\n-----END PUBLIC KEY-----"


def encryption(text):
    """RSA加密"""
    try:
        if not rsa_string:
            logger.error("RSA公钥未初始化")
            return ""
        public_key = serialization.load_pem_public_key(rsa_string.encode())
        encrypted = public_key.encrypt(
            text.encode(),
            padding.PKCS1v15()
        )
        return base64.b64encode(encrypted).decode()
    except Exception as e:
        logger.error(f"RSA加密失败: {e}")
        return ""


def generate_random_string(length=31):
    """生成随机字符串"""
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


def get_available_accounts(mission_code):
    """获取指定国家的可用账号列表"""
    try:
        all_accounts = redis_client.hgetall("vfs_accounts")
        available_accounts = []
        current_time = int(tm.time())

        for account_data in all_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                # 检查账号是否属于指定国家且token有效
                if (account_data.get('missionCode') == mission_code and
                    account_data.get('token') and
                    account_data.get('updateTokenTime') and
                    current_time - account_data.get('updateTokenTime') < 6000 and
                        current_time - account_data.get('updateTokenTime') > 60):
                    available_accounts.append(account_data)

            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析账号数据失败: {e}")
                continue

        logger.info(f"国家 {mission_code} 可用账号数量: {len(available_accounts)}")
        return available_accounts

    except Exception as e:
        logger.error(f"获取可用账号失败: {e}")
        return []


def get_visa_config(mission_code, center_code, visa_code):
    """获取签证类型配置，判断是否需要OTP"""
    try:
        center_data_str = redis_client.hget('vfs_center_data', mission_code)
        if not center_data_str:
            logger.warning(f"未找到国家 {mission_code} 的中心数据")
            return None

        center_data = json.loads(center_data_str)

        # 遍历中心数据查找匹配的签证类型
        for center in center_data.get('data', []):
            if center.get('isoCode') == center_code:
                # 递归查找签证类型配置
                def find_visa_config(items):
                    for item in items:
                        if item.get('code') == visa_code:
                            return item
                        if 'sub' in item:
                            result = find_visa_config(item['sub'])
                            if result:
                                return result
                    return None

                visa_config = find_visa_config(center.get('sub', []))
                if visa_config:
                    return visa_config

        logger.warning(f"未找到签证类型配置: {mission_code}/{center_code}/{visa_code}")
        return None

    except Exception as e:
        logger.error(f"获取签证配置失败: {e}")
        return None


def is_otp_required(mission_code, center_code, visa_code):
    """判断指定签证类型是否需要OTP"""
    try:
        visa_config = get_visa_config(mission_code, center_code, visa_code)
        if visa_config:
            return visa_config.get('isApplicantOTPEnabled', False)
        return False
    except Exception as e:
        logger.error(f"判断OTP需求失败: {e}")
        return False


def is_waitlist_enabled(mission_code, center_code, visa_code):
    """判断指定签证类型是否启用等待列表"""
    try:
        visa_config = get_visa_config(mission_code, center_code, visa_code)
        if visa_config:
            return visa_config.get('iswaitlist', False)
        return False
    except Exception as e:
        logger.error(f"判断等待列表需求失败: {e}")
        return False


def is_ocr_enabled(mission_code, center_code, visa_code):
    """判断指定签证类型是否需要OCR护照识别"""
    try:
        visa_config = get_visa_config(mission_code, center_code, visa_code)
        if visa_config:
            return visa_config.get('isOCREnable', False)
        return False
    except Exception as e:
        logger.error(f"判断OCR需求失败: {e}")
        return False


def get_pending_orders():
    """获取待处理的订单列表"""
    try:
        all_orders = redis_client.hgetall('vfs_user')
        pending_orders = []
        current_time = int(tm.time())

        for order_data in all_orders:
            try:
                if isinstance(order_data, str):
                    order_data = json.loads(order_data)

                # 检查订单状态和URN刷新需求
                if (order_data.get('status') == 'pending' and
                    order_data.get('visa_info') and
                        order_data.get('clients')):

                    # 检查是否需要刷新URN
                    needs_refresh = False

                    # 检查URN是否存在或过期（改为10000秒）
                    if (not order_data.get('urn') or
                        not order_data.get('updateUrnTime') or
                            current_time - order_data.get('updateUrnTime') > 10000):
                        needs_refresh = True

                    # 检查OTP时间（改为10000秒）
                    if (current_time - order_data.get('updateUrnTime', 0) > 180 and
                        (not order_data.get('update_otp_time') or
                         current_time - order_data.get('update_otp_time') > 10000)):
                        needs_refresh = True

                    if needs_refresh:
                        pending_orders.append(order_data)

            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析订单数据失败: {e}")
                continue

        logger.info(f"待处理订单数量: {len(pending_orders)}")
        return pending_orders

    except Exception as e:
        logger.error(f"获取待处理订单失败: {e}")
        return []


def get_passport_image(passport_image):
    """从图片服务器获取护照图片的base64编码"""
    try:
        url = f"http://120.27.241.45:5005/api/passport_image/{passport_image}"
        response = requests.get(url, timeout=30)

        if response.status_code == 200:
            # 假设返回的是base64编码的图片
            return True, response.text
        elif response.status_code == 404:
            logger.warning(f"护照图片不存在: {passport_image}")
            return False, 404
        else:
            logger.error(f"获取护照图片失败，状态码: {response.status_code}")
            return False, response.status_code

    except Exception as e:
        logger.error(f"获取护照图片异常: {e}")
        return False, str(e)


def upload_passport_document(order_data, client_data, account):
    """上传护照文档进行OCR识别"""
    try:
        visa_info = order_data.get('visa_info', {})
        mission_code = visa_info.get('mission_code')
        center_code = visa_info.get('center_code')
        visa_code = visa_info.get('visa_code')

        passport_image = client_data.get('passport_image')
        if not passport_image:
            logger.warning(f"客户 {client_data.get('name')} 没有护照图片")
            return False

        # 获取护照图片
        status, base64_image = get_passport_image(passport_image)
        if not status:
            if base64_image == 404:
                logger.warning(f"客户 {client_data.get('name')} 缺少护照图片")
            else:
                logger.error(f"获取护照图片失败: {base64_image}")
            return False

        # 构建上传请求
        url = "https://lift-apicn.vfsglobal.com/appointment/UploadApplicantDocument"

        # 生成随机授权头
        r_auth = f"EAAAANkoweAPl1c92tUMRWNCuOfMZw2XjSr1EcsVDtuqr18JsegEk1l3C6f7VvsD07lOiYz3/wFlhsC3jSGYpxjhITkvpQw0fbUp/FKE2JzaVAiq6Gye8LKchEL3yruYwYnsUiMw+7KqtabN0gfC0NP7jbyPIyWHyYHKoSs63jGk+/JKlFJXHQPEkrEAheFqoN/6eQ8H0sWaFQrb644IKM7N8HjRhkcQXB5MYsn{generate_random_string()}+BKVnsDVvsTqf9jVy6xG960twrgfiyyV9YJjWDEcPcw8bpbrgjV9+C3Snvxi7w3RvcZCbeugfy1Ci7ATQH4soTWNM0D0b3UZWXHmfCp2phPujse36H+e2yI4+FJ09IWjCaGr0fKEVMBHzzy5Ac63rE27z/J0qVJBnzNVs6dxDH3BX0KMZoj8+sUHP9K8+gZGcJoUmC1/1PVG87JGuHvu7YZSDubbjg0PIAmbec3MeoD3J0sz+AcJ2zWujjYRNodhBEfqYYeyugZaEau1vDmqk07iDphprFenX2Jo/j2iw3l/7sdR0YLMqqTV5d/OOCUKcMNyuvUNrcxFk4LojCvfI="

        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "route": f"chn/zh/{mission_code}",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": account.get('ltsn', '')
        }

        # 处理签证类型代码
        visa_category_code = visa_code
        if visa_code == "Schengen":
            visa_category_code = "Schengen "

        data = {
            "urn": "",
            "countryCode": "chn",
            "missionCode": mission_code,
            "loginUser": account.get("email"),
            "languageCode": "zh-CN",
            "centerCode": center_code,
            "visaCategoryCode": visa_category_code,
            "fileBytes": base64_image,
            "selfiImageFileBytes": ""
        }

        logger.info(f"正在上传客户 {client_data.get('name')} 的护照图片进行OCR识别")

        # 使用代理
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
            timeout=60
        )

        if response.status_code == 200:
            response_data = response.json()
            logger.info(f"护照上传响应: {response_data}")

            if response_data.get("error") is None:
                upload_guid = response_data.get('uploadDocumentGUID')
                if upload_guid:
                    logger.info(f"客户 {client_data.get('name')} 护照OCR识别成功，GUID: {upload_guid}")
                    # 将GUID保存到客户数据中
                    client_data['GUID'] = upload_guid
                    return True
                else:
                    logger.warning(f"客户 {client_data.get('name')} 护照OCR识别未返回GUID")
                    return False
            else:
                error_msg = response_data.get("error", {}).get("description", "未知错误")
                logger.warning(f"客户 {client_data.get('name')} 护照OCR识别失败: {error_msg}")
                return False
        else:
            logger.error(f"护照上传请求失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"上传护照文档异常: {e}")
        return False


def get_code_from_sms(phone):
    """从短信平台获取验证码"""
    try:
        url = f"http://api.haozhuma.com/sms/?api=getMessage&token=12979f4ebab7cfffc998fa47468d2cfe856737ff1745c2aeebcdf77de8ca30cf6a3d7927acaf80fa1c921eed962625876572a81c2e15f0b4a7b497091f5fb39ca9aa29d344ab1fceb38f21bd0ed8140d&sid=98424&phone={phone}"
        response = requests.get(url, impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']), verify=False)
        if response.status_code == 200:
            data = response.json()
            logger.debug(f"短信平台响应: {data}")
            # 如果找到匹配项，返回验证码
            if data.get("yzm"):
                return data.get("yzm")
            else:
                return ""
        return ""
    except Exception as e:
        logger.error(f"从短信平台获取验证码失败: {e}")
        return ""


def get_code_from_email(email):
    """从Redis获取邮箱验证码"""
    try:
        otp_code = redis_client.get(email)
        if otp_code:
            logger.debug(f"从邮箱 {email} 获取到验证码: {otp_code}")
            return otp_code
        return ""
    except Exception as e:
        logger.error(f"从邮箱获取验证码失败: {e}")
        return ""


def wait_for_otp_code(user_data, otp_method, account):
    """等待获取OTP验证码"""
    try:
        max_attempts = 30  # 最多等待5分钟 (30次 * 10秒)

        for attempt in range(max_attempts):
            otp_code = ""

            if "EMAIL" in otp_method:
                # 从Redis获取邮箱验证码
                otp_code = get_code_from_email(account.get("email"))
            elif "SMS" in otp_method:
                # 从短信平台获取验证码
                otp_code = get_code_from_sms(account.get("phone"))

            if otp_code:
                logger.info(f"客户 {user_data.get('chnname')} 获取到OTP验证码: {otp_code}")
                return otp_code

            # 等待10秒后重试
            tm.sleep(10)
            logger.debug(f"等待OTP验证码，第 {attempt + 1}/{max_attempts} 次尝试")

        logger.warning(f"客户 {user_data.get('chnname')} 等待OTP验证码超时")
        return ""

    except Exception as e:
        logger.error(f"等待OTP验证码失败: {e}")
        return ""


def validate_otp(user_data, otp_code, account):
    """验证OTP验证码"""
    try:
        mission_code = user_data.get('missionCode')

        url = "https://lift-apicn.vfsglobal.com/appointment/applicantotp"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": account.get('ltsn', '')
        }

        data = {
            "urn": user_data.get("urn"),
            "loginUser": user_data.get("loginUser"),
            "missionCode": mission_code,
            "languageCode": "zh-CN",
            "countryCode": "chn",
            "centerCode": user_data.get("centerCode"),
            "captcha_version": "",
            "captcha_api_key": "",
            "OTP": otp_code,
            "otpAction": "VALIDATE",
            "userAction": None,
        }

        # 使用代理
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            res = response.json()
            if res.get("isOTPValidated") == True:
                logger.info(f"客户 {user_data.get('chnname')} OTP验证成功")
                return True
            else:
                logger.warning(f"客户 {user_data.get('chnname')} OTP验证失败: {res}")
                return False
        else:
            logger.error(f"OTP验证请求失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"OTP验证失败: {e}")
        return False


def get_otp(user_data):
    """获取并验证OTP验证码"""
    try:
        mission_code = user_data.get('missionCode')
        available_accounts = get_available_accounts(mission_code)

        if not available_accounts:
            logger.warning(f"国家 {mission_code} 没有可用账号")
            return False

        account = random.choice(available_accounts)

        url = "https://lift-apicn.vfsglobal.com/appointment/applicantotp"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": account.get('ltsn', '')
        }

        data = {
            "urn": user_data.get("urn"),
            "loginUser": account.get("email"),
            "lOGiNUser": user_data.get("loginUser"),
            "missionCode": mission_code,
            "countryCode": "chn",
            "centerCode": user_data.get("centerCode"),
            "captcha_version": "",
            "captcha_api_key": "",
            "OTP": "",
            "otpAction": "GENERATE",
            "cultureCode": "zh-CN",
        }

        # 使用代理
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            json=data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            res = response.json()
            if res.get("isOTPGenerated") == True:
                logger.info(f"客户 {user_data.get('chnname')} 成功触发OTP生成")

                # 检查OTP生成方式
                otp_method = res.get("OTPGeneratedMethed", "")
                logger.info(f"客户 {user_data.get('chnname')} OTP生成方式: {otp_method}")

                if otp_method:
                    # 等待获取验证码
                    otp_code = wait_for_otp_code(user_data, otp_method, account)

                    if otp_code:
                        # 验证OTP
                        validation_result = validate_otp(user_data, otp_code, account)
                        if validation_result:
                            logger.info(f"客户 {user_data.get('chnname')} OTP完整流程成功")
                            return True
                        else:
                            logger.warning(f"客户 {user_data.get('chnname')} OTP验证失败")
                            return False
                    else:
                        logger.warning(f"客户 {user_data.get('chnname')} 未能获取到OTP验证码")
                        return False
                else:
                    logger.warning(f"客户 {user_data.get('chnname')} 未返回OTP生成方式")
                    return False
            else:
                logger.warning(f"客户 {user_data.get('chnname')} OTP生成失败: {res}")
                return False
        else:
            logger.error(f"OTP请求失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"获取OTP失败: {e}")
        return False


def refresh_global_config():
    """刷新全局配置"""
    global delegate, rsa_string, center_data, all_missionCode_map, all_centerCode_map

    try:
        # 刷新代理列表
        proxy_data = redis_client.get("fast_proxy")
        if proxy_data:
            delegate = json.loads(proxy_data)
            logger.info(f"刷新代理列表，数量: {len(delegate)}")

        # 刷新RSA公钥
        source_rsa_str = redis_client.get('rsa_str')
        if source_rsa_str:
            rsa_string = format_rsa_string(source_rsa_str)
            logger.info("RSA公钥已刷新")

        # 刷新中心数据 - 从centerData读取
        center_data_str = redis_client.get("centerData")
        if center_data_str:
            center_data = json.loads(center_data_str)
            all_missionCode_map.clear()
            all_centerCode_map.clear()

            for country_data in center_data:
                mission_code = country_data.get('missionCode')
                mission_name = country_data.get('missionCodeName')
                if mission_code and mission_name:
                    all_missionCode_map[mission_code] = mission_name

                for center in country_data.get('data', []):
                    center_code = center.get('isoCode')
                    center_name = center.get('centerName')
                    if center_code and center_name:
                        all_centerCode_map[center_code] = center_name

            logger.info(f"刷新centerData，国家数量: {len(all_missionCode_map)}, 中心数量: {len(all_centerCode_map)}")

        # 同时从vfs_center_data读取详细配置信息
        try:
            vfs_center_keys = redis_client.hkeys('vfs_center_data')
            if vfs_center_keys:
                logger.info(f"发现vfs_center_data中有 {len(vfs_center_keys)} 个国家的详细配置")
                # 补充从vfs_center_data读取的国家名称映射
                for mission_code in vfs_center_keys:
                    if mission_code not in all_missionCode_map:
                        center_data_str = redis_client.hget('vfs_center_data', mission_code)
                        if center_data_str:
                            try:
                                center_data = json.loads(center_data_str)
                                mission_name = center_data.get('missionCodeName')
                                if mission_name:
                                    all_missionCode_map[mission_code] = mission_name
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            logger.warning(f"读取vfs_center_data失败: {e}")

        # 清理处理中的用户集合
        processing_users.clear()

    except Exception as e:
        logger.error(f"刷新全局配置失败: {e}")


def config_refresh_thread():
    """配置刷新线程"""
    while True:
        try:
            refresh_global_config()
            tm.sleep(60)  # 每分钟刷新一次
        except Exception as e:
            logger.error(f"配置刷新线程异常: {e}")
            tm.sleep(10)


def get_urn(order_data):
    """获取订单URN"""
    try:
        visa_info = order_data.get('visa_info', {})
        mission_code = visa_info.get('mission_code')
        available_accounts = get_available_accounts(mission_code)

        if not available_accounts:
            logger.warning(f"国家 {mission_code} 没有可用账号")
            return False

        account = random.choice(available_accounts)

        # 生成随机授权头
        r_auth = f"EAAAAN{generate_random_string(597)}="

        url = "https://lift-apicn.vfsglobal.com/appointment/applicants"
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Authorize": r_auth,
            "authorize": account["token"],
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "route": f"chn/zh/{mission_code}",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "clientsource": encryption(f"GA;{get_current_timestamp()}Z"),
            "cookie": account.get('ltsn', '')
        }

        # 获取客户信息
        clients = order_data.get('clients', [])
        if not clients:
            logger.warning(f"订单 {order_data.get('order_id')} 没有客户信息")
            return False

        # 构建申请人列表
        applicant_list = []

        # 主申请人
        main_client = clients[0]
        main_applicant = {
            "urn": "",
            "arn": "",
            "loginUser": account.get("email"),
            "firstName": main_client.get("name", ""),
            "middleName": "",
            "lastName": main_client.get("surname_pinyin", ""),
            "salutation": "",
            "gender": 1 if main_client.get("gender") == "男" else 2,
            "nationalId": None,
            "VisaToken": None,
            "contactNumber": str(account.get("phone", "")),
            "dialCode": "86",
            "passportNumber": main_client.get("passport", ""),
            "confirmPassportNumber": main_client.get("passport", ""),
            "passportExpirtyDate": main_client.get("passport_expire", ""),
            "dateOfBirth": main_client.get("dob", ""),
            "emailId": account.get("email"),
            "nationalityCode": main_client.get("nationality", "CHN"),
            "state": None,
            "city": None,
            "isEndorsedChild": False,
            "applicantType": 0,
            "addressline1": None,
            "addressline2": None,
            "pincode": None,
            "referenceNumber": None,
            "vlnNumber": None,
            "applicantGroupId": 0,
            "parentPassportNumber": "",
            "parentPassportExpiry": "",
            "dateOfDeparture": "11/05/2024",
            "helloVerifyNumber": "",
            "gwfNumber": "",
            "entryType": "",
            "eoiVisaType": "",
            "isAutoRefresh": True,
            "passportType": "",
            "employerContactNumber": "",
            "employerDialCode": "",
            "employerEmailId": "",
            "employerFirstName": "",
            "employerLastName": "",
            "familyReunificationCerificateNumber": "",
            "vfsReferenceNumber": "",
            "PVRequestRefNumber": "",
            "PVStatus": "",
            "PVStatusDescription": "",
            "PVCanAllowRetry": True,
            "PVisVerified": False,
            "ipAddress": None,
        }
        applicant_list.append(main_applicant)

        # 添加子申请人（如果有）
        if len(clients) > 1:
            for child in clients[1:]:
                child_applicant = {
                    "urn": "",
                    "arn": "",
                    "loginUser": account.get("email"),
                    "firstName": child.get("name", ""),
                    "middleName": "",
                    "lastName": child.get("surname_pinyin", ""),
                    "salutation": "",
                    "gender": 1 if child.get("gender") == "男" else 2,
                    "nationalId": None,
                    "VisaToken": None,
                    "contactNumber": str(account.get("phone", "")),
                    "dialCode": "86",
                    "passportNumber": child.get("passport", ""),
                    "confirmPassportNumber": child.get("passport", ""),
                    "passportExpirtyDate": child.get("passport_expire", ""),
                    "dateOfBirth": child.get("dob", ""),
                    "emailId": account.get("email"),
                    "nationalityCode": child.get("nationality", "CHN"),
                    "state": None,
                    "city": None,
                    "isEndorsedChild": False,
                    "applicantType": 0,
                    "addressline1": None,
                    "addressline2": None,
                    "pincode": None,
                    "referenceNumber": None,
                    "vlnNumber": None,
                    "applicantGroupId": 0,
                    "parentPassportNumber": "",
                    "parentPassportExpiry": "",
                    "dateOfDeparture": "11/05/2024",
                    "helloVerifyNumber": "",
                    "gwfNumber": "",
                    "entryType": "",
                    "eoiVisaType": "",
                    "isAutoRefresh": True,
                    "passportType": "",
                    "employerContactNumber": "",
                    "employerDialCode": "",
                    "employerEmailId": "",
                    "employerFirstName": "",
                    "employerLastName": "",
                    "familyReunificationCerificateNumber": "",
                    "vfsReferenceNumber": "",
                    "PVRequestRefNumber": "",
                    "PVStatus": "",
                    "PVStatusDescription": "",
                    "PVCanAllowRetry": True,
                    "PVisVerified": False,
                    "ipAddress": None,
                }
                applicant_list.append(child_applicant)

        # 检查是否需要OCR护照识别
        center_code = visa_info.get('center_code')
        visa_code = visa_info.get('visa_code')
        ocr_required = is_ocr_enabled(mission_code, center_code, visa_code)

        if ocr_required:
            logger.info(f"签证类型 {mission_code}/{center_code}/{visa_code} 需要OCR护照识别")

            # 为所有客户上传护照进行OCR识别
            ocr_success = True
            for client in clients:
                if not upload_passport_document(order_data, client, account):
                    logger.warning(f"客户 {client.get('name')} 护照OCR识别失败")
                    ocr_success = False
                    break

            if not ocr_success:
                logger.error(f"订单 {order_data.get('order_id')} OCR识别失败，跳过URN获取")
                return False

        # 检查是否启用等待列表
        waitlist_enabled = is_waitlist_enabled(mission_code, center_code, visa_code)

        # 构建请求数据
        request_data = {
            "countryCode": "chn",
            "missionCode": mission_code,
            "centerCode": center_code,
            "loginUser": account.get("email"),
            "visaCategoryCode": visa_code,
            "isEdit": False,
            "feeEntryTypeCode": None,
            "feeExemptionTypeCode": None,
            "feeExemptionDetailsCode": None,
            "applicantList": applicant_list,
            "languageCode": "zh-CN",
            "isWaitlist": waitlist_enabled,
            "roleName": "Individual",
        }

        logger.info(f"正在刷新客户 {main_client.get('name')}，护照: {main_client.get('passport')}")

        # 使用代理
        proxy = random.choice(delegate) if delegate else None
        proxies = {
            "http": proxy,
            "https": proxy,
        } if proxy else None

        response = requests.post(
            url,
            json=request_data,
            headers=headers,
            proxies=proxies,
            impersonate=random.choice(['safari17_2_ios', 'safari17_0', 'safari18_0_ios', 'safari18_0']),
            verify=False,
            timeout=30
        )

        if response.status_code == 200:
            response_data = response.json()

            if response_data.get("error") is None and response_data.get("urn"):
                # 成功获取URN
                urn = response_data["urn"]
                update_time = int(tm.time())

                logger.info(f"客户 {main_client.get('name')} 成功获取URN: {urn}")

                # 更新原始订单数据
                order_id = order_data.get('order_id')
                if order_id:
                    # 直接更新当前订单数据
                    order_data['urn'] = urn
                    order_data['updateUrnTime'] = update_time

                    # 检查是否需要OTP
                    otp_required = is_otp_required(mission_code, center_code, visa_code)

                    if otp_required:
                        order_data['get_otp_time'] = update_time

                    # 更新Redis中的订单数据
                    redis_client.hset('vfs_user', order_id, json.dumps(order_data, ensure_ascii=False))

                    # 如果需要OTP，尝试获取OTP
                    if otp_required:
                        order_data['urn'] = urn
                        otp_result = get_otp(order_data)
                        if otp_result:
                            order_data['update_otp_time'] = int(tm.time())
                            order_data['otp_validated'] = True  # 添加OTP验证成功标记
                            order_data['otp_validation_time'] = int(tm.time())  # 添加验证时间
                            redis_client.hset('vfs_user', order_id, json.dumps(order_data, ensure_ascii=False))
                            logger.info(f"客户 {main_client.get('name')} 成功完成OTP验证")
                        else:
                            order_data['otp_validated'] = False  # 添加OTP验证失败标记
                            redis_client.hset('vfs_user', order_id, json.dumps(order_data, ensure_ascii=False))
                            logger.warning(f"客户 {main_client.get('name')} OTP验证失败")

                return True

            else:
                # 处理错误情况
                error_msg = response_data.get("error", {}).get("description", "未知错误")
                logger.warning(f"客户 {main_client.get('name')} 获取URN失败: {error_msg}")

                # 根据错误类型进行不同处理
                if any(keyword in error_msg for keyword in [
                    "You have exceeded the limit restricted",
                    "Invalid request",
                    "Invalid inputs",
                    "We are sorry but no appointment slots are currently available",
                    "Please wait a few moments before saving your details and continuing"
                ]):
                    # 这些错误不需要特殊处理，只记录日志
                    pass
                else:
                    # 其他错误可能需要通知
                    country_name = all_missionCode_map.get(mission_code, mission_code)
                    center_name = all_centerCode_map.get(center_code, center_code)
                    logger.error(f"{country_name}:{center_name} 客户 {main_client.get('name')} 获取URN失败: {error_msg}")

                return False
        else:
            logger.error(f"URN请求失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        logger.error(f"获取URN异常: {e}")
        return False
    finally:
        # 清理处理标记
        clients = order_data.get('clients', [])
        if clients:
            main_passport = clients[0].get('passport', '')
            user_id = f"{mission_code}-{center_code}-{visa_code}-{main_passport}"
            if user_id in processing_users:
                processing_users.remove(user_id)


def process_users():
    """处理订单队列的工作线程"""
    while True:
        try:
            order_data = user_queue.get(timeout=1)
            get_urn(order_data)
            user_queue.task_done()
        except queue.Empty:
            tm.sleep(0.1)
        except Exception as e:
            logger.error(f"处理订单队列异常: {e}")
            tm.sleep(1)


def refresh_urn_for_mission(mission_code, center_code, visa_code):
    """为指定的签证类型刷新URN"""
    try:
        logger.info(f"开始刷新 {mission_code} {center_code} {visa_code}")

        # 获取待处理订单
        pending_orders = get_pending_orders()

        # 过滤出匹配的订单
        matching_orders = []
        for order in pending_orders:
            visa_info = order.get('visa_info', {})
            if (visa_info.get('mission_code') == mission_code and
                visa_info.get('center_code') == center_code and
                    visa_info.get('visa_code') == visa_code):
                matching_orders.append(order)

        if not matching_orders:
            logger.info(f"没有找到匹配的待处理订单: {mission_code} {center_code} {visa_code}")
            return False

        logger.info(f"找到 {len(matching_orders)} 个待处理订单")

        # 获取可用账号
        available_accounts = get_available_accounts(mission_code)
        if not available_accounts:
            logger.warning(f"国家 {mission_code} 没有可用账号，跳过处理")
            return False

        # 处理每个订单
        processed_count = 0
        for order in matching_orders:
            try:
                # 随机选择一个账号
                account = random.choice(available_accounts)

                # 直接使用订单数据，添加账号信息
                order['loginUser'] = account.get('email')
                order['phone'] = account.get('phone')
                order['email'] = account.get('email')

                # 获取主要客户信息用于生成用户ID
                clients = order.get('clients', [])
                if not clients:
                    continue

                main_passport = clients[0].get('passport', '')

                # 检查是否已在处理中
                user_id = f"{mission_code}-{center_code}-{visa_code}-{main_passport}"
                if user_id in processing_users:
                    continue

                # 添加到处理队列
                user_queue.put(order)
                processing_users.add(user_id)
                processed_count += 1

            except Exception as e:
                logger.error(f"处理订单失败: {e}")
                continue

        logger.info(f"已将 {processed_count} 个订单添加到处理队列")
        return processed_count > 0

    except Exception as e:
        logger.error(f"刷新URN失败: {e}")
        return False


def continuous_urn_refresh():
    """持续刷新URN的主循环"""
    while True:
        try:
            logger.info("开始新一轮URN刷新检查...")

            # 获取待处理订单
            pending_orders = get_pending_orders()

            if not pending_orders:
                logger.info("没有待处理的订单，等待5分钟后重试")
                tm.sleep(300)  # 等待5分钟
                continue

            # 按签证类型分组
            orders_by_visa = {}
            for order in pending_orders:
                visa_info = order.get('visa_info', {})
                mission_code = visa_info.get('mission_code')
                center_code = visa_info.get('center_code')
                visa_code = visa_info.get('visa_code')

                if mission_code and center_code and visa_code:
                    key = f"{mission_code}--{center_code}--{visa_code}"
                    if key not in orders_by_visa:
                        orders_by_visa[key] = []
                    orders_by_visa[key].append(order)

            logger.info(f"找到 {len(orders_by_visa)} 种签证类型需要处理")

            # 处理每种签证类型
            processed_any = False
            for visa_key, orders in orders_by_visa.items():
                mission_code, center_code, visa_code = visa_key.split('--')
                logger.info(f"处理签证类型: {mission_code} {center_code} {visa_code}, 订单数: {len(orders)}")

                result = refresh_urn_for_mission(mission_code, center_code, visa_code)
                if result:
                    processed_any = True

                # 避免过快处理
                tm.sleep(2)

            if not processed_any:
                logger.info("本轮没有成功处理任何订单，等待5分钟后重试")
                tm.sleep(300)  # 等待5分钟
            else:
                logger.info("本轮处理完成，等待30秒后开始下一轮")
                tm.sleep(30)  # 等待30秒后开始下一轮

        except Exception as e:
            logger.error(f"持续刷新URN异常: {e}")
            tm.sleep(60)  # 出错时等待1分钟


# 启动工作线程
for i in range(20):
    thread = threading.Thread(target=process_users, daemon=True)
    thread.start()

# 启动配置刷新线程
threading.Thread(target=config_refresh_thread, daemon=True).start()

# 初始化配置
refresh_global_config()


def show_statistics():
    """显示统计信息"""
    try:
        # 统计订单信息
        all_orders = redis_client.hgetall('vfs_user')
        total_orders = len(all_orders)
        pending_orders = 0
        orders_with_urn = 0
        orders_with_otp_validated = 0
        orders_with_otp_failed = 0
        orders_by_country = {}

        for order_data in all_orders:
            try:
                if isinstance(order_data, str):
                    order_data = json.loads(order_data)

                if order_data.get('status') == 'pending':
                    pending_orders += 1

                if order_data.get('urn'):
                    orders_with_urn += 1

                # 统计OTP验证状态
                if order_data.get('otp_validated') == True:
                    orders_with_otp_validated += 1
                elif order_data.get('otp_validated') == False:
                    orders_with_otp_failed += 1

                mission_code = order_data.get('visa_info', {}).get('mission_code')
                if mission_code:
                    orders_by_country[mission_code] = orders_by_country.get(mission_code, 0) + 1

            except (json.JSONDecodeError, TypeError):
                continue

        # 统计账号信息
        all_accounts = redis_client.hgetall("vfs_accounts")
        total_accounts = len(all_accounts)
        active_accounts = 0
        accounts_by_country = {}
        current_time = int(tm.time())

        for account_data in all_accounts:
            try:
                if isinstance(account_data, str):
                    account_data = json.loads(account_data)

                mission_code = account_data.get('missionCode')
                if mission_code:
                    accounts_by_country[mission_code] = accounts_by_country.get(mission_code, 0) + 1

                if (account_data.get('token') and
                    account_data.get('updateTokenTime') and
                        current_time - account_data.get('updateTokenTime') < 6000):
                    active_accounts += 1

            except (json.JSONDecodeError, TypeError):
                continue

        logger.info("=" * 60)
        logger.info("VFS URN Manager 统计信息")
        logger.info("=" * 60)
        logger.info(f"订单统计:")
        logger.info(f"  总订单数: {total_orders}")
        logger.info(f"  待处理订单: {pending_orders}")
        logger.info(f"  已有URN订单: {orders_with_urn}")
        logger.info(f"  OTP验证成功: {orders_with_otp_validated}")
        logger.info(f"  OTP验证失败: {orders_with_otp_failed}")
        logger.info(f"  正在处理用户: {len(processing_users)}")
        logger.info(f"  队列中用户: {user_queue.qsize()}")
        logger.info(f"")
        logger.info(f"账号统计:")
        logger.info(f"  总账号数: {total_accounts}")
        logger.info(f"  活跃账号数: {active_accounts}")
        logger.info(f"")
        logger.info(f"按国家分布:")
        for country in set(list(orders_by_country.keys()) + list(accounts_by_country.keys())):
            order_count = orders_by_country.get(country, 0)
            account_count = accounts_by_country.get(country, 0)
            country_name = all_missionCode_map.get(country, country)
            logger.info(f"  {country_name} ({country}): 订单 {order_count}, 账号 {account_count}")
        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"显示统计信息失败: {e}")


def manual_refresh_all():
    """手动刷新所有待处理订单"""
    try:
        logger.info("开始手动刷新所有待处理订单...")

        pending_orders = get_pending_orders()
        if not pending_orders:
            logger.info("没有待处理的订单")
            return

        # 按签证类型分组
        orders_by_visa = {}
        for order in pending_orders:
            visa_info = order.get('visa_info', {})
            mission_code = visa_info.get('mission_code')
            center_code = visa_info.get('center_code')
            visa_code = visa_info.get('visa_code')

            if mission_code and center_code and visa_code:
                key = f"{mission_code}--{center_code}--{visa_code}"
                if key not in orders_by_visa:
                    orders_by_visa[key] = []
                orders_by_visa[key].append(order)

        logger.info(f"找到 {len(orders_by_visa)} 种签证类型需要处理")

        # 逐个处理每种签证类型
        for visa_key, orders in orders_by_visa.items():
            mission_code, center_code, visa_code = visa_key.split('--')
            logger.info(f"处理签证类型: {mission_code} {center_code} {visa_code}, 订单数: {len(orders)}")
            refresh_urn_for_mission(mission_code, center_code, visa_code)
            tm.sleep(1)  # 避免过快处理

        logger.info("手动刷新完成")

    except Exception as e:
        logger.error(f"手动刷新失败: {e}")


def main():
    """主函数"""
    try:
        logger.info("VFS URN Manager 启动中...")

        # 启动工作线程
        logger.info("启动工作线程...")
        for i in range(20):
            thread = threading.Thread(target=process_users, daemon=True, name=f"Worker-{i+1}")
            thread.start()

        # 启动配置刷新线程
        logger.info("启动配置刷新线程...")
        threading.Thread(target=config_refresh_thread, daemon=True, name="ConfigRefresh").start()

        # 初始化配置
        logger.info("初始化配置...")
        refresh_global_config()

        # 显示初始统计信息
        show_statistics()

        # 启动持续刷新线程
        logger.info("启动持续URN刷新线程...")
        refresh_thread = threading.Thread(target=continuous_urn_refresh, daemon=True, name="URNRefresh")
        refresh_thread.start()

        logger.info("VFS URN Manager 已启动，开始持续刷新URN...")

        # 主循环 - 定期显示统计信息
        while True:
            try:
                tm.sleep(30)  # 每30s显示一次统计信息
                show_statistics()
            except KeyboardInterrupt:
                logger.info("收到退出信号，正在关闭...")
                break
            except Exception as e:
                logger.error(f"主循环异常: {e}")
                tm.sleep(10)

    except Exception as e:
        logger.error(f"主函数异常: {e}")
    finally:
        logger.info("VFS URN Manager 已停止")


if __name__ == "__main__":
    main()
