<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西班牙签证默认Tourism功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #303133;
        }
        .input-section {
            margin: 15px 0;
        }
        .input-label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }
        .input-field {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .result.success {
            background-color: #f0f9ff;
            color: #67c23a;
            border: 1px solid #67c23a;
        }
        .result.info {
            background-color: #f4f4f5;
            color: #909399;
            border: 1px solid #d3d4d6;
        }
        .result.warning {
            background-color: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #e6a23c;
        }
        .test-button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #337ecc;
        }
        .example-data {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>西班牙签证默认Tourism功能测试</h1>
        <p>此页面用于测试西班牙签证类型默认选择Tourism的功能</p>
        
        <div class="test-section">
            <div class="test-title">功能说明</div>
            <ul>
                <li>当用户选择西班牙签证时，如果没有选择Tourism，系统会自动选择Tourism</li>
                <li>如果已经选择了其他类型（如Business），会替换为Tourism</li>
                <li>如果只选择了3级（国家、领馆、签证类型），会自动添加Tourism作为第4级</li>
                <li>非西班牙签证不受影响</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">测试输入</div>
            <div class="input-section">
                <label class="input-label">输入签证选择数据（JSON格式）:</label>
                <textarea id="input-data" class="input-field" rows="6" placeholder='例如: [["spain", "SHANGHAI", "schengen", "business"]]'></textarea>
            </div>
            <button class="test-button" onclick="testTourismDefault()">测试Tourism默认选择</button>
            <button class="test-button" onclick="loadExample1()">示例1: 西班牙+Business</button>
            <button class="test-button" onclick="loadExample2()">示例2: 西班牙+3级选择</button>
            <button class="test-button" onclick="loadExample3()">示例3: 意大利签证</button>
            <button class="test-button" onclick="loadExample4()">示例4: 混合签证</button>
        </div>

        <div class="test-section">
            <div class="test-title">处理结果</div>
            <div id="result-container"></div>
        </div>

        <div class="test-section">
            <div class="test-title">示例数据格式</div>
            <div class="example-data">
                <strong>输入格式:</strong><br>
                [["spain", "SHANGHAI", "schengen", "business"]] - 西班牙商务签证<br>
                [["spain", "BEIJING", "schengen"]] - 西班牙申根签证（3级）<br>
                [["italy", "SHANGHAI", "schengen", "business"]] - 意大利商务签证<br><br>
                
                <strong>预期输出（西班牙）:</strong><br>
                [["spain", "SHANGHAI", "schengen", "Tourism"]] - 自动替换为Tourism<br>
                [["spain", "BEIJING", "schengen", "Tourism"]] - 自动添加Tourism
            </div>
        </div>
    </div>

    <script>
        // 模拟为西班牙签证自动选择Tourism的函数
        function autoSelectSpainTourism(value) {
            if (!Array.isArray(value) || value.length === 0) {
                return value;
            }

            const processedValue = value.map(item => {
                if (!Array.isArray(item) || item.length < 2) {
                    return {
                        original: item,
                        processed: item,
                        message: '数据格式不正确'
                    };
                }

                // 检查是否是西班牙签证
                const countryCode = item[0];
                
                if (countryCode && countryCode.toLowerCase().includes('spain')) {
                    console.log('检测到西班牙签证选择:', item);
                    
                    // 如果选择了西班牙但没有选择到Tourism，自动补全Tourism
                    if (item.length >= 3 && !item.includes('Tourism')) {
                        // 检查是否已经有完整的4级选择
                        if (item.length === 4) {
                            // 如果第4级不是Tourism，替换为Tourism
                            const autoCompleted = [item[0], item[1], item[2], 'Tourism'];
                            console.log('西班牙签证自动选择Tourism:', autoCompleted);
                            return {
                                original: item,
                                processed: autoCompleted,
                                message: '已自动选择旅游签证类型（替换原有类型）'
                            };
                        } else if (item.length === 3) {
                            // 如果只有3级，添加Tourism作为第4级
                            const autoCompleted = [...item, 'Tourism'];
                            console.log('西班牙签证自动添加Tourism:', autoCompleted);
                            return {
                                original: item,
                                processed: autoCompleted,
                                message: '已自动选择旅游签证类型（添加第4级）'
                            };
                        }
                    } else if (item.includes('Tourism')) {
                        return {
                            original: item,
                            processed: item,
                            message: '已经选择了Tourism，无需修改'
                        };
                    }
                }
                
                return {
                    original: item,
                    processed: item,
                    message: '非西班牙签证，无需修改'
                };
            });

            return processedValue;
        }

        // 测试Tourism默认选择功能
        function testTourismDefault() {
            const inputData = document.getElementById('input-data').value.trim();
            const resultContainer = document.getElementById('result-container');
            
            if (!inputData) {
                resultContainer.innerHTML = '<div class="result info">请输入测试数据</div>';
                return;
            }

            try {
                const parsedData = JSON.parse(inputData);
                const result = autoSelectSpainTourism(parsedData);
                
                let resultHtml = '<div class="result success">处理成功!</div>';
                
                result.forEach((item, index) => {
                    const isModified = JSON.stringify(item.original) !== JSON.stringify(item.processed);
                    const resultClass = isModified ? 'warning' : 'info';
                    
                    resultHtml += `
                        <div class="result ${resultClass}">
                            <strong>项目 ${index + 1}:</strong><br>
                            原始输入: ${JSON.stringify(item.original)}<br>
                            处理结果: ${JSON.stringify(item.processed)}<br>
                            状态: ${item.message}
                            ${isModified ? '<br><strong>✓ 已修改</strong>' : '<br><strong>- 未修改</strong>'}
                        </div>
                    `;
                });
                
                resultContainer.innerHTML = resultHtml;
                
            } catch (error) {
                resultContainer.innerHTML = `<div class="result" style="background-color: #fef0f0; color: #f56c6c; border: 1px solid #f56c6c;">解析错误: ${error.message}</div>`;
            }
        }

        // 加载示例数据
        function loadExample1() {
            document.getElementById('input-data').value = '[["spain", "SHANGHAI", "schengen", "business"]]';
        }

        function loadExample2() {
            document.getElementById('input-data').value = '[["spain", "BEIJING", "schengen"]]';
        }

        function loadExample3() {
            document.getElementById('input-data').value = '[["italy", "SHANGHAI", "schengen", "business"]]';
        }

        function loadExample4() {
            document.getElementById('input-data').value = '[["spain", "SHANGHAI", "schengen", "business"], ["italy", "BEIJING", "schengen", "tourism"], ["spain", "GUANGZHOU", "schengen"]]';
        }

        // 页面加载时显示默认示例
        window.onload = function() {
            loadExample1();
            testTourismDefault();
        };
    </script>
</body>
</html>
