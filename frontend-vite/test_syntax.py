#!/usr/bin/env python3
# 简单的语法检查脚本

import ast
import sys

def check_syntax(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 编译检查语法
        ast.parse(source)
        print(f"✓ {file_path} 语法正确")
        return True
    except SyntaxError as e:
        print(f"✗ {file_path} 语法错误:")
        print(f"  行 {e.lineno}: {e.text}")
        print(f"  错误: {e.msg}")
        return False
    except Exception as e:
        print(f"✗ {file_path} 检查失败: {e}")
        return False

if __name__ == "__main__":
    if check_syntax("backend/backend.py"):
        print("语法检查通过！")
        sys.exit(0)
    else:
        print("语法检查失败！")
        sys.exit(1)