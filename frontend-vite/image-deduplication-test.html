<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片去重功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .upload-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .upload-section.success {
            border-color: #67c23a;
            background-color: #f0f9ff;
        }
        .upload-section.error {
            border-color: #f56c6c;
            background-color: #fef0f0;
        }
        .file-input {
            margin: 10px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .result.success {
            background-color: #f0f9ff;
            color: #67c23a;
            border: 1px solid #67c23a;
        }
        .result.error {
            background-color: #fef0f0;
            color: #f56c6c;
            border: 1px solid #f56c6c;
        }
        .hash-display {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #303133;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>图片去重功能测试</h1>
        <p>此页面用于测试护照照片和头像照片的去重校验功能</p>
        
        <div class="test-section">
            <div class="test-title">测试场景 1: 护照照片上传</div>
            <div class="upload-section" id="passport-section">
                <h3>护照照片</h3>
                <input type="file" id="passport-input" class="file-input" accept=".jpg,.jpeg,.png">
                <div id="passport-result" class="result" style="display: none;"></div>
                <div id="passport-hash" class="hash-display" style="display: none;"></div>
                <img id="passport-preview" class="image-preview" style="display: none;">
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试场景 2: 头像照片上传</div>
            <div class="upload-section" id="avatar-section">
                <h3>头像照片</h3>
                <input type="file" id="avatar-input" class="file-input" accept=".jpg,.jpeg,.png">
                <div id="avatar-result" class="result" style="display: none;"></div>
                <div id="avatar-hash" class="hash-display" style="display: none;"></div>
                <img id="avatar-preview" class="image-preview" style="display: none;">
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">去重检查结果</div>
            <div id="deduplication-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">测试说明</div>
            <ul>
                <li>上传两张不同的图片 - 应该显示成功</li>
                <li>上传两张相同的图片 - 应该显示重复警告</li>
                <li>哈希值相同表示图片内容相同</li>
                <li>支持 JPG、JPEG、PNG 格式</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟客户数据结构
        const mockClient = {
            passport_image: '',
            passport_image_hash: '',
            avatar_image: '',
            avatar_image_hash: ''
        };

        // 计算文件哈希值
        const calculateFileHash = async (file) => {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = async (event) => {
                    try {
                        const arrayBuffer = event.target.result;
                        const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
                        const hashArray = Array.from(new Uint8Array(hashBuffer));
                        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                        resolve(hashHex);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsArrayBuffer(file);
            });
        };

        // 检查图片是否重复
        const checkImageDuplication = async (file, uploadType) => {
            try {
                const fileHash = await calculateFileHash(file);
                
                if (uploadType === 'passport' && mockClient.avatar_image_hash) {
                    if (mockClient.avatar_image_hash === fileHash) {
                        return {
                            isDuplicate: true,
                            message: '护照照片不能与头像照片相同，请选择不同的图片',
                            hash: fileHash
                        };
                    }
                } else if (uploadType === 'avatar' && mockClient.passport_image_hash) {
                    if (mockClient.passport_image_hash === fileHash) {
                        return {
                            isDuplicate: true,
                            message: '头像照片不能与护照照片相同，请选择不同的图片',
                            hash: fileHash
                        };
                    }
                }
                
                return {
                    isDuplicate: false,
                    hash: fileHash
                };
            } catch (error) {
                console.error('图片去重检查失败:', error);
                return {
                    isDuplicate: false,
                    hash: null,
                    error: error.message
                };
            }
        };

        // 处理护照照片上传
        document.getElementById('passport-input').addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (!file) return;

            const section = document.getElementById('passport-section');
            const result = document.getElementById('passport-result');
            const hashDisplay = document.getElementById('passport-hash');
            const preview = document.getElementById('passport-preview');

            try {
                const duplicationCheck = await checkImageDuplication(file, 'passport');
                
                if (duplicationCheck.isDuplicate) {
                    section.className = 'upload-section error';
                    result.className = 'result error';
                    result.textContent = duplicationCheck.message;
                    result.style.display = 'block';
                } else {
                    section.className = 'upload-section success';
                    result.className = 'result success';
                    result.textContent = '护照照片上传成功';
                    result.style.display = 'block';
                    
                    // 保存哈希值
                    mockClient.passport_image = file.name;
                    mockClient.passport_image_hash = duplicationCheck.hash;
                }

                // 显示哈希值
                if (duplicationCheck.hash) {
                    hashDisplay.textContent = `文件哈希: ${duplicationCheck.hash}`;
                    hashDisplay.style.display = 'block';
                }

                // 显示预览
                const reader = new FileReader();
                reader.onload = (e) => {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);

                updateDeduplicationResult();
            } catch (error) {
                section.className = 'upload-section error';
                result.className = 'result error';
                result.textContent = `处理失败: ${error.message}`;
                result.style.display = 'block';
            }
        });

        // 处理头像照片上传
        document.getElementById('avatar-input').addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (!file) return;

            const section = document.getElementById('avatar-section');
            const result = document.getElementById('avatar-result');
            const hashDisplay = document.getElementById('avatar-hash');
            const preview = document.getElementById('avatar-preview');

            try {
                const duplicationCheck = await checkImageDuplication(file, 'avatar');
                
                if (duplicationCheck.isDuplicate) {
                    section.className = 'upload-section error';
                    result.className = 'result error';
                    result.textContent = duplicationCheck.message;
                    result.style.display = 'block';
                } else {
                    section.className = 'upload-section success';
                    result.className = 'result success';
                    result.textContent = '头像照片上传成功';
                    result.style.display = 'block';
                    
                    // 保存哈希值
                    mockClient.avatar_image = file.name;
                    mockClient.avatar_image_hash = duplicationCheck.hash;
                }

                // 显示哈希值
                if (duplicationCheck.hash) {
                    hashDisplay.textContent = `文件哈希: ${duplicationCheck.hash}`;
                    hashDisplay.style.display = 'block';
                }

                // 显示预览
                const reader = new FileReader();
                reader.onload = (e) => {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);

                updateDeduplicationResult();
            } catch (error) {
                section.className = 'upload-section error';
                result.className = 'result error';
                result.textContent = `处理失败: ${error.message}`;
                result.style.display = 'block';
            }
        });

        // 更新去重检查结果
        function updateDeduplicationResult() {
            const resultDiv = document.getElementById('deduplication-result');
            
            if (mockClient.passport_image_hash && mockClient.avatar_image_hash) {
                const isDuplicate = mockClient.passport_image_hash === mockClient.avatar_image_hash;
                
                if (isDuplicate) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '⚠️ 检测到重复图片！护照照片和头像照片是同一张图片。';
                } else {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 图片去重检查通过，护照照片和头像照片是不同的图片。';
                }
                resultDiv.style.display = 'block';
            } else {
                resultDiv.style.display = 'none';
            }
        }
    </script>
</body>
</html>
