<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西班牙签证自动补全功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #303133;
        }
        .input-section {
            margin: 15px 0;
        }
        .input-label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }
        .input-field {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .result.success {
            background-color: #f0f9ff;
            color: #67c23a;
            border: 1px solid #67c23a;
        }
        .result.info {
            background-color: #f4f4f5;
            color: #909399;
            border: 1px solid #d3d4d6;
        }
        .test-button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #337ecc;
        }
        .example-data {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>西班牙签证自动补全功能测试</h1>
        <p>此页面用于测试当选择西班牙和领馆时，自动勾选申根签证和旅游类型的功能</p>
        
        <div class="test-section">
            <div class="test-title">功能说明</div>
            <ul>
                <li>当用户选择西班牙国家和任意领馆时，自动补全"申根签证"和"旅游类型"</li>
                <li>如果只选择了国家和领馆（2级），自动补全为4级完整选择</li>
                <li>如果选择了国家、领馆和申根签证（3级），自动补全旅游类型</li>
                <li>非西班牙签证不受影响</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">测试输入</div>
            <div class="input-section">
                <label class="input-label">输入签证选择数据（JSON格式）:</label>
                <textarea id="input-data" class="input-field" rows="6" placeholder='例如: [["spain", "SHANGHAI"]]'></textarea>
            </div>
            <button class="test-button" onclick="testAutoComplete()">测试自动补全</button>
            <button class="test-button" onclick="loadExample1()">示例1: 西班牙+上海</button>
            <button class="test-button" onclick="loadExample2()">示例2: 西班牙+北京+申根</button>
            <button class="test-button" onclick="loadExample3()">示例3: 意大利签证</button>
            <button class="test-button" onclick="loadExample4()">示例4: 多个签证</button>
        </div>

        <div class="test-section">
            <div class="test-title">处理结果</div>
            <div id="result-container"></div>
        </div>

        <div class="test-section">
            <div class="test-title">示例数据格式</div>
            <div class="example-data">
                <strong>输入格式:</strong><br>
                [["spain", "SHANGHAI"]] - 选择西班牙上海领馆<br>
                [["spain", "BEIJING", "schengen"]] - 选择西班牙北京领馆申根签证<br>
                [["italy", "SHANGHAI", "schengen", "tourism"]] - 完整的意大利签证选择<br><br>

                <strong>预期输出（西班牙）:</strong><br>
                [["spain", "SHANGHAI", "schengen", "tourism"]] - 自动补全申根签证和旅游类型
            </div>
        </div>
    </div>

    <script>
        // 模拟自动补全西班牙签证的函数
        function autoCompleteSpainVisa(value) {
            if (!Array.isArray(value) || value.length === 0) {
                return value;
            }

            const processedValue = value.map(item => {
                if (!Array.isArray(item) || item.length < 2) {
                    return item;
                }

                // 检查是否是西班牙签证
                const countryCode = item[0];
                const centerCode = item[1];
                
                if (countryCode && countryCode.toLowerCase().includes('spain')) {
                    console.log('检测到西班牙签证选择:', item);
                    
                    // 如果只选择了国家和领馆，自动补全申根签证和旅游类型
                    if (item.length === 2) {
                        const autoCompleted = [countryCode, centerCode, 'schengen', 'tourism'];
                        console.log('自动补全西班牙签证:', autoCompleted);
                        return {
                            original: item,
                            completed: autoCompleted,
                            message: '已自动选择申根签证和旅游类型'
                        };
                    }
                    // 如果只选择了国家、领馆和签证类型，自动补全旅游类型
                    else if (item.length === 3) {
                        const visaCategory = item[2];
                        if (visaCategory && visaCategory.toLowerCase().includes('schengen')) {
                            const autoCompleted = [countryCode, centerCode, visaCategory, 'tourism'];
                            console.log('自动补全旅游类型:', autoCompleted);
                            return {
                                original: item,
                                completed: autoCompleted,
                                message: '已自动选择旅游类型'
                            };
                        }
                    }
                }
                
                return {
                    original: item,
                    completed: item,
                    message: '无需自动补全'
                };
            });

            return processedValue;
        }

        // 测试自动补全功能
        function testAutoComplete() {
            const inputData = document.getElementById('input-data').value.trim();
            const resultContainer = document.getElementById('result-container');
            
            if (!inputData) {
                resultContainer.innerHTML = '<div class="result info">请输入测试数据</div>';
                return;
            }

            try {
                const parsedData = JSON.parse(inputData);
                const result = autoCompleteSpainVisa(parsedData);
                
                let resultHtml = '<div class="result success">处理成功!</div>';
                
                result.forEach((item, index) => {
                    resultHtml += `
                        <div class="result info">
                            <strong>项目 ${index + 1}:</strong><br>
                            原始输入: ${JSON.stringify(item.original)}<br>
                            处理结果: ${JSON.stringify(item.completed)}<br>
                            状态: ${item.message}
                        </div>
                    `;
                });
                
                resultContainer.innerHTML = resultHtml;
                
            } catch (error) {
                resultContainer.innerHTML = `<div class="result" style="background-color: #fef0f0; color: #f56c6c; border: 1px solid #f56c6c;">解析错误: ${error.message}</div>`;
            }
        }

        // 加载示例数据
        function loadExample1() {
            document.getElementById('input-data').value = '[["spain", "SHANGHAI"]]';
        }

        function loadExample2() {
            document.getElementById('input-data').value = '[["spain", "BEIJING", "schengen"]]';
        }

        function loadExample3() {
            document.getElementById('input-data').value = '[["italy", "SHANGHAI", "schengen", "tourism"]]';
        }

        function loadExample4() {
            document.getElementById('input-data').value = '[["spain", "SHANGHAI"], ["italy", "BEIJING", "schengen", "tourism"], ["france", "GUANGZHOU"]]';
        }

        // 页面加载时显示默认示例
        window.onload = function() {
            loadExample1();
            testAutoComplete();
        };
    </script>
</body>
</html>
