# Excel 导出功能增强：添加来源列

## 🔍 需求描述

在 BookedOrders.vue 的 exportToExcel 函数中：

1. 新增一个"来源"列
2. 字段对应客户的 `customer` 字段
3. 相同来源的客户放到一起并合并来源显示
4. 没有来源的客户放到最后
5. 来源列放到单价前面

## 🔧 实现方案

### 1. 数据分组逻辑

#### **按来源分组**

```javascript
// 按来源分组订单数据
const groupedBySource = {};

filteredOrders.value.forEach(order => {
  // 获取客户来源
  const customerSource = order.customer || '无来源';

  if (!groupedBySource[customerSource]) {
    groupedBySource[customerSource] = [];
  }
  groupedBySource[customerSource].push(order);
});
```

#### **排序规则**

1. **有来源的数据**：按来源名称字母顺序排序
2. **无来源的数据**：放在最后

```javascript
// 先处理有来源的数据（按来源名称排序）
const sourcesWithData = Object.keys(groupedBySource)
  .filter(source => source !== '无来源')
  .sort();

// 最后处理无来源的数据
if (groupedBySource['无来源']) {
  // 处理无来源数据
}
```

### 2. 来源合并显示

#### **合并规则**

- 同一来源的第一条记录显示来源名称
- 同一来源的其他记录来源列为空（实现合并效果）

```javascript
excelData.push({
  完成时间: order.updated_at || '未知',
  抢号日期: order.appointment_date || order.updated_at?.split(' ')[0] || '未知',
  姓名: customerNames,
  领区: visaInfo,
  对接: order.operator_name || '未知',
  来源: index === 0 ? source : '', // 只在第一行显示来源，其他行合并
  单价: order.price || '未知',
  人数: personCount,
});
```

### 3. 列结构调整

#### **修改前的列结构**

| 列序号 | 列名     | 宽度 |
| ------ | -------- | ---- |
| 1      | 完成时间 | 18   |
| 2      | 抢号日期 | 12   |
| 3      | 姓名     | 20   |
| 4      | 领区     | 25   |
| 5      | 对接     | 12   |
| 6      | 单价     | 10   |
| 7      | 人数     | 8    |

#### **修改后的列结构**

| 列序号 | 列名     | 宽度   |
| ------ | -------- | ------ |
| 1      | 完成时间 | 18     |
| 2      | 抢号日期 | 12     |
| 3      | 姓名     | 20     |
| 4      | 领区     | 25     |
| 5      | 对接     | 12     |
| 6      | **来源** | **15** |
| 7      | 单价     | 10     |
| 8      | 人数     | 8      |

## 📊 数据处理流程

### 流程图

```
filteredOrders
    ↓
按 customer 字段分组
    ↓
有来源的组（按名称排序）
    ↓
无来源的组
    ↓
生成 Excel 数据
    ↓
合并相同来源的显示
    ↓
导出 Excel 文件
```

### 详细步骤

#### **步骤 1：数据分组**

```javascript
// 遍历所有订单
filteredOrders.value.forEach(order => {
  // 提取客户来源
  const customerSource = order.customer || '无来源';

  // 按来源分组
  groupedBySource[customerSource].push(order);
});
```

#### **步骤 2：排序处理**

```javascript
// 有来源的数据按名称排序
const sourcesWithData = Object.keys(groupedBySource)
  .filter(source => source !== '无来源')
  .sort();

// 无来源的数据放最后
```

#### **步骤 3：生成 Excel 数据**

```javascript
// 处理有来源的数据
sourcesWithData.forEach(source => {
  ordersInSource.forEach((order, index) => {
    excelData.push({
      // ... 其他字段
      来源: index === 0 ? source : '', // 合并显示
      // ... 其他字段
    });
  });
});

// 处理无来源的数据
```

## 🎯 实现效果

### 示例数据展示

#### **原始数据**

```javascript
[
  { customer: '渠道A', clients: [{ name: '张三' }], price: 100 },
  { customer: '渠道A', clients: [{ name: '李四' }], price: 150 },
  { customer: '渠道B', clients: [{ name: '王五' }], price: 200 },
  { customer: '', clients: [{ name: '赵六' }], price: 120 },
  { clients: [{ name: '钱七' }], price: 180 },
];
```

#### **导出后的 Excel 效果**

| 完成时间   | 抢号日期   | 姓名 | 领区        | 对接     | 来源   | 单价 | 人数 |
| ---------- | ---------- | ---- | ----------- | -------- | ------ | ---- | ---- |
| 2025-01-15 | 2025-01-20 | 张三 | 德国-北京   | 操作员 A | 渠道 A | 100  | 1    |
| 2025-01-16 | 2025-01-21 | 李四 | 法国-上海   | 操作员 B |        | 150  | 1    |
| 2025-01-17 | 2025-01-22 | 王五 | 意大利-广州 | 操作员 C | 渠道 B | 200  | 1    |
| 2025-01-18 | 2025-01-23 | 赵六 | 西班牙-北京 | 操作员 D | 无来源 | 120  | 1    |
| 2025-01-19 | 2025-01-24 | 钱七 | 荷兰-上海   | 操作员 E |        | 180  | 1    |

### 关键特性

#### **1. 分组排序**

- ✅ 渠道 A 的订单聚集在一起
- ✅ 渠道 B 的订单聚集在一起
- ✅ 无来源的订单放在最后

#### **2. 合并显示**

- ✅ 每个来源只在第一行显示名称
- ✅ 同一来源的其他行来源列为空
- ✅ 视觉上形成合并效果

#### **3. 列位置**

- ✅ 来源列位于对接列和单价列之间
- ✅ 列宽设置为 15，适合显示来源名称

## 🔍 数据来源说明

### customer 字段获取

```javascript
// 直接从订单中获取客户来源
const customerSource = order.customer || '无来源';
```

### 处理逻辑

1. **有 customer 字段**：使用 `order.customer`
2. **customer 为空或 undefined**：显示为"无来源"

## ✅ 功能验证

### 测试场景

#### **场景 1：多种来源混合**

- 渠道 A：3 个订单
- 渠道 B：2 个订单
- 无来源：1 个订单

**预期结果**：

- 渠道 A 的 3 个订单聚集显示，只有第一个显示"渠道 A"
- 渠道 B 的 2 个订单聚集显示，只有第一个显示"渠道 B"
- 无来源的 1 个订单在最后，显示"无来源"

#### **场景 2：全部无来源**

- 所有订单都没有来源

**预期结果**：

- 所有订单显示在一起
- 只有第一个订单显示"无来源"，其他为空

#### **场景 3：单一来源**

- 所有订单都来自同一渠道

**预期结果**：

- 所有订单聚集显示
- 只有第一个订单显示来源名称

## 🎉 增强效果

### 用户体验提升

- ✅ **数据分组**：相同来源的订单聚集显示，便于分析
- ✅ **视觉合并**：来源列合并显示，界面更清晰
- ✅ **排序优化**：有来源的数据优先显示，无来源的放最后
- ✅ **列位置**：来源列位置合理，便于查看

### 数据分析价值

- ✅ **来源统计**：可以清楚看到各渠道的订单数量
- ✅ **业绩分析**：便于按来源分析业绩表现
- ✅ **渠道管理**：有助于渠道效果评估

现在 Excel 导出功能支持按来源分组和合并显示，大大提升了数据的可读性和分析价值！
