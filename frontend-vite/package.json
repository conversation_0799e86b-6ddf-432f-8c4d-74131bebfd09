{"name": "frontend-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "element-plus": "^2.10.1", "html2canvas": "^1.4.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.5"}}