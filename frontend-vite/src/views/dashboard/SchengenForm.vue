<template>
  <div class="schengen-form-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Document /></el-icon>
            申根表单创建
          </h1>
          <p class="page-description">创建和管理申根签证申请表单</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="createNewForm" :icon="Plus">
            新建表单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-section">
      <div class="filter-content">
        <div class="filter-left">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索表单名称或申请人姓名"
            :prefix-icon="Search"
            clearable
            style="width: 300px"
            @input="handleSearch"
          />
          <el-select
            v-model="filterStatus"
            placeholder="表单状态"
            clearable
            style="width: 150px; margin-left: 12px"
          >
            <el-option label="全部" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </div>
        <div class="filter-right">
          <el-button :icon="Refresh" @click="refreshData">刷新</el-button>
        </div>
      </div>
    </div>

    <!-- 表单列表 -->
    <div class="form-list-section">
      <el-table
        :data="filteredForms"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column label="申请ID" width="220">
          <template #default="{ row }">
            <div class="id-cell">
              <span class="id-text">{{ row.id }}</span>
              <el-button
                type="text"
                size="small"
                @click="copyToClipboard(row.id)"
                class="copy-btn"
                title="复制申请ID"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="applicant_name"
          label="申请人姓名"
          min-width="150"
        />
        <el-table-column prop="passport_number" label="护照号码" width="120" />
        <el-table-column prop="nationality" label="国籍" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-dropdown trigger="click" placement="bottom-end">
              <el-button type="primary" size="small" @click.stop>
                操作
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="editForm(row)">
                    <el-icon><Edit /></el-icon>
                    编辑表单
                  </el-dropdown-item>
                  <el-dropdown-item @click="downloadForm(row)">
                    <el-icon><Download /></el-icon>
                    下载表单
                  </el-dropdown-item>
                  <el-dropdown-item @click="downloadVafForm(row)">
                    <el-icon><DocumentCopy /></el-icon>
                    下载VAF表单
                  </el-dropdown-item>
                  <el-dropdown-item 
                    @click="deleteForm(row)"
                    divided
                  >
                    <el-icon><Delete /></el-icon>
                    删除表单
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 表单创建/编辑对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="isEditing ? '编辑申根表单' : '创建申根表单'"
      width="90%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      class="schengen-dialog"
    >
      <SchengenFormEditor
        v-if="formDialogVisible"
        :form-data="currentFormData"
        :is-editing="isEditing"
        @save="handleFormSave"
        @cancel="handleFormCancel"
      />
    </el-dialog>

    <!-- VAF表单下载进度弹窗 -->
    <el-dialog
      v-model="downloadDialogVisible"
      title="下载VAF表单"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="download-progress-container">
        <div class="progress-content">
          <!-- 加载图标 -->
          <div class="loading-icon">
            <el-icon 
              v-if="downloadProgress.status !== 'completed'" 
              class="is-loading"
              :size="40"
            >
              <Loading />
            </el-icon>
            <el-icon 
              v-else 
              class="success-icon"
              :size="40"
            >
              <CircleCheck />
            </el-icon>
          </div>

          <!-- 状态信息 -->
          <div class="status-info">
            <div class="status-message">{{ downloadProgress.message }}</div>
            <div v-if="downloadProgress.taskId" class="task-id">
              任务ID: {{ downloadProgress.taskId }}
            </div>
          </div>

          <!-- 进度指示器 -->
          <div class="progress-indicator">
            <el-progress 
              v-if="downloadProgress.status === 'processing'"
              :percentage="100"
              :indeterminate="true"
              :duration="3"
              status="success"
            />
            <el-progress 
              v-else-if="downloadProgress.status === 'completed'"
              :percentage="100"
              status="success"
            />
            <el-progress 
              v-else-if="downloadProgress.status === 'failed'"
              :percentage="100"
              status="exception"
            />
            <el-progress 
              v-else
              :percentage="30"
              :indeterminate="true"
              :duration="5"
            />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="progress-actions">
          <el-button 
            v-if="downloadProgress.status !== 'completed'"
            @click="cancelDownload"
            type="default"
          >
            取消下载
          </el-button>
          <el-button 
            v-else
            @click="downloadDialogVisible = false"
            type="primary"
          >
            完成
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Document,
  Plus,
  Search,
  Refresh,
  Edit,
  Download,
  Delete,
  DocumentCopy,
  ArrowDown,
  Loading,
  CircleCheck,
} from '@element-plus/icons-vue';
import http from '@/utils/http';
import SchengenFormEditor from '@/components/SchengenFormEditor.vue';

// 响应式数据
const loading = ref(false);
const searchKeyword = ref('');
const filterStatus = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const totalCount = ref(0);
const formList = ref([]);
const formDialogVisible = ref(false);
const isEditing = ref(false);
const currentFormData = ref(null);

// 计算属性
const filteredForms = computed(() => {
  let filtered = formList.value;

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      form =>
        form.form_name?.toLowerCase().includes(keyword) ||
        form.applicant_name?.toLowerCase().includes(keyword)
    );
  }

  if (filterStatus.value) {
    filtered = filtered.filter(form => form.status === filterStatus.value);
  }

  return filtered;
});

// 方法

const getStatusType = status => {
  const typeMap = {
    draft: '',
    submitted: 'warning',
    completed: 'success',
  };
  return typeMap[status] || '';
};

const getStatusText = status => {
  const textMap = {
    draft: '草稿',
    submitted: '已提交',
    completed: '已完成',
  };
  return textMap[status] || status;
};

const formatDateTime = dateTime => {
  if (!dateTime) return '';
  return new Date(dateTime).toLocaleString('zh-CN');
};

const createNewForm = () => {
  currentFormData.value = null;
  isEditing.value = false;
  formDialogVisible.value = true;
};

const editForm = async form => {
  try {
    loading.value = true;

    // 调用详情接口获取完整的表单数据
    const response = await http.get(`/api/schengen-forms/${form.id}`);

    if (response.code === 1) {
      currentFormData.value = response.data;
      isEditing.value = true;
      formDialogVisible.value = true;
    } else {
      ElMessage.error(response.message || '获取表单详情失败');
    }
  } catch (error) {
    console.error('获取表单详情失败:', error);
    ElMessage.error('获取表单详情失败，请重试');
  } finally {
    loading.value = false;
  }
};

const downloadForm = async form => {
  try {
    loading.value = true;
    console.log('开始下载表单:', form.id);

    const response = await http.get(`/api/schengen-forms/${form.id}/download`, {
      responseType: 'blob',
    });

    console.log('下载响应状态:', response.status);
    console.log('下载响应头:', response.headers);
    console.log('下载响应数据类型:', typeof response.data);
    console.log('下载响应数据大小:', response.data?.size);

    // 检查响应状态
    if (response.status !== 200) {
      console.error('响应状态异常:', response.status);
      throw new Error(`下载失败，状态码: ${response.status}`);
    }

    // 检查响应数据
    if (!response.data) {
      console.error('响应数据为空');
      throw new Error('下载的文件为空');
    }

    console.log('响应数据检查通过');

    // 从响应头获取文件名，如果没有则使用默认名称
    let filename = `申根签证表单_${form.applicant_name || 'Unknown'}_${
      form.id
    }`;
    let fileExtension = '.html'; // 默认扩展名

    try {
      const contentDisposition = response.headers['content-disposition'];
      console.log('Content-Disposition:', contentDisposition);

      if (contentDisposition) {
        // 尝试多种文件名解析方式
        const filenameMatch =
          contentDisposition.match(/filename\*=UTF-8''([^;]+)/) ||
          contentDisposition.match(/filename="([^"]+)"/) ||
          contentDisposition.match(/filename=([^;]+)/);

        if (filenameMatch) {
          const extractedFilename = decodeURIComponent(filenameMatch[1].trim());
          console.log('提取的文件名:', extractedFilename);
          filename = extractedFilename;
        }
      }

      // 根据Content-Type确定文件扩展名
      const contentType = response.headers['content-type'];
      console.log('Content-Type:', contentType);

      if (contentType) {
        if (contentType.includes('text/html')) {
          fileExtension = '.html';
        } else if (contentType.includes('application/pdf')) {
          fileExtension = '.pdf';
        } else if (contentType.includes('application/json')) {
          fileExtension = '.json';
        }
      }

      // 如果文件名没有扩展名，添加扩展名
      if (!filename.includes('.')) {
        filename += fileExtension;
      }
    } catch (headerError) {
      console.warn('解析响应头失败:', headerError);
      filename += fileExtension;
    }

    console.log('最终文件名:', filename);

    // 创建下载链接
    try {
      const blob = new Blob([response.data], {
        type: response.headers['content-type'] || 'application/octet-stream',
      });
      console.log('创建的Blob:', blob);

      const url = window.URL.createObjectURL(blob);
      console.log('创建的URL:', url);

      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);

      console.log('下载成功');
      ElMessage.success('表单下载成功');
    } catch (downloadError) {
      console.error('创建下载链接失败:', downloadError);
      throw new Error('创建下载链接失败');
    }
  } catch (error) {
    console.error('下载表单失败:', error);

    // 更详细的错误信息
    let errorMessage = '下载表单失败，请重试';
    if (error.response) {
      errorMessage = `下载失败: ${error.response.status} ${error.response.statusText}`;
    } else if (error.message) {
      errorMessage = `下载失败: ${error.message}`;
    }

    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

const deleteForm = async form => {
  try {
    await ElMessageBox.confirm(
      `确定要删除表单"${form.form_name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await http.delete(`/api/schengen-forms/${form.id}`);
    ElMessage.success('删除成功');
    await loadFormList();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败，请重试');
    }
  }
};


// 添加下载弹窗相关的响应式数据
const downloadDialogVisible = ref(false);
const downloadProgress = ref({
  status: 'pending',
  message: '正在准备下载...',
  taskId: null
});

const downloadVafForm = async form => {
  let pollTimeoutId = null;
  let startTime = Date.now();
  let isCancelled = false;
  const maxWaitTime = 8 * 60 * 1000; // 8分钟超时
  
  const cleanup = () => {
    if (pollTimeoutId) {
      clearTimeout(pollTimeoutId);
      pollTimeoutId = null;
    }
  };

  const cancelDownload = () => {
    isCancelled = true;
    cleanup();
    downloadDialogVisible.value = false;
    ElMessage.warning('已取消VAF表单下载');
  };

  try {
    console.log('开始下载VAF表单:', form.id);

    // 初始化进度状态并显示弹窗
    downloadProgress.value = {
      status: 'pending',
      message: '正在启动下载任务...',
      taskId: null
    };
    downloadDialogVisible.value = true;

    // 第一步：启动下载任务
    const startResponse = await http.post('/api/download_vaf_form', {
      form_id: form.id, // 使用申根表单的ID作为form_id
    });

    if (startResponse?.code !== 1) {
      downloadDialogVisible.value = false;
      ElMessage.warning(startResponse?.message || '启动下载任务失败');
      return;
    }

    const taskId = startResponse.data.task_id;
    console.log('下载任务已启动，任务ID:', taskId);
    
    // 更新进度状态
    downloadProgress.value = {
      status: 'pending',
      message: '下载任务已启动，正在队列中等待...',
      taskId: taskId
    };

    // 第二步：轮询任务状态
    const pollStatus = async () => {
      try {
        // 检查是否已取消
        if (isCancelled) {
          return;
        }

        // 检查超时
        const elapsed = Date.now() - startTime;
        if (elapsed > maxWaitTime) {
          throw new Error('下载超时，请稍后重试');
        }

        const statusResponse = await http.get(`/api/download_vaf_form_status/${taskId}`);
        
        if (statusResponse?.code !== 1) {
          throw new Error('查询任务状态失败');
        }

        const taskInfo = statusResponse.data;
        console.log('任务状态:', taskInfo);

        // 更新弹窗状态
        downloadProgress.value = {
          status: taskInfo.status,
          message: taskInfo.message || '正在处理中...',
          taskId: taskId
        };
        
        if (taskInfo.status === 'processing') {
          // 继续轮询
          pollTimeoutId = setTimeout(() => {
            if (!isCancelled) pollStatus();
          }, 2000); // 2秒后再次查询
          
        } else if (taskInfo.status === 'completed') {
          // 下载完成
          downloadProgress.value = {
            status: 'completed',
            message: '下载完成，正在准备文件...',
            taskId: taskId
          };
          
          const pdfData = taskInfo.result;
          if (!pdfData || !pdfData.pdfdata) {
            throw new Error('下载的数据格式错误');
          }

          // 将base64数据转换为blob并下载
          const base64Data = pdfData.pdfdata;
          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: 'application/pdf' });
          
          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `VAF_Form_${form.applicant_name || 'Unknown'}_${pdfData.passport_no || form.id}.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          // 关闭弹窗
          setTimeout(() => {
            downloadDialogVisible.value = false;
            ElMessage.success('VAF表单下载成功');
            
            // 添加字体提示
            setTimeout(() => {
              ElMessage.info({
                message: '如果PDF中中文显示异常，请使用Adobe Reader或其他专业PDF阅读器打开',
                duration: 5000
              });
            }, 1000);
          }, 1000);
          
        } else if (taskInfo.status === 'failed') {
          // 下载失败
          throw new Error(taskInfo.message || '下载失败');
          
        } else if (taskInfo.status === 'pending') {
          // 继续轮询
          pollTimeoutId = setTimeout(() => {
            if (!isCancelled) pollStatus();
          }, 2000);
          
        } else {
          // 未知状态
          throw new Error(`未知的任务状态: ${taskInfo.status}`);
        }
        
      } catch (pollError) {
        console.error('轮询任务状态失败:', pollError);
        cleanup();
        throw pollError;
      }
    };

    // 开始轮询
    pollTimeoutId = setTimeout(() => {
      if (!isCancelled) pollStatus();
    }, 1000); // 1秒后开始查询

  } catch (error) {
    console.error('下载VAF表单失败:', error);
    cleanup();
    downloadDialogVisible.value = false;
    ElMessage.error(error.message || '下载VAF表单失败，请重试');
  }
};

// 复制到剪贴板功能
const copyToClipboard = async text => {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success('申请ID已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    // 降级方案：使用传统方法
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('申请ID已复制到剪贴板');
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError);
      ElMessage.error('复制失败，请手动复制');
    }
  }
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handleSizeChange = newSize => {
  pageSize.value = newSize;
  currentPage.value = 1;
  loadFormList();
};

const handleCurrentChange = newPage => {
  currentPage.value = newPage;
  loadFormList();
};

const handleFormSave = async () => {
  formDialogVisible.value = false;
  await loadFormList();
  ElMessage.success(isEditing.value ? '表单更新成功' : '表单创建成功');
};

const handleFormCancel = () => {
  formDialogVisible.value = false;
};

const refreshData = () => {
  loadFormList();
};

const loadFormList = async () => {
  try {
    loading.value = true;
    const response = await http.get('/api/schengen-forms', {
      params: {
        page: currentPage.value,
        size: pageSize.value,
        search: searchKeyword.value,
        status: filterStatus.value,
      },
    });

    if (response.code === 1) {
      formList.value = response.data.items || [];
      totalCount.value = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '获取表单列表失败');
    }
  } catch (error) {
    console.error('获取表单列表失败:', error);
    ElMessage.error('获取表单列表失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadFormList();
});
</script>

<style scoped>
.schengen-form-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
}

.page-header {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 20px 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #409eff;
}

.page-description {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.filter-section {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 16px 24px;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-left {
  display: flex;
  align-items: center;
}

.form-list-section {
  flex: 1;
  padding: 20px 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.country-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.country-flag {
  width: 24px;
  height: 18px;
  border-radius: 2px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 申请ID列样式 */
.id-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.id-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #333;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.copy-btn {
  padding: 4px;
  min-width: auto;
  height: auto;
  color: #409eff;
  opacity: 0.7;
  transition: all 0.2s;
}

.copy-btn:hover {
  opacity: 1;
  background-color: #ecf5ff;
  border-radius: 4px;
}

.copy-btn .el-icon {
  font-size: 14px;
}

/* 申根表单对话框样式 */
:deep(.schengen-dialog .el-dialog) {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

:deep(.schengen-dialog .el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
}

:deep(.schengen-dialog .el-dialog__body) {
  padding: 0;
  flex: 1;
  overflow: hidden;
}

:deep(.schengen-dialog .el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
}

@media (max-width: 768px) {
  :deep(.schengen-dialog .el-dialog) {
    width: 95% !important;
    margin: 2vh auto !important;
    max-height: 96vh;
  }
}

/* VAF表单下载进度弹窗样式 */
.download-progress-container {
  text-align: center;
  padding: 20px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.loading-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-icon .is-loading {
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.loading-icon .success-icon {
  color: #67c23a;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-info {
  text-align: center;
}

.status-message {
  font-size: 16px;
  color: #303133;
  margin-bottom: 8px;
  font-weight: 500;
}

.task-id {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.progress-indicator {
  width: 100%;
  max-width: 300px;
}

.progress-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.progress-actions .el-button {
  min-width: 100px;
}
</style>
