<template>
  <div class="profile-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
    </div>

    <!-- 主内容区域 -->
    <div class="profile-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="user-avatar">
            <el-icon size="32"><User /></el-icon>
          </div>
          <div class="header-text">
            <h1 class="page-title">个人中心</h1>
            <p class="page-subtitle">管理您的账户设置和偏好</p>
          </div>
        </div>
      </div>

      <!-- 用户信息卡片 -->
      <div class="profile-section">
        <div class="section-grid">
          <!-- 用户基本信息 -->
          <div class="info-card user-info-card">
            <div class="card-header">
              <div class="card-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <h3 class="card-title">用户信息</h3>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="info-label">用户名</span>
                <span class="info-value">{{ userStore.name || '未设置' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">权限等级</span>
                <el-tag
                  :type="getPermissionType(userStore.permission)"
                  effect="light"
                >
                  {{ getPermissionText(userStore.permission) }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="info-label">角色</span>
                <span class="info-value">{{
                  userStore.role_name || '未设置'
                }}</span>
              </div>
            </div>
          </div>

          <!-- 企微机器人管理 -->
          <div class="info-card webhook-card">
            <div class="card-header">
              <div class="card-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <h3 class="card-title">企微机器人</h3>
            </div>
            <div class="card-content">
              <!-- 添加新机器人 -->
              <div class="add-webhook-section">
                <el-form :model="webhookForm" class="webhook-form">
                  <el-form-item>
                    <el-input
                      v-model="newHookUrl"
                      placeholder="请输入企微机器人 Webhook URL"
                      class="modern-input"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Link /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      type="primary"
                      @click="addHookUrl"
                      :loading="isAdding"
                      class="modern-button"
                    >
                      <el-icon><Plus /></el-icon>
                      添加机器人
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>

              <!-- 已保存的机器人列表 -->
              <div class="webhook-list" v-if="hookUrls.length > 0">
                <h4 class="list-title">已配置的机器人</h4>
                <div class="webhook-items">
                  <div
                    v-for="(hook, index) in hookUrls"
                    :key="hook.id"
                    class="webhook-item"
                  >
                    <div class="webhook-content">
                      <div class="webhook-info">
                        <el-icon class="webhook-icon"><Setting /></el-icon>
                        <div class="webhook-details">
                          <span class="webhook-name"
                            >机器人 #{{ index + 1 }}</span
                          >
                          <el-input
                            v-model="hook.hook_url"
                            size="small"
                            class="webhook-url-input"
                            placeholder="Webhook URL"
                          />
                        </div>
                      </div>
                      <div class="webhook-actions">
                        <el-button
                          type="primary"
                          size="small"
                          @click="updateHookUrl(hook)"
                          :loading="hook.updating"
                          text
                        >
                          <el-icon><Edit /></el-icon>
                          更新
                        </el-button>
                        <el-button
                          type="danger"
                          size="small"
                          @click="deleteHookUrl(hook)"
                          :loading="hook.deleting"
                          text
                        >
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-state">
                <el-icon size="48" class="empty-icon"><ChatDotRound /></el-icon>
                <p class="empty-text">暂无配置的企微机器人</p>
                <p class="empty-hint">添加机器人以接收系统通知</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作区域 -->
      <div class="actions-section">
        <div class="action-card logout-card">
          <div class="action-content">
            <div class="action-info">
              <el-icon class="action-icon"><SwitchButton /></el-icon>
              <div class="action-text">
                <h4 class="action-title">退出登录</h4>
                <p class="action-description">安全退出当前账户</p>
              </div>
            </div>
            <el-button
              type="danger"
              @click="logout"
              class="logout-button"
              :loading="isLoggingOut"
            >
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  User,
  UserFilled,
  ChatDotRound,
  Link,
  Plus,
  Setting,
  Edit,
  Delete,
  SwitchButton,
} from '@element-plus/icons-vue';
import { useUserStore } from '@/stores/user';
import http from '@/utils/http';

const router = useRouter();
const userStore = useUserStore();

// 响应式数据
const newHookUrl = ref('');
const hookUrls = ref([]);
const isAdding = ref(false);
const isLoggingOut = ref(false);

// 表单数据
const webhookForm = reactive({
  hookUrl: '',
});

// 权限相关方法
const getPermissionText = permission => {
  const permissionMap = {
    admin: '管理员',
    kefu: '客服',
    user: '普通用户',
  };
  return permissionMap[permission] || '未知权限';
};

const getPermissionType = permission => {
  const typeMap = {
    admin: 'danger',
    kefu: 'warning',
    user: 'info',
  };
  return typeMap[permission] || 'info';
};

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    isLoggingOut.value = true;

    // 模拟退出延迟，提供更好的用户体验
    setTimeout(() => {
      localStorage.removeItem('token');
      userStore.logout();
      ElMessage.success('已安全退出');
      router.push('/login');
    }, 500);
  } catch {
    // 用户取消退出
  }
};

// 加载企微机器人列表
const loadHookUrls = async () => {
  try {
    const res = await http.get('/api/hooks');
    if (res.code === 1) {
      hookUrls.value = res.data.map(item => ({
        ...item,
        updating: false,
        deleting: false,
      }));
    }
  } catch (error) {
    console.error('加载企微机器人失败:', error);
    ElMessage.error('加载企微机器人失败');
  }
};

// 添加企微机器人
const addHookUrl = async () => {
  if (!newHookUrl.value.trim()) {
    ElMessage.warning('请输入企微机器人 Webhook URL');
    return;
  }

  // 简单的URL验证
  if (!newHookUrl.value.startsWith('http')) {
    ElMessage.warning('请输入有效的 Webhook URL');
    return;
  }

  isAdding.value = true;

  try {
    const res = await http.post('/api/hook', { hook_url: newHookUrl.value });
    if (res.code === 1) {
      ElMessage.success('企微机器人添加成功');
      newHookUrl.value = '';
      await loadHookUrls();
    } else {
      ElMessage.error(res.message || '添加失败');
    }
  } catch (error) {
    console.error('添加企微机器人失败:', error);
    ElMessage.error('添加企微机器人失败');
  } finally {
    isAdding.value = false;
  }
};

// 更新企微机器人
const updateHookUrl = async hook => {
  if (!hook.hook_url.trim()) {
    ElMessage.warning('Webhook URL 不能为空');
    return;
  }

  hook.updating = true;

  try {
    const res = await http.post('/api/hook/update', {
      id: hook.id,
      hook_url: hook.hook_url,
    });

    if (res.code === 1) {
      ElMessage.success('企微机器人更新成功');
      await loadHookUrls();
    } else {
      ElMessage.error(res.message || '更新失败');
    }
  } catch (error) {
    console.error('更新企微机器人失败:', error);
    ElMessage.error('更新企微机器人失败');
  } finally {
    hook.updating = false;
  }
};

// 删除企微机器人
const deleteHookUrl = async hook => {
  try {
    await ElMessageBox.confirm(
      `确定要删除机器人 #${hookUrls.value.indexOf(hook) + 1} 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    hook.deleting = true;

    const res = await http.post('/api/hook/delete', { id: hook.id });
    if (res.code === 1) {
      ElMessage.success('企微机器人删除成功');
      await loadHookUrls();
    } else {
      ElMessage.error(res.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除企微机器人失败:', error);
      ElMessage.error('删除企微机器人失败');
    }
  } finally {
    hook.deleting = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadHookUrls();
});
</script>

<style scoped>
/* 主页面样式 */
.profile-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 15%;
  animation-delay: 3s;
}

.shape-3 {
  width: 120px;
  height: 120px;
  bottom: 20%;
  left: 20%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 1;
  }
}

/* 主容器 */
.profile-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  margin-bottom: 32px;
  animation: slideDown 0.6s ease-out;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 24px 32px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
  font-weight: 500;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 主要内容区域 */
.profile-section {
  margin-bottom: 32px;
}

.section-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  animation: slideUp 0.6s ease-out 0.2s both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 信息卡片 */
.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  color: white;
  font-size: 18px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.card-content {
  flex: 1;
}

/* 用户信息卡片 */
.user-info-card .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.4);
}

.user-info-card .info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #2d3748;
  font-weight: 600;
}

/* 企微机器人卡片 */
.webhook-card {
  min-height: 400px;
}

.add-webhook-section {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.webhook-form .el-form-item {
  margin-bottom: 16px;
}

/* 现代化输入框样式 */
:deep(.modern-input .el-input__wrapper) {
  background: rgba(247, 250, 252, 0.8);
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  box-shadow: none;
}

:deep(.modern-input .el-input__wrapper:hover) {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(255, 255, 255, 0.9);
}

:deep(.modern-input.is-focus .el-input__wrapper) {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 现代化按钮样式 */
.modern-button {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.modern-button:hover::before {
  left: 100%;
}

/* 机器人列表 */
.list-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 16px 0;
}

.webhook-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.webhook-item {
  background: rgba(247, 250, 252, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.webhook-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateX(4px);
}

.webhook-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.webhook-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.webhook-icon {
  color: #667eea;
  font-size: 20px;
  margin-top: 4px;
}

.webhook-details {
  flex: 1;
}

.webhook-name {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.webhook-url-input {
  width: 100%;
}

.webhook-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  color: #a0aec0;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #4a5568;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.empty-hint {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

/* 操作区域 */
.actions-section {
  animation: slideUp 0.6s ease-out 0.4s both;
}

.action-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.action-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-icon {
  color: #e53e3e;
  font-size: 24px;
}

.action-text {
  flex: 1;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 4px 0;
}

.action-description {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.logout-button {
  background: linear-gradient(135deg, #e53e3e, #c53030);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.logout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(229, 62, 62, 0.4);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .section-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .header-content {
    padding: 20px 24px;
  }

  .page-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .profile-page {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .user-avatar {
    width: 56px;
    height: 56px;
  }

  .info-card {
    padding: 20px;
  }

  .webhook-content {
    flex-direction: column;
    gap: 12px;
  }

  .webhook-actions {
    align-self: stretch;
    justify-content: flex-end;
  }

  .action-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .profile-page {
    padding: 12px;
  }

  .header-content {
    padding: 16px;
    border-radius: 16px;
  }

  .info-card {
    padding: 16px;
    border-radius: 16px;
  }

  .floating-shape {
    display: none;
  }

  .webhook-item {
    padding: 12px;
  }

  .action-card {
    padding: 16px;
    border-radius: 16px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .header-content,
  .info-card,
  .action-card {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .page-title,
  .card-title,
  .action-title {
    color: #f7fafc;
  }

  .page-subtitle,
  .info-label,
  .action-description {
    color: #a0aec0;
  }

  .info-value {
    color: #e2e8f0;
  }

  .webhook-item {
    background: rgba(74, 85, 104, 0.6);
    border-color: rgba(113, 128, 150, 0.3);
  }

  .webhook-item:hover {
    background: rgba(74, 85, 104, 0.8);
  }

  :deep(.modern-input .el-input__wrapper) {
    background: rgba(74, 85, 104, 0.8);
    border-color: rgba(113, 128, 150, 0.3);
  }

  :deep(.modern-input .el-input__inner) {
    color: #f7fafc;
  }
}
</style>
