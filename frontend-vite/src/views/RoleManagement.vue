<template>
  <div class="role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <el-icon class="title-icon"><Setting /></el-icon>
          角色管理
        </h2>
        <p class="page-description">管理系统角色和权限分配</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddRoleDialog">
          <el-icon><Plus /></el-icon>
          新增角色
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="角色名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入角色名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchRoles">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 角色列表 -->
    <div class="table-section">
      <el-card>
        <el-table
          v-loading="loading"
          :data="filteredRoles"
          style="width: 100%"
          height="600"
          row-key="id"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="角色名称" min-width="150">
            <template #default="{ row }">
              <div class="role-name-cell">
                <el-tag :type="getRoleTagType(row)" effect="light" size="small">
                  {{ row.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column label="权限数量" width="120">
            <template #default="{ row }">
              <el-badge :value="row.permissions?.length || 0" class="permission-badge">
                <el-button type="info" text size="small">
                  权限
                </el-button>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column label="用户数量" width="120">
            <template #default="{ row }">
              <el-badge :value="row.user_count || 0" class="user-badge">
                <el-button type="success" text size="small">
                  用户
                </el-button>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                text
                size="small"
                @click="viewPermissions(row)"
              >
                <el-icon><View /></el-icon>
                查看权限
              </el-button>
              <el-button
                type="warning"
                text
                size="small"
                @click="editRole(row)"
                :disabled="!canEditRole(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="danger"
                text
                size="small"
                @click="deleteRole(row)"
                :disabled="!canDeleteRole(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 新增/编辑角色弹窗 -->
    <el-dialog
      v-model="roleDialogVisible"
      :title="roleDialogTitle"
      width="800px"
      :close-on-click-modal="false"
      @closed="resetRoleForm"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="roleForm.name"
            placeholder="请输入角色名称"
            :disabled="isEditMode && isSystemRole(roleForm)"
          />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="权限配置" prop="permissions">
          <div class="permission-tree-container">
            <el-tree
              ref="permissionTreeRef"
              :data="permissionTree"
              :props="treeProps"
              show-checkbox
              node-key="key"
              :default-checked-keys="roleForm.permissions"
              :check-strictly="false"
              class="permission-tree"
            >
              <template #default="{ node, data }">
                <div class="tree-node">
                  <span class="node-label">{{ data.label }}</span>
                  <span v-if="data.description" class="node-description">
                    {{ data.description }}
                  </span>
                </div>
              </template>
            </el-tree>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="roleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRole" :loading="saving">
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 权限查看弹窗 -->
    <el-dialog
      v-model="permissionViewDialogVisible"
      title="角色权限详情"
      width="600px"
    >
      <div class="permission-view-content">
        <div class="role-info">
          <h3>{{ currentRole?.name }}</h3>
          <p class="role-description">{{ currentRole?.description }}</p>
        </div>
        <el-divider />
        <div class="permission-groups">
          <div
            v-for="group in groupedPermissions"
            :key="group.module"
            class="permission-group"
          >
            <h4 class="group-title">{{ group.moduleName }}</h4>
            <div class="permission-list">
              <el-tag
                v-for="permission in group.permissions"
                :key="permission.key"
                size="small"
                class="permission-tag"
              >
                {{ permission.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 快速创建预定义角色提示 -->
    <div v-if="roles.length === 0" class="empty-state">
      <el-empty description="暂无角色数据">
        <template #extra>
          <div class="predefined-roles">
            <p>您可以快速创建预定义角色：</p>
            <el-space wrap>
              <el-button
                v-for="(role, key) in PREDEFINED_ROLES"
                :key="key"
                type="primary"
                plain
                size="small"
                @click="createPredefinedRole(role)"
              >
                创建{{ role.name }}
              </el-button>
            </el-space>
          </div>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/stores/user';
import http from '@/utils/http';
import { 
  MODULES, 
  PREDEFINED_ROLES, 
  getPermissionTree,
  formatPermissionName 
} from '@/utils/permissions';
import {
  Setting,
  Plus,
  Search,
  Edit,
  Delete,
  View,
} from '@element-plus/icons-vue';

const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const roles = ref([]);

// 搜索表单
const searchForm = reactive({
  name: '',
});

// 角色弹窗
const roleDialogVisible = ref(false);
const isEditMode = ref(false);
const currentRoleId = ref(null);
const roleFormRef = ref();
const permissionTreeRef = ref();

const roleForm = reactive({
  name: '',
  description: '',
  permissions: [],
});

// 权限查看弹窗
const permissionViewDialogVisible = ref(false);
const currentRole = ref(null);

// 权限树配置
const permissionTree = ref([]);
const treeProps = {
  children: 'children',
  label: 'label',
};

// 表单验证规则
const roleRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 20, message: '角色名称长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' },
  ],
};

// 计算属性
const roleDialogTitle = computed(() => {
  return isEditMode.value ? '编辑角色' : '新增角色';
});

const filteredRoles = computed(() => {
  if (!Array.isArray(roles.value)) {
    return [];
  }
  return roles.value.filter(role => {
    const matchName = !searchForm.name || 
      role.name.toLowerCase().includes(searchForm.name.toLowerCase());
    return matchName;
  });
});

const groupedPermissions = computed(() => {
  if (!currentRole.value?.permissions) return [];
  
  const groups = {};
  currentRole.value.permissions.forEach(permissionKey => {
    const parts = permissionKey.split('.');
    if (parts.length !== 2) return;
    
    const [moduleKey, actionKey] = parts;
    const module = Object.values(MODULES).find(m => m.key === moduleKey);
    if (!module) return;
    
    const action = Object.values(module.children).find(a => a.key === actionKey);
    if (!action) return;
    
    if (!groups[moduleKey]) {
      groups[moduleKey] = {
        module: moduleKey,
        moduleName: module.name,
        permissions: [],
      };
    }
    
    groups[moduleKey].permissions.push({
      key: permissionKey,
      name: action.name,
    });
  });
  
  return Object.values(groups);
});

// 工具函数
const getRoleTagType = (role) => {
  if (role.name.includes('管理员')) return 'danger';
  if (role.name.includes('客服')) return 'warning';
  return 'info';
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN');
};

const isSystemRole = (role) => {
  // 系统预定义角色不允许修改名称
  return ['超级管理员', '管理员', '客服', '普通用户'].includes(role.name);
};

const canEditRole = (role) => {
  // 检查编辑权限
  return userStore.permissions?.includes('role_management.edit_role') || false;
};

const canDeleteRole = (role) => {
  // 检查删除权限，且不能删除有用户的角色
  const hasPermission = userStore.permissions?.includes('role_management.delete_role') || false;
  const hasUsers = (role.user_count || 0) > 0;
  return hasPermission && !hasUsers;
};

// API 方法
const fetchRoles = async () => {
  loading.value = true;
  try {
    const response = await http.get('/api/roles');
    
    if (response.code === 1) {
      roles.value = Array.isArray(response.data?.roles) ? response.data.roles : [];
    } else {
      ElMessage.error(response.message || '获取角色列表失败');
      roles.value = [];
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
    ElMessage.error('获取角色列表失败');
    roles.value = [];
  } finally {
    loading.value = false;
  }
};

const saveRole = async () => {
  if (!roleFormRef.value) return;
  
  const valid = await roleFormRef.value.validate().catch(() => false);
  if (!valid) return;

  // 获取选中的权限
  const checkedKeys = permissionTreeRef.value.getCheckedKeys();
  const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys();
  
  // 只保存叶子节点权限
  const permissions = checkedKeys.filter(key => key.includes('.'));
  
  const roleData = {
    ...roleForm,
    permissions,
  };

  saving.value = true;
  try {
    let response;
    if (isEditMode.value) {
      response = await http.put(`/api/roles/${currentRoleId.value}`, roleData);
    } else {
      response = await http.post('/api/roles', roleData);
    }

    if (response.code === 1) {
      ElMessage.success(isEditMode.value ? '角色更新成功' : '角色创建成功');
      roleDialogVisible.value = false;
      await fetchRoles();
    } else {
      ElMessage.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('保存角色失败:', error);
    ElMessage.error('操作失败');
  } finally {
    saving.value = false;
  }
};

const createPredefinedRole = async (roleTemplate) => {
  saving.value = true;
  try {
    const response = await http.post('/api/roles', roleTemplate);
    
    if (response.code === 1) {
      ElMessage.success(`${roleTemplate.name}创建成功`);
      await fetchRoles();
    } else {
      ElMessage.error(response.message || '创建失败');
    }
  } catch (error) {
    console.error('创建预定义角色失败:', error);
    ElMessage.error('创建失败');
  } finally {
    saving.value = false;
  }
};

// 事件处理
const searchRoles = () => {
  // 本地搜索，如果需要服务端搜索可以调用fetchRoles
};

const resetSearch = () => {
  searchForm.name = '';
};

const showAddRoleDialog = () => {
  isEditMode.value = false;
  currentRoleId.value = null;
  roleDialogVisible.value = true;
};

const editRole = async (role) => {
  isEditMode.value = true;
  currentRoleId.value = role.id;
  
  Object.assign(roleForm, {
    name: role.name,
    description: role.description,
    permissions: role.permissions || [],
  });
  
  roleDialogVisible.value = true;
  
  // 等待对话框渲染完成后设置选中状态
  await nextTick();
  if (permissionTreeRef.value) {
    permissionTreeRef.value.setCheckedKeys(role.permissions || []);
  }
};

const viewPermissions = (role) => {
  currentRole.value = role;
  permissionViewDialogVisible.value = true;
};

const deleteRole = async (role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const response = await http.delete(`/api/roles/${role.id}`);
    
    if (response.code === 1) {
      ElMessage.success('角色删除成功');
      await fetchRoles();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const resetRoleForm = () => {
  Object.assign(roleForm, {
    name: '',
    description: '',
    permissions: [],
  });
  
  if (roleFormRef.value) {
    roleFormRef.value.clearValidate();
  }
  
  if (permissionTreeRef.value) {
    permissionTreeRef.value.setCheckedKeys([]);
  }
};

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    const userInfo = await http.get('/user/info');
    if (userInfo.code === 1) {
      userStore.setUser({
        token: userStore.token,
        permission: userStore.permission,
        permissions: userInfo.data?.permissions || [],
        id: userInfo.data?.id,
        name: userInfo.data?.name,
        role_id: userInfo.data?.role_id,
        role_name: userInfo.data?.role_name
      });
    }
  } catch (error) {
    console.error('同步用户信息失败:', error);
  }
};

// 生命周期
onMounted(async () => {
  // 同步获取最新用户信息
  await refreshUserInfo();
  
  // 检查访问权限
  if (!userStore.permissions?.includes('role_management.view_roles')) {
    ElMessage.error('权限不足，无法访问角色管理');
    return;
  }
  
  // 初始化权限树
  permissionTree.value = getPermissionTree();
  
  fetchRoles();
});
</script>

<style scoped>
.role-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* ==================== 页面头部 ==================== */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 28px;
  color: #409eff;
}

.page-description {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.header-right {
  flex-shrink: 0;
}

/* ==================== 搜索区域 ==================== */
.search-section {
  margin-bottom: 24px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

/* ==================== 表格区域 ==================== */
.table-section {
  margin-bottom: 24px;
}

.role-name-cell {
  display: flex;
  align-items: center;
}

.permission-badge,
.user-badge {
  cursor: pointer;
}

/* ==================== 表格样式 ==================== */
.el-table :deep(.el-table__row) {
  height: 60px;
}

.el-table :deep(.cell) {
  padding: 12px 0;
  display: flex;
  align-items: center;
}

.permission-badge :deep(.el-badge__content),
.user-badge :deep(.el-badge__content) {
  right: -10px;
  top: 0px;
  font-size: 11px;
  min-width: 18px;
  height: 18px;
  line-height: 16px;
  border: 1px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* ==================== 权限树 ==================== */
.permission-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.permission-tree {
  background: transparent;
}

.permission-tree :deep(.el-tree-node__content) {
  height: auto;
  min-height: 36px;
  padding: 4px 0;
  align-items: flex-start;
}

.permission-tree :deep(.el-tree-node__expand-icon) {
  margin-top: 6px;
}

.permission-tree :deep(.el-checkbox) {
  margin-top: 6px;
  margin-right: 8px;
}

.permission-tree :deep(.el-tree-node__label) {
  flex: 1;
  padding-left: 4px;
}

.tree-node {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  padding: 4px 0;
  line-height: 1.4;
}

.node-label {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
  word-break: break-word;
  width: 100%;
}

.node-description {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
  line-height: 1.3;
  word-break: break-word;
  width: 100%;
}

/* ==================== 权限查看 ==================== */
.permission-view-content {
  max-height: 500px;
  overflow-y: auto;
}

.role-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.role-description {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.permission-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.permission-group {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  background: #f8f9fa;
}

.group-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  margin: 0;
}

/* ==================== 空状态 ==================== */
.empty-state {
  text-align: center;
  padding: 40px;
}

.predefined-roles {
  margin-top: 16px;
}

.predefined-roles p {
  margin-bottom: 12px;
  color: #6c757d;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .role-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-bottom: 16px;
  }
  
  .permission-tree-container {
    max-height: 300px;
  }
}
</style>