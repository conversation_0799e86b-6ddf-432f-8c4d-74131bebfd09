<template>
  <div class="dashboard-page">
    <div class="sidebar">
      <!-- 顶部 Logo 区域 -->
      <div class="sidebar-header">
        <div class="logo">
          <el-icon class="logo-icon"><Stamp /></el-icon>
          <span class="logo-text">签证系统</span>
        </div>
        <div class="user-info">
          <el-avatar :size="32" class="user-avatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="user-details">
            <div class="user-name">{{ userStore.name || '用户' }}</div>
            <div class="user-role">{{ getRoleText(permission) }}</div>
          </div>
        </div>
      </div>

      <!-- 菜单区域 -->
      <el-menu
        mode="vertical"
        router
        :default-active="route.path"
        class="side-menu"
      >
        <!-- 动态菜单渲染 -->
        <div
          v-for="section in menuSections"
          :key="section.title"
          class="menu-section"
        >
          <div class="menu-section-title">{{ section.title }}</div>
          <el-menu-item
            v-for="menu in section.menus"
            :key="menu.path"
            :index="menu.path"
            class="menu-item-enhanced"
          >
            <div class="menu-item-content">
              <el-icon class="menu-icon">
                <component :is="menu.icon" />
              </el-icon>
              <span class="menu-text">{{ menu.name }}</span>
              <el-badge
                v-if="menu.badge"
                :value="menu.badge.value"
                :hidden="menu.badge.hidden"
                class="menu-badge"
              />
            </div>
          </el-menu-item>
        </div>
      </el-menu>
      <!-- 底部信息 -->
      <div class="sidebar-footer">
        <div class="system-info">
          <div class="version">v0.1.1 beta</div>
          <div class="copyright">© 2025 签证预约系统</div>
        </div>
      </div>
    </div>
    <div class="content">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import http from '@/utils/http';
import { useVfsStore } from '@/stores/useVfsStore';
import { useUserStore } from '@/stores/user';
import { usePermissions } from '@/composables/usePermissions';
import { useUserSync } from '@/composables/useUserSync';
import {
  Document,
  DocumentChecked,
  Search,
  User,
  UserFilled,
  Setting,
  Stamp,
  Money,
} from '@element-plus/icons-vue';

const route = useRoute();
const vfsStore = useVfsStore();
const userStore = useUserStore();
const { checkPermission } = usePermissions();
const { refreshUserInfo } = useUserSync();
const permission = userStore.permission;

// 待预约订单数量
const pendingCount = ref(0);

// 动态菜单配置
const menuSections = computed(() => {
  const sections = [];
  const userPermissions = userStore.permissions || [];

  // 订单管理菜单
  const orderMenus = [];

  if (checkPermission('order_management.view_pending_orders')) {
    orderMenus.push({
      path: '/dashboard/pending',
      name: '待预约订单',
      icon: 'Document',
      badge: {
        value: pendingCount.value,
        hidden: pendingCount.value === 0,
      },
    });
  }

  if (checkPermission('order_management.view_booked_orders')) {
    orderMenus.push({
      path: '/dashboard/completed',
      name: '已完成订单',
      icon: 'DocumentChecked',
    });
  }

  if (checkPermission('order_management.view_wait_pay_orders')) {
    orderMenus.push({
      path: '/dashboard/waitpay',
      name: '待支付订单',
      icon: 'Money',
    });
  }

  if (orderMenus.length > 0) {
    sections.push({
      title: '订单管理',
      menus: orderMenus,
    });
  }

  // 系统功能菜单
  const systemMenus = [];

  // 签证查询
  if (checkPermission('visa_query.view_visa_info')) {
    systemMenus.push({
      path: '/dashboard/visa',
      name: '放号查询',
      icon: 'Search',
    });
  }

  // 申根表单创建
  if (checkPermission('schengen_form.create_form')) {
    systemMenus.push({
      path: '/dashboard/schengen-form',
      name: '申根表单创建',
      icon: 'Document',
    });
  }

  // 用户管理
  if (checkPermission('user_management.view_users')) {
    systemMenus.push({
      path: '/dashboard/users',
      name: '用户管理',
      icon: 'UserFilled',
    });
  }

  // 角色管理
  if (checkPermission('role_management.view_roles')) {
    systemMenus.push({
      path: '/dashboard/roles',
      name: '角色管理',
      icon: 'Setting',
    });
  }

  // 个人中心 - 所有登录用户都可以访问
  systemMenus.push({
    path: '/dashboard/profile',
    name: '个人中心',
    icon: 'User',
  });

  if (systemMenus.length > 0) {
    sections.push({
      title: '系统功能',
      menus: systemMenus,
    });
  }

  return sections;
});

// 角色文本映射
const getRoleText = role => {
  const roleMap = {
    admin: '管理员',
    kefu: '客服',
    user: '普通用户',
  };
  return roleMap[role] || '用户';
};

// 获取待预约订单数量
const fetchPendingCount = async () => {
  try {
    if (permission === 'admin' || permission === 'kefu') {
      const res = await http.get('/api/get_pending_orders');
      if (res.code === 1) {
        // 统计待预约订单数量
        pendingCount.value = res.data?.length || 0;
      }
    }
  } catch (error) {
    console.error('获取待预约订单数量失败:', error);
  }
};

onMounted(async () => {
  // 同步获取最新用户信息（确保权限是最新的）
  await refreshUserInfo();

  // 加载 VFS 配置
  try {
    const resVfsConfig = await http.post('/get_vfs_config');
    vfsStore.setConfig(resVfsConfig.data);
  } catch (error) {
    console.error('加载 VFS 配置失败:', error);
  }

  // 获取待预约订单数量
  await fetchPendingCount();
});
</script>

<style scoped>
/* ==================== 主布局 ==================== */
.dashboard-page {
  display: flex;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 240px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  height: 100vh;
  overflow-y: auto;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.content {
  flex: 1;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

/* ==================== 侧边栏头部 ==================== */
.sidebar-header {
  padding: 16px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.logo-icon {
  font-size: 24px;
  color: #3498db;
  margin-right: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.logo-text {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
  margin-right: 8px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-role {
  font-size: 12px;
  color: #bdc3c7;
  opacity: 0.9;
}

/* ==================== 菜单区域 ==================== */
.side-menu {
  border-right: none;
  background: transparent;
  flex: 1;
  padding: 16px 0;
}

.menu-section {
  margin-bottom: 24px;
  padding: 0 16px;
}

.menu-section-title {
  font-size: 11px;
  font-weight: 600;
  color: #95a5a6;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 12px;
  padding: 0 12px;
  position: relative;
}

.menu-section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 16px;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #3498db, transparent);
  border-radius: 1px;
}

/* ==================== 菜单项样式 ==================== */
.menu-item-enhanced {
  margin-bottom: 6px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.menu-item-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s ease;
  z-index: 0;
}

.menu-item-enhanced:hover::before {
  width: 4px;
}

.menu-item-enhanced.is-active::before {
  width: 4px;
}

.menu-item-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 16px;
  position: relative;
  z-index: 1;
}

.menu-icon {
  font-size: 16px;
  margin-right: 10px;
  transition: all 0.3s ease;
  color: #bdc3c7;
}

.menu-text {
  font-size: 13px;
  font-weight: 500;
  color: #ecf0f1;
  flex: 1;
  transition: all 0.3s ease;
}

.menu-badge {
  margin-left: auto;
}

/* ==================== 菜单项状态 ==================== */
.menu-item-enhanced:hover {
  background: rgba(52, 152, 219, 0.1) !important;
  transform: translateX(4px);
}

.menu-item-enhanced:hover .menu-icon {
  color: #3498db;
  transform: scale(1.1);
}

.menu-item-enhanced:hover .menu-text {
  color: #ffffff;
}

.menu-item-enhanced.is-active {
  background: rgba(52, 152, 219, 0.15) !important;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.menu-item-enhanced.is-active .menu-icon {
  color: #3498db;
}

.menu-item-enhanced.is-active .menu-text {
  color: #ffffff;
  font-weight: 600;
}

/* ==================== 侧边栏底部 ==================== */
.sidebar-footer {
  padding: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
  margin-top: auto;
}

.system-info {
  text-align: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.version {
  font-size: 12px;
  font-weight: 600;
  color: #3498db;
  margin-bottom: 4px;
}

.copyright {
  font-size: 10px;
  color: #95a5a6;
  opacity: 0.8;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }

  .logo-text {
    font-size: 16px;
  }

  .menu-text {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .sidebar-header {
    padding: 16px;
  }

  .logo {
    padding: 8px 12px;
  }

  .logo-text {
    font-size: 14px;
  }

  .user-info {
    padding: 8px 12px;
  }

  .menu-section {
    padding: 0 16px;
  }

  .menu-item-content {
    padding: 12px 16px;
  }
}

/* ==================== 滚动条美化 ==================== */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ==================== Element Plus 覆盖样式 ==================== */
.side-menu .el-menu-item {
  background: transparent !important;
  border: none !important;
  color: inherit !important;
  padding: 0 !important;
  margin: 0 !important;
  height: auto !important;
  line-height: normal !important;
}

.side-menu .el-menu-item:hover {
  background: transparent !important;
}

.side-menu .el-menu-item.is-active {
  background: transparent !important;
}

/* ==================== Badge 样式 ==================== */
.menu-badge :deep(.el-badge__content) {
  background: #e74c3c;
  border: 2px solid #2c3e50;
  font-size: 10px;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  line-height: 14px;
}
</style>
