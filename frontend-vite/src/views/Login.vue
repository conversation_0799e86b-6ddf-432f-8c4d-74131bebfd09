<template>
  <div class="login-page">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主登录卡片 -->
    <div class="login-container">
      <div class="login-card">
        <!-- 品牌区域 -->
        <div class="brand-section">
          <div class="brand-icon">
            <el-icon size="48"><Tickets /></el-icon>
          </div>
          <h1 class="brand-title">签证预约系统</h1>
          <p class="brand-subtitle">安全 · 高效 · 便捷</p>
        </div>

        <!-- 登录表单 -->
        <div class="form-section">
          <h2 class="form-title">欢迎回来</h2>
          <p class="form-subtitle">请登录您的账户</p>

          <el-form :model="form" @submit.prevent class="login-form">
            <el-form-item class="form-item">
              <el-input
                v-model="form.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                size="large"
                clearable
                class="modern-input"
              />
            </el-form-item>

            <el-form-item class="form-item">
              <el-input
                v-model="form.password"
                placeholder="请输入密码"
                show-password
                prefix-icon="Lock"
                size="large"
                clearable
                class="modern-input"
                @keyup.enter="onLogin"
              />
            </el-form-item>

            <el-button
              type="primary"
              class="login-btn"
              size="large"
              @click="onLogin"
              :loading="isLoading"
            >
              <span v-if="!isLoading">登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form>
        </div>

        <!-- 底部信息 -->
        <div class="footer-section">
          <p class="copyright">© 2025 签证预约系统. All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { Tickets } from '@element-plus/icons-vue';
import http from '@/utils/http';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();
const form = reactive({ username: '', password: '' });
const isLoading = ref(false);

const onLogin = async () => {
  if (!form.username || !form.password) {
    ElMessage.warning('请输入用户名和密码');
    return;
  }

  isLoading.value = true;

  try {
    const res = await http.post('/user/login', form);
    localStorage.setItem('token', res.token);

    // 获取用户详细信息包括权限
    const userInfo = await http.get('/user/info');

    const userData = {
      token: res.token,
      permission: res.permission,
      permissions: userInfo.data?.permissions || [],
      id: userInfo.data?.id,
      name: userInfo.data?.name,
      role_id: userInfo.data?.role_id,
      role_name: userInfo.data?.role_name,
    };

    userStore.setUser(userData);

    ElMessage.success('登录成功');
    router.push('/dashboard');
  } catch (error) {
    console.error('登录失败:', error);
    ElMessage.error('用户名或密码错误');
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* 主页面样式 */
.login-page {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 10%;
  right: 20%;
  animation-delay: 1s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 登录容器 */
.login-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
}

/* 登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 48px 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  animation: slideUp 0.6s ease-out;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 品牌区域 */
.brand-section {
  text-align: center;
  margin-bottom: 40px;
}

.brand-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  margin-bottom: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
  }
  100% {
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  }
}

.brand-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
  font-weight: 500;
}

/* 表单区域 */
.form-section {
  margin-bottom: 32px;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
  text-align: center;
}

.form-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0 0 32px 0;
  text-align: center;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 24px;
}

/* 现代化输入框样式 */
:deep(.modern-input .el-input__wrapper) {
  background: rgba(247, 250, 252, 0.8);
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  box-shadow: none;
}

:deep(.modern-input .el-input__wrapper:hover) {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(255, 255, 255, 0.9);
}

:deep(.modern-input.is-focus .el-input__wrapper) {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

:deep(.modern-input .el-input__inner) {
  color: #2d3748;
  font-weight: 500;
}

:deep(.modern-input .el-input__inner::placeholder) {
  color: #a0aec0;
}

:deep(.modern-input .el-input__prefix) {
  color: #718096;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

/* 底部区域 */
.footer-section {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.copyright {
  font-size: 12px;
  color: #a0aec0;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-page {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
  }

  .brand-title {
    font-size: 24px;
  }

  .form-title {
    font-size: 20px;
  }

  .brand-icon {
    width: 64px;
    height: 64px;
  }

  .brand-icon .el-icon {
    font-size: 32px;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 24px 20px;
    border-radius: 16px;
  }

  .brand-title {
    font-size: 20px;
  }

  .form-title {
    font-size: 18px;
  }

  .brand-icon {
    width: 56px;
    height: 56px;
  }

  .brand-icon .el-icon {
    font-size: 28px;
  }

  .floating-shape {
    display: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(45, 55, 72, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .brand-title {
    color: #f7fafc;
  }

  .form-title {
    color: #f7fafc;
  }

  .brand-subtitle,
  .form-subtitle {
    color: #a0aec0;
  }

  :deep(.modern-input .el-input__wrapper) {
    background: rgba(74, 85, 104, 0.8);
    border-color: rgba(113, 128, 150, 0.3);
  }

  :deep(.modern-input .el-input__inner) {
    color: #f7fafc;
  }

  .footer-section {
    border-top-color: rgba(113, 128, 150, 0.3);
  }
}
</style>
