<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <el-icon class="title-icon"><User /></el-icon>
          用户管理
        </h2>
        <p class="page-description">管理系统用户、角色和权限</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showAddUserDialog">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-card>
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="用户名">
            <el-input
              v-model="searchForm.username"
              placeholder="请输入用户名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchUsers">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-card>
        <el-table
          v-loading="loading"
          :data="filteredUsers"
          style="width: 100%"
          height="600"
          row-key="id"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" min-width="120">
            <template #default="{ row }">
              <div class="username-cell">
                <el-avatar :size="32" class="user-avatar">
                  {{ row.username.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="username">{{ row.username }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名" min-width="100" />
          <el-table-column prop="role_name" label="角色" width="150">
            <template #default="{ row }">
              <el-tag
                v-if="row.role_name"
                type="info"
                effect="plain"
                size="small"
              >
                {{ row.role_name }}
              </el-tag>
              <span v-else class="no-role">无角色</span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="last_login" label="最后登录" width="180">
            <template #default="{ row }">
              {{ formatDate(row.last_login) || '从未登录' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                text
                size="small"
                @click="editUser(row)"
                :disabled="!canEditUser(row)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="warning"
                text
                size="small"
                @click="resetPassword(row)"
                :disabled="!canEditUser(row)"
              >
                <el-icon><Key /></el-icon>
                重置密码
              </el-button>
              <el-button
                type="danger"
                text
                size="small"
                @click="deleteUser(row)"
                :disabled="!canDeleteUser(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalUsers"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑用户弹窗 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="userDialogTitle"
      width="500px"
      :close-on-click-modal="false"
      @closed="resetUserForm"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="userForm.username"
            placeholder="请输入用户名"
            :disabled="isEditMode"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item v-if="!isEditMode" label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="角色" prop="role_id">
          <el-select
            v-model="userForm.role_id"
            placeholder="请选择角色"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="role in availableRoles"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            >
              <span>{{ role.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ role.description }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 重置密码弹窗 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="重置密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="80px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmResetPassword"
          :loading="saving"
        >
          确认重置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useUserStore } from '@/stores/user';
import http from '@/utils/http';
import { User, Plus, Search, Edit, Delete, Key } from '@element-plus/icons-vue';

const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const users = ref([]);
const availableRoles = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const totalUsers = ref(0);

// 搜索表单
const searchForm = reactive({
  username: '',
});

// 用户弹窗
const userDialogVisible = ref(false);
const isEditMode = ref(false);
const currentUserId = ref(null);
const userFormRef = ref();

const userForm = reactive({
  username: '',
  name: '',
  password: '',
  role_id: null,
});

// 密码重置弹窗
const passwordDialogVisible = ref(false);
const currentResetUserId = ref(null);
const passwordFormRef = ref();

const passwordForm = reactive({
  newPassword: '',
  confirmPassword: '',
});

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    {
      min: 2,
      max: 20,
      message: '用户名长度在 3 到 20 个字符',
      trigger: 'blur',
    },
  ],
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  permission: [{ required: true, message: '请选择权限', trigger: 'change' }],
};

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
};

// 计算属性
const userDialogTitle = computed(() => {
  return isEditMode.value ? '编辑用户' : '新增用户';
});

const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchUsername =
      !searchForm.username ||
      user.username.toLowerCase().includes(searchForm.username.toLowerCase());
    return matchUsername;
  });
});

// 工具函数

const formatDate = dateString => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN');
};

const canEditUser = user => {
  // 管理员可以编辑所有用户，但不能编辑自己的权限
  if (userStore.permission === 'admin') {
    return true;
  }
  return false;
};

const canDeleteUser = user => {
  // 只有管理员可以删除用户，且不能删除自己
  if (userStore.permission === 'admin' && user.id !== userStore.id) {
    return true;
  }
  return false;
};

// API 方法
const fetchRoles = async () => {
  try {
    const response = await http.get('/api/roles');
    if (response.code === 1) {
      availableRoles.value = Array.isArray(response.data?.roles)
        ? response.data.roles
        : [];
    } else {
      availableRoles.value = [];
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
    availableRoles.value = [];
  }
};

const fetchUsers = async () => {
  loading.value = true;
  try {
    const response = await http.get('/api/users', {
      params: {
        page: currentPage.value,
        page_size: pageSize.value,
      },
    });

    if (response.code === 1) {
      users.value = response.data.users || [];
      totalUsers.value = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

const saveUser = async () => {
  if (!userFormRef.value) return;

  const valid = await userFormRef.value.validate().catch(() => false);
  if (!valid) return;

  saving.value = true;
  try {
    let response;
    if (isEditMode.value) {
      response = await http.put(`/api/users/${currentUserId.value}`, userForm);
    } else {
      response = await http.post('/api/users', userForm);
    }

    if (response.code === 1) {
      ElMessage.success(isEditMode.value ? '用户更新成功' : '用户创建成功');
      userDialogVisible.value = false;
      await fetchUsers();
    } else {
      ElMessage.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('保存用户失败:', error);
    ElMessage.error('操作失败');
  } finally {
    saving.value = false;
  }
};

const confirmResetPassword = async () => {
  if (!passwordFormRef.value) return;

  const valid = await passwordFormRef.value.validate().catch(() => false);
  if (!valid) return;

  saving.value = true;
  try {
    const response = await http.post(
      `/api/users/${currentResetUserId.value}/reset-password`,
      {
        new_password: passwordForm.newPassword,
      }
    );

    if (response.code === 1) {
      ElMessage.success('密码重置成功');
      passwordDialogVisible.value = false;
    } else {
      ElMessage.error(response.message || '密码重置失败');
    }
  } catch (error) {
    console.error('密码重置失败:', error);
    ElMessage.error('密码重置失败');
  } finally {
    saving.value = false;
  }
};

// 事件处理
const searchUsers = () => {
  currentPage.value = 1;
  fetchUsers();
};

const resetSearch = () => {
  searchForm.username = '';
  currentPage.value = 1;
  fetchUsers();
};

const showAddUserDialog = () => {
  isEditMode.value = false;
  currentUserId.value = null;
  userDialogVisible.value = true;
};

const editUser = user => {
  isEditMode.value = true;
  currentUserId.value = user.id;

  Object.assign(userForm, {
    username: user.username,
    name: user.name,
    role_id: user.role_id || null,
    password: '', // 编辑时不显示密码
  });

  userDialogVisible.value = true;
};

const resetPassword = user => {
  currentResetUserId.value = user.id;
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
  passwordDialogVisible.value = true;
};

const deleteUser = async user => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const response = await http.delete(`/api/users/${user.id}`);

    if (response.code === 1) {
      ElMessage.success('用户删除成功');
      await fetchUsers();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const resetUserForm = () => {
  Object.assign(userForm, {
    username: '',
    name: '',
    password: '',
    role_id: null,
  });

  if (userFormRef.value) {
    userFormRef.value.clearValidate();
  }
};

const handleSizeChange = newSize => {
  pageSize.value = newSize;
  fetchUsers();
};

const handleCurrentChange = newPage => {
  currentPage.value = newPage;
  fetchUsers();
};

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    const userInfo = await http.get('/user/info');
    if (userInfo.code === 1) {
      userStore.setUser({
        token: userStore.token,
        permission: userStore.permission,
        permissions: userInfo.data?.permissions || [],
        id: userInfo.data?.id,
        name: userInfo.data?.name,
        role_id: userInfo.data?.role_id,
        role_name: userInfo.data?.role_name,
      });
    }
  } catch (error) {
    console.error('同步用户信息失败:', error);
  }
};

// 生命周期
onMounted(async () => {
  // 同步获取最新用户信息
  await refreshUserInfo();

  // 只有管理员可以访问用户管理
  if (userStore.permission !== 'admin') {
    ElMessage.error('权限不足，无法访问用户管理');
    return;
  }

  fetchRoles();
  fetchUsers();
});
</script>

<style scoped>
.user-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* ==================== 页面头部 ==================== */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 28px;
  color: #409eff;
}

.page-description {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.header-right {
  flex-shrink: 0;
}

/* ==================== 搜索区域 ==================== */
.search-section {
  margin-bottom: 24px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

/* ==================== 表格区域 ==================== */
.table-section {
  margin-bottom: 24px;
}

.username-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  flex-shrink: 0;
}

.username {
  font-weight: 500;
  color: #2c3e50;
}

.no-role {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

/* ==================== 分页 ==================== */
.pagination-section {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

/* ==================== 弹窗样式 ==================== */
.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 16px 24px;
}

.el-dialog__body {
  padding: 24px;
}

.el-dialog__footer {
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .user-management {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
