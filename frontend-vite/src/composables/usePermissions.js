/**
 * 权限管理Composable
 * 提供权限检查和路由守卫功能
 */
import { computed } from 'vue';
import { useUserStore } from '@/stores/user';
import { hasPermission, hasRoutePermission, getAccessibleMenus } from '@/utils/permissions';

export function usePermissions() {
  const userStore = useUserStore();
  
  // 获取用户权限列表
  const userPermissions = computed(() => {
    return userStore.permissions || [];
  });
  
  // 检查是否有特定权限
  const checkPermission = (permission) => {
    return hasPermission(userPermissions.value, permission);
  };
  
  // 检查是否有多个权限中的任意一个
  const checkAnyPermission = (permissions) => {
    if (!Array.isArray(permissions)) return false;
    return permissions.some(permission => checkPermission(permission));
  };
  
  // 检查是否有所有权限
  const checkAllPermissions = (permissions) => {
    if (!Array.isArray(permissions)) return false;
    return permissions.every(permission => checkPermission(permission));
  };
  
  // 检查路由权限
  const checkRoutePermission = (routePath) => {
    return hasRoutePermission(userPermissions.value, routePath);
  };
  
  // 获取可访问的菜单
  const accessibleMenus = computed(() => {
    return getAccessibleMenus(userPermissions.value);
  });
  
  // 检查是否是管理员
  const isAdmin = computed(() => {
    return userStore.permission === 'admin';
  });
  
  // 检查是否是客服
  const isCustomerService = computed(() => {
    return userStore.permission === 'kefu';
  });
  
  // 检查是否是普通用户
  const isUser = computed(() => {
    return userStore.permission === 'user';
  });
  
  return {
    userPermissions,
    checkPermission,
    checkAnyPermission,
    checkAllPermissions,
    checkRoutePermission,
    accessibleMenus,
    isAdmin,
    isCustomerService,
    isUser,
  };
}