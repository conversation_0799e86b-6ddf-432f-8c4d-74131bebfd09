/**
 * 用户信息同步组合式函数
 * 用于在页面刷新时自动同步最新的用户权限信息
 */

import { useUserStore } from '@/stores/user';
import http from '@/utils/http';

export function useUserSync() {
  const userStore = useUserStore();

  /**
   * 刷新用户信息
   * 获取最新的用户权限和角色信息
   */
  const refreshUserInfo = async () => {
    try {
      const userInfo = await http.get('/user/info');
      if (userInfo.code === 1) {
        // 更新用户store中的信息，保持token不变
        userStore.setUser({
          token: userStore.token,
          permission: userStore.permission,
          permissions: userInfo.data?.permissions || [],
          id: userInfo.data?.id,
          name: userInfo.data?.name,
          role_id: userInfo.data?.role_id,
          role_name: userInfo.data?.role_name
        });
        
        console.log('用户信息已同步更新:', {
          permissions: userInfo.data?.permissions?.length || 0,
          role: userInfo.data?.role_name
        });
        
        return userInfo.data;
      }
    } catch (error) {
      console.error('同步用户信息失败:', error);
      throw error;
    }
  };

  /**
   * 检查用户是否有指定权限
   * @param {string} permission 权限标识
   * @returns {boolean}
   */
  const hasPermission = (permission) => {
    return userStore.permissions?.includes(permission) || false;
  };

  /**
   * 检查用户是否是管理员
   * @returns {boolean}
   */
  const isAdmin = () => {
    return userStore.permission === 'admin';
  };

  return {
    refreshUserInfo,
    hasPermission,
    isAdmin,
    userStore
  };
}