import axios from 'axios';
import { ElMessage } from 'element-plus';

// 创建 axios 实例
const http = axios.create({
  baseURL: 'http://120.27.241.45:5005', // 替换为你 FastAPI 服务地址
  timeout: 60000,
});

// 请求拦截器：自动携带 token
http.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器：处理成功与失败
http.interceptors.response.use(
  response => {
    const contentType = response.headers['content-type'];

    // 处理图片响应
    if (contentType && contentType.startsWith('image/')) {
      return response.data; // 返回 blob 类型图片
    }

    // 处理文件下载响应（只有明确指定responseType为blob的请求）
    if (response.config.responseType === 'blob') {
      return response; // 返回完整的响应对象，包含headers
    }

    // 处理普通JSON响应
    if (
      response.data &&
      typeof response.data === 'object' &&
      'code' in response.data
    ) {
      if (response.data.code === 0 || response.data.code === 1) {
        return response.data;
      } else {
        ElMessage.error(response.data.message || '服务器响应异常');
        return Promise.reject(response);
      }
    }

    // 其他情况直接返回响应数据
    return response.data;
  },
  error => {
    const status = error.response?.status;
    if (status === 401) {
      ElMessage.error('未授权，请重新登录');
      localStorage.removeItem('token');
      location.href = '/login';
    } else if (status === 403) {
      ElMessage.error('权限不足');
      localStorage.removeItem('token');
      location.href = '/login';
    } else {
      ElMessage.error('请求失败，请稍后再试');
    }
    return Promise.reject(error);
  }
);

export default http;
