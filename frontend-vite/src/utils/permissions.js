/**
 * 权限管理工具
 * 定义系统中的页面功能模块和权限控制
 */

// 系统功能模块定义
export const MODULES = {
  // 订单管理
  ORDER_MANAGEMENT: {
    key: 'order_management',
    name: '订单管理',
    description: '管理签证订单',
    children: {
      VIEW_PENDING_ORDERS: {
        key: 'view_pending_orders',
        name: '查看待预约订单',
      },
      VIEW_BOOKED_ORDERS: { key: 'view_booked_orders', name: '查看已预约订单' },
      VIEW_WAIT_PAY_ORDERS: {
        key: 'view_wait_pay_orders',
        name: '查看待支付订单',
      },
      CREATE_ORDER: { key: 'create_order', name: '创建订单' },
      EDIT_ORDER: { key: 'edit_order', name: '编辑订单' },
      DELETE_ORDER: { key: 'delete_order', name: '删除订单' },
      MARK_PAID: { key: 'mark_paid', name: '标记已支付' },
    },
  },

  // 用户管理
  USER_MANAGEMENT: {
    key: 'user_management',
    name: '用户管理',
    description: '管理系统用户',
    children: {
      VIEW_USERS: { key: 'view_users', name: '查看用户列表' },
      CREATE_USER: { key: 'create_user', name: '创建用户' },
      EDIT_USER: { key: 'edit_user', name: '编辑用户' },
      DELETE_USER: { key: 'delete_user', name: '删除用户' },
      RESET_PASSWORD: { key: 'reset_password', name: '重置密码' },
    },
  },

  // 角色管理
  ROLE_MANAGEMENT: {
    key: 'role_management',
    name: '角色管理',
    description: '管理系统角色和权限',
    children: {
      VIEW_ROLES: { key: 'view_roles', name: '查看角色列表' },
      CREATE_ROLE: { key: 'create_role', name: '创建角色' },
      EDIT_ROLE: { key: 'edit_role', name: '编辑角色' },
      DELETE_ROLE: { key: 'delete_role', name: '删除角色' },
      ASSIGN_PERMISSIONS: { key: 'assign_permissions', name: '分配权限' },
    },
  },

  // 签证查询
  VISA_QUERY: {
    key: 'visa_query',
    name: '签证查询',
    description: '查询签证放号信息',
    children: {
      VIEW_VISA_INFO: { key: 'view_visa_info', name: '查看签证信息' },
    },
  },

  // 申根表单管理
  SCHENGEN_FORM: {
    key: 'schengen_form',
    name: '申根表单管理',
    description: '管理申根签证表单',
    children: {
      CREATE_FORM: { key: 'create_form', name: '创建申根表单' },
      VIEW_FORMS: { key: 'view_forms', name: '查看申根表单' },
      EDIT_FORM: { key: 'edit_form', name: '编辑申根表单' },
      DELETE_FORM: { key: 'delete_form', name: '删除申根表单' },
    },
  },

  // 个人中心
  PROFILE: {
    key: 'profile',
    name: '个人中心',
    description: '个人信息管理',
    children: {
      VIEW_PROFILE: { key: 'view_profile', name: '查看个人信息' },
      EDIT_PROFILE: { key: 'edit_profile', name: '编辑个人信息' },
      CHANGE_PASSWORD: { key: 'change_password', name: '修改密码' },
    },
  },
};

// 预定义角色
export const PREDEFINED_ROLES = {
  SUPER_ADMIN: {
    name: '超级管理员',
    description: '拥有系统所有权限',
    permissions: getAllPermissions(),
  },
  ADMIN: {
    name: '管理员',
    description: '管理订单和用户',
    permissions: [
      'order_management.view_pending_orders',
      'order_management.view_booked_orders',
      'order_management.view_wait_pay_orders',
      'order_management.create_order',
      'order_management.edit_order',
      'order_management.delete_order',
      'order_management.mark_paid',
      'user_management.view_users',
      'user_management.create_user',
      'user_management.edit_user',
      'user_management.delete_user',
      'user_management.reset_password',
      'visa_query.view_visa_info',
      'schengen_form.create_form',
      'schengen_form.view_forms',
      'schengen_form.edit_form',
      'schengen_form.delete_form',
      'profile.view_profile',
      'profile.edit_profile',
      'profile.change_password',
    ],
  },
  CUSTOMER_SERVICE: {
    name: '客服',
    description: '处理订单和客户服务',
    permissions: [
      'order_management.view_pending_orders',
      'order_management.view_booked_orders',
      'order_management.view_wait_pay_orders',
      'order_management.create_order',
      'order_management.edit_order',
      'order_management.mark_paid',
      'visa_query.view_visa_info',
      'schengen_form.create_form',
      'schengen_form.view_forms',
      'schengen_form.edit_form',
      'profile.view_profile',
      'profile.edit_profile',
      'profile.change_password',
    ],
  },
  USER: {
    name: '普通用户',
    description: '查看个人相关信息',
    permissions: [
      'profile.view_profile',
      'profile.edit_profile',
      'profile.change_password',
    ],
  },
};

// 路由权限映射
export const ROUTE_PERMISSIONS = {
  '/dashboard/pending': ['order_management.view_pending_orders'],
  '/dashboard/completed': ['order_management.view_booked_orders'],
  '/dashboard/waitpay': ['order_management.view_wait_pay_orders'],
  '/dashboard/users': ['user_management.view_users'],
  '/dashboard/roles': ['role_management.view_roles'],
  '/dashboard/visa': ['visa_query.view_visa_info'],
  '/dashboard/schengen-form': ['schengen_form.create_form'],
  // 个人中心不需要特殊权限，所有用户都可以访问
  // '/dashboard/profile': ['profile.view_profile'],
};

/**
 * 获取所有权限列表
 */
function getAllPermissions() {
  const permissions = [];
  Object.values(MODULES).forEach(module => {
    Object.values(module.children).forEach(permission => {
      permissions.push(`${module.key}.${permission.key}`);
    });
  });
  return permissions;
}

/**
 * 获取权限树结构（用于权限选择组件）
 */
export function getPermissionTree() {
  return Object.values(MODULES).map(module => ({
    key: module.key,
    label: module.name,
    description: module.description,
    children: Object.values(module.children).map(permission => ({
      key: `${module.key}.${permission.key}`,
      label: permission.name,
    })),
  }));
}

/**
 * 检查用户是否有某个权限
 */
export function hasPermission(userPermissions, requiredPermission) {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false;
  }
  return userPermissions.includes(requiredPermission);
}

/**
 * 检查用户是否有某个路由的访问权限
 */
export function hasRoutePermission(userPermissions, routePath) {
  const requiredPermissions = ROUTE_PERMISSIONS[routePath];
  if (!requiredPermissions) {
    return true; // 没有配置权限要求的路由默认允许访问
  }

  return requiredPermissions.some(permission =>
    hasPermission(userPermissions, permission)
  );
}

/**
 * 获取用户可访问的菜单列表
 */
export function getAccessibleMenus(userPermissions) {
  const menus = [];

  // 订单管理菜单
  if (hasPermission(userPermissions, 'order_management.view_pending_orders')) {
    menus.push({
      path: '/dashboard/pending',
      name: '待预约订单',
      icon: 'Document',
    });
  }

  if (hasPermission(userPermissions, 'order_management.view_booked_orders')) {
    menus.push({
      path: '/dashboard/completed',
      name: '已完成订单',
      icon: 'DocumentChecked',
    });
  }

  // 签证查询
  if (hasPermission(userPermissions, 'visa_query.view_visa_info')) {
    menus.push({
      path: '/dashboard/visa',
      name: '放号查询',
      icon: 'Search',
    });
  }

  // 用户管理
  if (hasPermission(userPermissions, 'user_management.view_users')) {
    menus.push({
      path: '/dashboard/users',
      name: '用户管理',
      icon: 'UserFilled',
    });
  }

  // 角色管理
  if (hasPermission(userPermissions, 'role_management.view_roles')) {
    menus.push({
      path: '/dashboard/roles',
      name: '角色管理',
      icon: 'Setting',
    });
  }

  // 个人中心
  if (hasPermission(userPermissions, 'profile.view_profile')) {
    menus.push({
      path: '/dashboard/profile',
      name: '个人中心',
      icon: 'User',
    });
  }

  return menus;
}

/**
 * 权限格式化显示
 */
export function formatPermissionName(permissionKey) {
  const parts = permissionKey.split('.');
  if (parts.length !== 2) return permissionKey;

  const [moduleKey, actionKey] = parts;
  const module = Object.values(MODULES).find(m => m.key === moduleKey);
  if (!module) return permissionKey;

  const action = Object.values(module.children).find(a => a.key === actionKey);
  if (!action) return permissionKey;

  return `${module.name} - ${action.name}`;
}
