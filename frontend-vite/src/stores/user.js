import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    permission: localStorage.getItem('permission') || '',
    permissions: JSON.parse(localStorage.getItem('permissions') || '[]'),
    id: localStorage.getItem('userId') || '',
    name: localStorage.getItem('userName') || '',
    role_id: localStorage.getItem('userRoleId') || '',
    role_name: localStorage.getItem('userRoleName') || '',
  }),
  getters: {
    isLoggedIn: (state) => !!state.token,
  },
  actions: {
    setUser({ token, permission, permissions, id, name, role_id, role_name }) {
      this.token = token;
      this.permission = permission;
      this.permissions = permissions || [];
      this.id = id || '';
      this.name = name || '';
      this.role_id = role_id || '';
      this.role_name = role_name || '';
      
      localStorage.setItem('token', token);
      localStorage.setItem('permission', permission);
      localStorage.setItem('permissions', JSON.stringify(permissions || []));
      localStorage.setItem('userId', id || '');
      localStorage.setItem('userName', name || '');
      localStorage.setItem('userRoleId', role_id || '');
      localStorage.setItem('userRoleName', role_name || '');
    },
    logout() {
      this.token = '';
      this.permission = '';
      this.permissions = [];
      this.id = '';
      this.name = '';
      this.role_id = '';
      this.role_name = '';
      
      localStorage.removeItem('token');
      localStorage.removeItem('permission');
      localStorage.removeItem('permissions');
      localStorage.removeItem('userId');
      localStorage.removeItem('userName');
      localStorage.removeItem('userRoleId');
      localStorage.removeItem('userRoleName');
    },
  },
});
