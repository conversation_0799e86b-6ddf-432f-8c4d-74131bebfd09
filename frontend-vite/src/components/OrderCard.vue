<template>
  <el-card class="order-card" shadow="hover">
    <div class="card-header">
      <div class="title">{{ order.country }} - {{ order.center }}</div>
      <el-tag :type="tagType" effect="light" size="small">{{
        statusLabel
      }}</el-tag>
    </div>

    <div class="card-body">
      <p><strong>申请人：</strong>{{ order.applicant }}</p>
      <p><strong>预约时间：</strong>{{ order.date }}</p>
      <p><strong>订单编号：</strong>{{ order.id }}</p>
    </div>
  </el-card>
</template>

<script setup>
const props = defineProps({
  order: Object,
  type: String,
});

const tagType = props.type === 'pending' ? 'warning' : 'success';
const statusLabel = props.type === 'pending' ? '待预约' : '已预约';
</script>

<style scoped>
.order-card {
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease;
}
.order-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title {
  font-weight: bold;
  color: #222;
}

.card-body p {
  margin: 4px 0;
  color: #555;
  font-size: 14px;
}
</style>
