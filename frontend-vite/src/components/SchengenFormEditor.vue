<template>
  <div class="schengen-form-editor">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 步骤指示器 -->
      <div class="steps-container">
        <el-steps :active="currentStep" align-center>
          <el-step
            title="基本资格信息"
            description="姓名、护照、国籍等基本信息"
          />
          <el-step title="护照详细信息" description="护照详细信息及个人资料" />
          <el-step title="申请人信息" description="职业、联系方式、工作信息" />
          <el-step
            title="旅行计划信息"
            description="旅行目的、入境次数、签证历史"
          />
          <el-step title="住宿安排信息" description="邀请方信息及住宿安排" />
          <el-step
            title="附加信息及确认"
            description="费用承担、欧盟关系、最终确认"
          />
        </el-steps>
      </div>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1: 资格准则 -->
        <div v-if="currentStep === 0" class="step-panel">
          <h3 class="step-title">第一步：基本资格信息</h3>
          <el-form
            ref="step1FormRef"
            :model="formData"
            label-width="140px"
            class="step-form"
          >
            <!-- 护照上传和识别 -->
            <div class="passport-upload-section">
              <el-form-item label="护照上传">
                <div class="upload-container">
                  <el-upload
                    class="passport-uploader"
                    :show-file-list="false"
                    :before-upload="beforeUpload"
                    :http-request="handlePassportUpload"
                    accept="image/*"
                    drag
                  >
                    <div class="upload-area">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">点击或拖拽上传护照</div>
                      <div class="upload-hint">支持 JPG、PNG 格式</div>
                    </div>
                  </el-upload>
                  <div v-if="uploading" class="upload-loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                    <span>识别中...</span>
                  </div>
                </div>
              </el-form-item>
            </div>

            <!-- 姓名信息 -->
            <div class="form-row">
              <el-form-item
                label="姓"
                prop="surname"
                class="form-item-half"
                required
              >
                <el-input v-model="formData.surname" placeholder="请输入姓" />
              </el-form-item>
              <el-form-item
                label="名"
                prop="given_name"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.given_name"
                  placeholder="请输入名"
                />
              </el-form-item>
            </div>

            <!-- 基本信息 -->
            <div class="form-row">
              <el-form-item
                label="性别"
                prop="gender"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.gender"
                  placeholder="请选择性别"
                  style="width: 100%"
                >
                  <el-option label="男" value="M" />
                  <el-option label="女" value="F" />
                </el-select>
              </el-form-item>
              <el-form-item
                label="出生日期"
                prop="birth_date"
                class="form-item-half"
                required
              >
                <el-date-picker
                  v-model="formData.birth_date"
                  type="date"
                  placeholder="选择出生日期"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  style="width: 100%"
                />
              </el-form-item>
            </div>

            <!-- 国籍选择 -->
            <div class="form-row">
              <el-form-item label="现国籍" prop="nationality" required>
                <el-select
                  v-model="formData.nationality"
                  placeholder="请选择国籍"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in countries"
                    :key="country.isoCode"
                    :label="country.nationalityName"
                    :value="country.isoCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 护照信息 -->
            <div class="form-row">
              <el-form-item
                label="护照号码"
                prop="passport_number"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.passport_number"
                  placeholder="请输入护照号码"
                />
              </el-form-item>
              <el-form-item
                label="护照有效期"
                prop="passport_expire_date"
                class="form-item-half"
                required
              >
                <el-date-picker
                  v-model="formData.passport_expire_date"
                  type="date"
                  placeholder="选择护照有效期"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 步骤2: 护照信息 -->
        <div v-if="currentStep === 1" class="step-panel">
          <h3 class="step-title">第二步：护照详细信息</h3>
          <el-form
            ref="step2FormRef"
            :model="formData"
            label-width="140px"
            class="step-form"
          >
            <!-- 姓氏信息 -->
            <div class="form-row">
              <el-form-item
                label="姓"
                prop="passport_surname"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.passport_surname"
                  placeholder="请输入姓"
                />
              </el-form-item>
              <el-form-item
                label="出生时姓氏"
                prop="birth_surname"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.birth_surname"
                  placeholder="请输入出生时姓氏"
                />
              </el-form-item>
            </div>

            <!-- 基本信息 -->
            <div class="form-row">
              <el-form-item
                label="名"
                prop="passport_given_name"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.passport_given_name"
                  placeholder="请输入名"
                />
              </el-form-item>
              <el-form-item
                label="出生日期"
                prop="passport_birth_date"
                class="form-item-half"
                required
              >
                <el-date-picker
                  v-model="formData.passport_birth_date"
                  type="date"
                  placeholder="选择出生日期"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  style="width: 100%"
                />
              </el-form-item>
            </div>

            <!-- 出生信息 -->
            <div class="form-row">
              <el-form-item
                label="出生国"
                prop="birth_country"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.birth_country"
                  placeholder="请选择出生国"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in countries"
                    :key="country.isoCode"
                    :label="country.nationalityName"
                    :value="country.isoCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="出生地(拼音)"
                prop="birth_place"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.birth_place"
                  placeholder="请输入出生地拼音，如：SHANGHAI"
                />
              </el-form-item>
            </div>

            <!-- 国籍信息 -->
            <div class="form-row">
              <el-form-item
                label="现国籍"
                prop="current_nationality"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.current_nationality"
                  placeholder="请选择现国籍"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in countries"
                    :key="country.isoCode"
                    :label="country.nationalityName"
                    :value="country.isoCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="出生时国籍"
                prop="birth_nationality"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.birth_nationality"
                  placeholder="请选择出生时国籍"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in countries"
                    :key="country.isoCode"
                    :label="country.nationalityName"
                    :value="country.isoCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 个人详细信息 -->
            <div class="form-row">
              <el-form-item
                label="性别"
                prop="passport_gender"
                class="form-item-third"
                required
              >
                <el-select
                  v-model="formData.passport_gender"
                  placeholder="请选择性别"
                  style="width: 100%"
                >
                  <el-option label="男" value="M" />
                  <el-option label="女" value="F" />
                </el-select>
              </el-form-item>
              <el-form-item
                label="婚姻状况"
                prop="passport_marital_status"
                class="form-item-third"
                required
              >
                <el-select
                  v-model="formData.passport_marital_status"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="status in maritalStatusOptions"
                    :key="status.masterCode"
                    :label="status.displayName_cn"
                    :value="status.masterCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="未成年申请者"
                prop="is_minor"
                class="form-item-third"
                required
              >
                <el-select
                  v-model="formData.is_minor"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option label="是" value="yes" />
                  <el-option label="否" value="no" />
                </el-select>
              </el-form-item>
            </div>

            <!-- 监护人信息（仅当选择未成年时显示） -->
            <div v-if="formData.is_minor === 'yes'" class="form-row">
              <el-form-item
                label="家长监护权"
                prop="guardian_type"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.guardian_type"
                  placeholder="请选择监护权类型"
                  style="width: 100%"
                >
                  <el-option label="法定监护人" value="LEGAL_GUARDIAN" />
                  <el-option label="父亲" value="FATHER" />
                  <el-option label="母亲" value="MOTHER" />
                </el-select>
              </el-form-item>
            </div>

            <div v-if="formData.is_minor === 'yes'" class="form-row">
              <el-form-item
                label="姓-监护人"
                prop="guardian_surname"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.guardian_surname"
                  placeholder="请输入监护人姓氏"
                />
              </el-form-item>
              <el-form-item
                label="名-监护人"
                prop="guardian_given_name"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.guardian_given_name"
                  placeholder="请输入监护人名字"
                />
              </el-form-item>
            </div>

            <div v-if="formData.is_minor === 'yes'" class="form-row">
              <el-form-item
                label="地址-监护人"
                prop="guardian_address"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.guardian_address"
                  placeholder="请输入监护人地址"
                  type="textarea"
                  :rows="2"
                />
              </el-form-item>
              <el-form-item
                label="国籍-监护人"
                prop="guardian_nationality"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.guardian_nationality"
                  placeholder="请选择监护人国籍"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in countries"
                    :key="country.isoCode"
                    :label="country.nationalityName"
                    :value="country.isoCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 护照详细信息 -->
            <div class="form-row">
              <el-form-item
                label="护照号码"
                prop="passport_number_detail"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.passport_number_detail"
                  placeholder="请输入护照号码"
                />
              </el-form-item>
              <el-form-item
                label="护照类型"
                prop="passport_type"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.passport_type"
                  placeholder="请选择护照类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="type in passportTypeOptions"
                    :key="type.masterCode"
                    :label="type.displayName_cn"
                    :value="type.masterCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 护照签发信息 -->
            <div class="form-row">
              <el-form-item
                label="护照签发日期"
                prop="passport_issue_date_detail"
                class="form-item-half"
                required
              >
                <el-date-picker
                  v-model="formData.passport_issue_date_detail"
                  type="date"
                  placeholder="选择护照签发日期"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item
                label="签发国"
                prop="issuing_country"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.issuing_country"
                  placeholder="请选择签发国"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in countries"
                    :key="country.isoCode"
                    :label="country.nationalityName"
                    :value="country.isoCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 护照有效期和签发机关 -->
            <div class="form-row">
              <el-form-item
                label="护照有效期"
                prop="passport_expire_date_detail"
                class="form-item-half"
                required
              >
                <el-date-picker
                  v-model="formData.passport_expire_date_detail"
                  type="date"
                  placeholder="选择护照有效期"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item
                label="签发机关(拼音)"
                prop="issuing_authority"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.issuing_authority"
                  placeholder="请输入签发机关拼音，如：SHANGHAI"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 步骤3: 申请信息 -->
        <div v-if="currentStep === 2" class="step-panel">
          <h3 class="step-title">第三步：申请人信息</h3>
          <el-form
            ref="step3FormRef"
            :model="formData"
            label-width="160px"
            class="step-form"
          >
            <!-- 基本申请信息 -->
            <div class="form-row">
              <el-form-item
                label="申请者居住国"
                prop="applicant_country"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.applicant_country"
                  placeholder="请选择居住国"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in countries"
                    :key="country.isoCode"
                    :label="country.nationalityName"
                    :value="country.isoCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="申请者电子邮箱"
                prop="applicant_email"
                class="form-item-half"
                required
              >
                <el-input
                  v-model="formData.applicant_email"
                  placeholder="请输入电子邮箱"
                  type="email"
                />
              </el-form-item>
            </div>

            <!-- 住址信息 -->
            <div class="form-row">
              <el-form-item
                label="申请者住址"
                prop="applicant_address"
                required
              >
                <el-input
                  v-model="formData.applicant_address"
                  placeholder="请输入住址（拼音或英文，最多100字符）"
                  type="textarea"
                  :rows="2"
                  :maxlength="100"
                  show-word-limit
                  @input="handleAddressInput"
                />
              </el-form-item>
            </div>

            <!-- 联系方式 -->
            <div class="form-row">
              <el-form-item label="申请者电话" prop="applicant_phone" required>
                <div class="phone-input-group">
                  <div class="phone-code-input">
                    <span class="phone-prefix">+</span>
                    <el-input
                      v-model="formData.applicant_phone_code"
                      placeholder="区号"
                      style="width: 100px"
                    />
                  </div>
                  <el-input
                    v-model="formData.applicant_phone"
                    placeholder="请输入电话号码"
                    style="flex: 1; margin-left: 8px"
                  />
                </div>
              </el-form-item>
            </div>

            <!-- 职业信息 -->
            <div class="form-row">
              <el-form-item label="当前职业" prop="occupation_id" required>
                <el-select
                  v-model="formData.occupation_id"
                  placeholder="请选择职业"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="occupation in occupationOptions"
                    :key="occupation.masterCode"
                    :label="occupation.displayName"
                    :value="occupation.masterCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 职业其他信息 -->
            <div v-if="formData.occupation_id === 'OTR'" class="form-row">
              <el-form-item
                label="职业其他信息"
                prop="occupation_others"
                required
              >
                <el-input
                  v-model="formData.occupation_others"
                  placeholder="请详细描述您的职业"
                />
              </el-form-item>
            </div>

            <!-- 工作单位信息 -->
            <div
              v-if="
                formData.occupation_id &&
                formData.occupation_id !== 'NOO' &&
                formData.occupation_id !== 'NTA'
              "
              class="work-info-section"
            >
              <h4 class="section-title">工作单位信息</h4>

              <div class="form-row">
                <el-form-item
                  label="工作单位名称"
                  prop="employer_name"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.employer_name"
                    placeholder="请输入工作单位名称"
                  />
                </el-form-item>
                <el-form-item
                  label="工作单位电话"
                  prop="employer_phone"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.employer_phone"
                    placeholder="请输入工作单位电话"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item
                  label="工作单位地址"
                  prop="employer_address"
                  required
                >
                  <el-input
                    v-model="formData.employer_address"
                    placeholder="请输入工作单位地址"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item
                  label="雇主/教育机构城市"
                  prop="employer_city"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.employer_city"
                    placeholder="请输入城市"
                  />
                </el-form-item>
                <el-form-item
                  label="雇主/教育机构邮政编码"
                  prop="employer_postal_code"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.employer_postal_code"
                    placeholder="请输入邮政编码"
                  />
                </el-form-item>
                <el-form-item
                  label="雇主/教育机构国家"
                  prop="employer_country"
                  class="form-item-third"
                  required
                >
                  <el-select
                    v-model="formData.employer_country"
                    placeholder="请选择国家"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>

            <!-- 居住情况 -->
            <div class="form-row">
              <el-form-item
                label="是否居住在现国籍以外的国家"
                prop="residence_other_nationality"
                required
              >
                <el-radio-group v-model="formData.residence_other_nationality">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="2">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 居留许可信息 -->
            <div
              v-if="formData.residence_other_nationality === 1"
              class="residence-permit-section"
            >
              <h4 class="section-title">居留许可信息</h4>

              <div class="form-row">
                <el-form-item
                  label="居留国许可编号"
                  prop="residence_permit_no"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.residence_permit_no"
                    placeholder="请输入居留国许可编号"
                  />
                </el-form-item>
                <el-form-item
                  label="居留国许可结束日期"
                  prop="residence_permit_valid_until"
                  class="form-item-half"
                  required
                >
                  <el-date-picker
                    v-model="formData.residence_permit_valid_until"
                    type="date"
                    placeholder="选择结束日期"
                    format="DD/MM/YYYY"
                    value-format="DD/MM/YYYY"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
            </div>

            <!-- 指纹采集情况 -->
            <div class="form-row">
              <el-form-item
                label="之前是否采集过指纹"
                prop="fingerprints_collected"
                required
              >
                <el-radio-group v-model="formData.fingerprints_collected">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="2">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 指纹采集信息 -->
            <div
              v-if="formData.fingerprints_collected === 1"
              class="fingerprint-section"
            >
              <h4 class="section-title">指纹采集信息</h4>

              <div class="form-row">
                <el-form-item
                  label="上次指纹采集日期"
                  prop="date_of_collection"
                  class="form-item-half"
                  required
                >
                  <el-date-picker
                    v-model="formData.date_of_collection"
                    type="date"
                    placeholder="选择采集日期"
                    format="DD/MM/YYYY"
                    value-format="DD/MM/YYYY"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="上次指纹采集时的签证贴纸号码"
                  prop="previous_application_number"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.previous_application_number"
                    placeholder="请输入签证贴纸号码"
                  />
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 步骤4: 旅行信息 -->
        <div v-if="currentStep === 3" class="step-panel">
          <h3 class="step-title">第四步：旅行计划信息</h3>
          <el-form
            ref="step4FormRef"
            :model="formData"
            label-width="160px"
            class="step-form"
          >
            <!-- 旅行目的 -->
            <div class="form-row">
              <el-form-item label="旅行目的" prop="purpose_of_travel" required>
                <el-select
                  v-model="formData.purpose_of_travel"
                  placeholder="请选择旅行目的"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="purpose in travelPurposeOptions"
                    :key="purpose.masterCode"
                    :label="purpose.displayName"
                    :value="purpose.masterCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 停留目的补充信息 -->
            <div class="form-row">
              <el-form-item
                label="停留目的补充信息"
                prop="purpose_of_travel_add_info"
                required
              >
                <el-input
                  v-model="formData.purpose_of_travel_add_info"
                  placeholder="请详细描述您的停留目的"
                  type="textarea"
                  :rows="3"
                />
              </el-form-item>
            </div>

            <!-- 申请入境次数 -->
            <div class="form-row">
              <el-form-item
                label="申请入境次数"
                prop="number_of_entries"
                required
                class="form-item-half"
              >
                <el-select
                  v-model="formData.number_of_entries"
                  placeholder="请选择入境次数"
                  style="width: 100%; min-width: 200px"
                >
                  <el-option
                    v-for="entry in entryOptions"
                    :key="entry.masterCode"
                    :label="entry.displayName"
                    :value="entry.masterCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 申根签证情况 -->
            <div class="form-row">
              <el-form-item
                label="有无过去三年签发的申根签证"
                prop="is_schengen_visa_issued"
                required
              >
                <el-radio-group v-model="formData.is_schengen_visa_issued">
                  <el-radio :label="1">有</el-radio>
                  <el-radio :label="2">无</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 申根签证有效期信息 -->
            <div
              v-if="formData.is_schengen_visa_issued === 1"
              class="schengen-visa-section"
            >
              <h4 class="section-title">申根签证有效期信息</h4>

              <div class="form-row">
                <el-form-item
                  label="有效期开始日期"
                  prop="valid_from"
                  class="form-item-half"
                  required
                >
                  <el-date-picker
                    v-model="formData.valid_from"
                    type="date"
                    placeholder="选择开始日期"
                    format="DD/MM/YYYY"
                    value-format="DD/MM/YYYY"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="有效期结束日期"
                  prop="valid_till"
                  class="form-item-half"
                  required
                >
                  <el-date-picker
                    v-model="formData.valid_till"
                    type="date"
                    placeholder="选择结束日期"
                    format="DD/MM/YYYY"
                    value-format="DD/MM/YYYY"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
            </div>

            <!-- 旅行保险 -->
            <div class="form-row">
              <el-form-item
                label="旅行保险"
                prop="is_adequate_medical_insurance"
                required
              >
                <el-radio-group
                  v-model="formData.is_adequate_medical_insurance"
                >
                  <el-radio :label="1">有</el-radio>
                  <el-radio :label="2">无</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 步骤5: 住宿信息 -->
        <div v-if="currentStep === 4" class="step-panel">
          <h3 class="step-title">第五步：住宿安排信息</h3>
          <el-form
            ref="step5FormRef"
            :model="formData"
            label-width="160px"
            class="step-form"
          >
            <!-- 邀请方类型 -->
            <div class="form-row">
              <el-form-item
                label="邀请方类型"
                prop="inviting_party_type"
                required
              >
                <el-select
                  v-model="formData.inviting_party_type"
                  placeholder="请选择邀请方类型"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="type in invitingPartyOptions"
                    :key="type.masterCode"
                    :label="type.displayName"
                    :value="type.masterCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 酒店信息 -->
            <div
              v-if="formData.inviting_party_type === 'Hotel'"
              class="accommodation-section"
            >
              <h4 class="section-title">酒店信息</h4>

              <div class="form-row">
                <el-form-item
                  label="酒店名称"
                  prop="hotel_name"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.hotel_name"
                    placeholder="请输入酒店名称"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="酒店地址" prop="hotel_address" required>
                  <el-input
                    v-model="formData.hotel_address"
                    placeholder="请输入酒店地址"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </div>

              <!-- 街道单独一行 -->
              <div class="form-row">
                <el-form-item label="酒店街道" prop="hotel_street" required>
                  <el-input
                    v-model="formData.hotel_street"
                    placeholder="请输入街道"
                  />
                </el-form-item>
              </div>

              <!-- 国家、城市、邮编在一行平分宽度 -->
              <div class="form-row">
                <el-form-item
                  label="国家"
                  prop="hotel_country"
                  class="form-item-third"
                  required
                >
                  <el-select
                    v-model="formData.hotel_country"
                    placeholder="请选择国家"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="城市"
                  prop="hotel_city"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.hotel_city"
                    placeholder="请输入城市"
                  />
                </el-form-item>
                <el-form-item
                  label="邮编"
                  prop="hotel_postal_code"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.hotel_postal_code"
                    placeholder="请输入邮编"
                  />
                </el-form-item>
              </div>

              <!-- 电子邮箱单独一行 -->
              <div class="form-row">
                <el-form-item label="酒店邮箱" prop="hotel_email" required>
                  <el-input
                    v-model="formData.hotel_email"
                    placeholder="请输入酒店邮箱"
                    type="email"
                  />
                </el-form-item>
              </div>

              <!-- 电话单独一行 -->
              <div class="form-row">
                <el-form-item label="电话" prop="hotel_phone">
                  <div class="phone-input-group">
                    <div class="phone-code-input">
                      <span class="phone-prefix">+</span>
                      <el-input
                        v-model="formData.hotel_phone_code"
                        placeholder="区号"
                        style="width: 100px"
                        @input="handlePhoneInput($event, 'hotel_phone_code')"
                      />
                    </div>
                    <el-input
                      v-model="formData.hotel_phone"
                      placeholder="请输入电话号码"
                      style="flex: 1; margin-left: 8px"
                      @input="handlePhoneInput($event, 'hotel_phone')"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- 邀请人信息 -->
            <div
              v-if="formData.inviting_party_type === 'InvitingPerson'"
              class="accommodation-section"
            >
              <h4 class="section-title">邀请人信息</h4>

              <div class="form-row">
                <el-form-item
                  label="邀请人姓"
                  prop="inviting_person_surname"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.inviting_person_surname"
                    placeholder="请输入邀请人姓"
                  />
                </el-form-item>
                <el-form-item
                  label="邀请人名"
                  prop="inviting_person_given_name"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.inviting_person_given_name"
                    placeholder="请输入邀请人名"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item
                  label="邀请人地址"
                  prop="inviting_person_address"
                  required
                >
                  <el-input
                    v-model="formData.inviting_person_address"
                    placeholder="请输入邀请人地址"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </div>

              <!-- 街道单独一行 -->
              <div class="form-row">
                <el-form-item
                  label="街道"
                  prop="inviting_person_street"
                  required
                >
                  <el-input
                    v-model="formData.inviting_person_street"
                    placeholder="请输入街道"
                  />
                </el-form-item>
              </div>

              <!-- 国家、城市、邮编在一行平分宽度 -->
              <div class="form-row">
                <el-form-item
                  label="国家"
                  prop="inviting_person_country"
                  class="form-item-third"
                  required
                >
                  <el-select
                    v-model="formData.inviting_person_country"
                    placeholder="请选择国家"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="城市"
                  prop="inviting_person_city"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.inviting_person_city"
                    placeholder="请输入城市"
                  />
                </el-form-item>
                <el-form-item
                  label="邮编"
                  prop="inviting_person_postal_code"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.inviting_person_postal_code"
                    placeholder="请输入邮编"
                  />
                </el-form-item>
              </div>

              <!-- 电子邮箱单独一行 -->
              <div class="form-row">
                <el-form-item
                  label="电子邮箱"
                  prop="inviting_person_email"
                  required
                >
                  <el-input
                    v-model="formData.inviting_person_email"
                    placeholder="请输入电子邮箱"
                    type="email"
                    @input="
                      formData.inviting_person_email = filterEmailCharacters(
                        formData.inviting_person_email
                      )
                    "
                  />
                </el-form-item>
              </div>

              <!-- 电话单独一行 -->
              <div class="form-row">
                <el-form-item
                  label="电话"
                  prop="inviting_person_phone"
                  required
                >
                  <div class="phone-input-group">
                    <div class="phone-code-input">
                      <span class="phone-prefix">+</span>
                      <el-input
                        v-model="formData.inviting_person_phone_code"
                        placeholder="区号"
                        style="width: 100px"
                        @input="handlePhoneInput($event, 'inviting_person_phone_code')"
                      />
                    </div>
                    <el-input
                      v-model="formData.inviting_person_phone"
                      placeholder="请输入电话号码"
                      style="flex: 1; margin-left: 8px"
                      @input="handlePhoneInput($event, 'inviting_person_phone')"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- 邀请企业信息 -->
            <div
              v-if="formData.inviting_party_type === 'Invitingenterprise'"
              class="accommodation-section"
            >
              <h4 class="section-title">邀请企业信息</h4>

              <div class="form-row">
                <el-form-item
                  label="邀请机构名称"
                  prop="enterprise_name"
                  required
                >
                  <el-input
                    v-model="formData.enterprise_name"
                    placeholder="请输入邀请机构名称"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item
                  label="邀请机构地址"
                  prop="enterprise_address"
                  required
                >
                  <el-input
                    v-model="formData.enterprise_address"
                    placeholder="请输入邀请机构地址"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </div>

              <!-- 街道单独一行 -->
              <div class="form-row">
                <el-form-item label="街道" prop="enterprise_street" required>
                  <el-input
                    v-model="formData.enterprise_street"
                    placeholder="请输入街道"
                  />
                </el-form-item>
              </div>

              <!-- 国家、城市、邮编在一行平分宽度 -->
              <div class="form-row">
                <el-form-item
                  label="国家"
                  prop="enterprise_country"
                  class="form-item-third"
                  required
                >
                  <el-select
                    v-model="formData.enterprise_country"
                    placeholder="请选择国家"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="城市"
                  prop="enterprise_city"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.enterprise_city"
                    placeholder="请输入城市"
                  />
                </el-form-item>
                <el-form-item
                  label="邮编"
                  prop="enterprise_postal_code"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.enterprise_postal_code"
                    placeholder="请输入邮编"
                  />
                </el-form-item>
              </div>

              <!-- 电子邮箱单独一行 -->
              <div class="form-row">
                <el-form-item label="邮箱" prop="enterprise_email" required>
                  <el-input
                    v-model="formData.enterprise_email"
                    placeholder="请输入邮箱"
                    type="email"
                  />
                </el-form-item>
              </div>

              <!-- 电话单独一行 -->
              <div class="form-row">
                <el-form-item label="电话" prop="enterprise_phone">
                  <div class="phone-input-group">
                    <div class="phone-code-input">
                      <span class="phone-prefix">+</span>
                      <el-input
                        v-model="formData.enterprise_phone_code"
                        placeholder="区号"
                        style="width: 100px"
                        @input="handlePhoneInput($event, 'enterprise_phone_code')"
                      />
                    </div>
                    <el-input
                      v-model="formData.enterprise_phone"
                      placeholder="请输入电话号码"
                      style="flex: 1; margin-left: 8px"
                      @input="handlePhoneInput($event, 'enterprise_phone')"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- 邀请机构信息 -->
            <div
              v-if="formData.inviting_party_type === 'InvitingOrganisation'"
              class="accommodation-section"
            >
              <h4 class="section-title">邀请机构信息</h4>

              <div class="form-row">
                <el-form-item
                  label="邀请机构名称"
                  prop="organisation_name"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.organisation_name"
                    placeholder="请输入邀请机构名称"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item
                  label="邀请机构地址"
                  prop="organisation_address"
                  required
                >
                  <el-input
                    v-model="formData.organisation_address"
                    placeholder="请输入邀请机构地址"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </div>

              <!-- 街道单独一行 -->
              <div class="form-row">
                <el-form-item label="街道" prop="organisation_street" required>
                  <el-input
                    v-model="formData.organisation_street"
                    placeholder="请输入街道"
                  />
                </el-form-item>
              </div>

              <!-- 国家、城市、邮编在一行平分宽度 -->
              <div class="form-row">
                <el-form-item
                  label="国家"
                  prop="organisation_country"
                  class="form-item-third"
                  required
                >
                  <el-select
                    v-model="formData.organisation_country"
                    placeholder="请选择国家"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="城市"
                  prop="organisation_city"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.organisation_city"
                    placeholder="请输入城市"
                  />
                </el-form-item>
                <el-form-item
                  label="邮编"
                  prop="organisation_postal_code"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.organisation_postal_code"
                    placeholder="请输入邮编"
                  />
                </el-form-item>
              </div>

              <!-- 电子邮箱单独一行 -->
              <div class="form-row">
                <el-form-item label="邮箱" prop="organisation_email" required>
                  <el-input
                    v-model="formData.organisation_email"
                    placeholder="请输入邮箱"
                    type="email"
                  />
                </el-form-item>
              </div>

              <!-- 电话单独一行 -->
              <div class="form-row">
                <el-form-item label="电话" prop="organisation_phone">
                  <div class="phone-input-group">
                    <div class="phone-code-input">
                      <span class="phone-prefix">+</span>
                      <el-input
                        v-model="formData.organisation_phone_code"
                        placeholder="区号"
                        style="width: 100px"
                        @input="handlePhoneInput($event, 'organisation_phone_code')"
                      />
                    </div>
                    <el-input
                      v-model="formData.organisation_phone"
                      placeholder="请输入电话号码"
                      style="flex: 1; margin-left: 8px"
                      @input="handlePhoneInput($event, 'organisation_phone')"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- 临时住宿信息 -->
            <div
              v-if="formData.inviting_party_type === 'Temporary accommodation'"
              class="accommodation-section"
            >
              <h4 class="section-title">临时住宿信息</h4>

              <div class="form-row">
                <el-form-item
                  label="住宿地址"
                  prop="temp_accommodation_address"
                  required
                >
                  <el-input
                    v-model="formData.temp_accommodation_address"
                    placeholder="请输入住宿地址"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </div>

              <!-- 街道单独一行 -->
              <div class="form-row">
                <el-form-item
                  label="街道"
                  prop="temp_accommodation_street"
                  required
                >
                  <el-input
                    v-model="formData.temp_accommodation_street"
                    placeholder="请输入街道"
                  />
                </el-form-item>
              </div>

              <!-- 国家、城市、邮编在一行平分宽度 -->
              <div class="form-row">
                <el-form-item
                  label="国家"
                  prop="temp_accommodation_country"
                  class="form-item-third"
                  required
                >
                  <el-select
                    v-model="formData.temp_accommodation_country"
                    placeholder="请选择国家"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="城市"
                  prop="temp_accommodation_city"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.temp_accommodation_city"
                    placeholder="请输入城市"
                  />
                </el-form-item>
                <el-form-item
                  label="邮编"
                  prop="temp_accommodation_postal_code"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.temp_accommodation_postal_code"
                    placeholder="请输入邮编"
                  />
                </el-form-item>
              </div>

              <!-- 电子邮箱单独一行 -->
              <div class="form-row">
                <el-form-item
                  label="电子邮箱"
                  prop="temp_accommodation_email"
                  required
                >
                  <el-input
                    v-model="formData.temp_accommodation_email"
                    placeholder="请输入电子邮箱"
                    type="email"
                  />
                </el-form-item>
              </div>

              <!-- 电话单独一行 -->
              <div class="form-row">
                <el-form-item label="电话" prop="temp_accommodation_phone">
                  <div class="phone-input-group">
                    <div class="phone-code-input">
                      <span class="phone-prefix">+</span>
                      <el-input
                        v-model="formData.temp_accommodation_phone_code"
                        placeholder="区号"
                        style="width: 100px"
                        @input="handlePhoneInput($event, 'temp_accommodation_phone_code')"
                      />
                    </div>
                    <el-input
                      v-model="formData.temp_accommodation_phone"
                      placeholder="电话号码"
                      style="flex: 1; margin-left: 8px"
                      @input="handlePhoneInput($event, 'temp_accommodation_phone')"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- 瑞士居留规定信息 -->
            <div
              v-if="
                formData.inviting_party_type === 'RegulationsstayinSwitzerland'
              "
              class="accommodation-section"
            >
              <h4 class="section-title">瑞士居留规定信息</h4>

              <div class="form-row">
                <el-form-item
                  label="住宿地址"
                  prop="regulations_address"
                  required
                >
                  <el-input
                    v-model="formData.regulations_address"
                    placeholder="请输入住宿地址"
                    type="textarea"
                    :rows="2"
                  />
                </el-form-item>
              </div>

              <!-- 街道单独一行 -->
              <div class="form-row">
                <el-form-item label="街道" prop="regulations_street" required>
                  <el-input
                    v-model="formData.regulations_street"
                    placeholder="请输入街道"
                  />
                </el-form-item>
              </div>

              <!-- 国家、城市、邮编在一行平分宽度 -->
              <div class="form-row">
                <el-form-item
                  label="国家"
                  prop="regulations_country"
                  class="form-item-third"
                  required
                >
                  <el-select
                    v-model="formData.regulations_country"
                    placeholder="请选择国家"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="城市"
                  prop="regulations_city"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.regulations_city"
                    placeholder="请输入城市"
                  />
                </el-form-item>
                <el-form-item
                  label="邮编"
                  prop="regulations_postal_code"
                  class="form-item-third"
                  required
                >
                  <el-input
                    v-model="formData.regulations_postal_code"
                    placeholder="请输入邮编"
                  />
                </el-form-item>
              </div>

              <!-- 电子邮箱单独一行 -->
              <div class="form-row">
                <el-form-item
                  label="电子邮箱"
                  prop="regulations_email"
                  required
                >
                  <el-input
                    v-model="formData.regulations_email"
                    placeholder="请输入电子邮箱"
                    type="email"
                  />
                </el-form-item>
              </div>

              <!-- 电话单独一行 -->
              <div class="form-row">
                <el-form-item label="电话" prop="regulations_phone">
                  <div class="phone-input-group">
                    <div class="phone-code-input">
                      <span class="phone-prefix">+</span>
                      <el-input
                        v-model="formData.regulations_phone_code"
                        placeholder="区号"
                        style="width: 100px"
                        @input="handlePhoneInput($event, 'regulations_phone_code')"
                      />
                    </div>
                    <el-input
                      v-model="formData.regulations_phone"
                      placeholder="电话号码"
                      style="flex: 1; margin-left: 8px"
                      @input="handlePhoneInput($event, 'regulations_phone')"
                    />
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>

        <!-- 步骤6: 附加信息 -->
        <div v-if="currentStep === 5" class="step-panel">
          <h3 class="step-title">第六步：附加信息及确认</h3>
          <el-form
            ref="step6FormRef"
            :model="formData"
            label-width="200px"
            class="step-form"
          >
            <!-- 过境签证 -->
            <div class="form-row">
              <el-form-item
                label="选择过境签证"
                prop="is_transit_visa"
                required
              >
                <el-radio-group v-model="formData.is_transit_visa">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="2">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 申根区日期 -->
            <div class="form-row">
              <el-form-item
                label="到达申根区日期"
                prop="arrival_date"
                class="form-item-half"
                required
              >
                <el-date-picker
                  v-model="formData.arrival_date"
                  type="date"
                  placeholder="选择到达日期"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  style="width: 100%"
                  @change="calculateStayDuration"
                />
              </el-form-item>
              <el-form-item
                label="离开申根区日期"
                prop="departure_date"
                class="form-item-half"
                required
              >
                <el-date-picker
                  v-model="formData.departure_date"
                  type="date"
                  placeholder="选择离开日期"
                  format="DD/MM/YYYY"
                  value-format="DD/MM/YYYY"
                  style="width: 100%"
                  @change="calculateStayDuration"
                />
              </el-form-item>
            </div>

            <!-- 停留时间（自动计算） -->
            <div class="form-row">
              <el-form-item label="停留时间（天）" prop="duration_of_stay">
                <el-input
                  v-model="formData.duration_of_stay"
                  placeholder="自动计算"
                  readonly
                  style="width: 200px"
                />
              </el-form-item>
            </div>

            <!-- 费用承担方 -->
            <div class="form-row">
              <el-form-item
                label="出行和居住费用的承担方"
                prop="cost_covered_by"
                required
              >
                <el-checkbox-group v-model="formData.cost_covered_by">
                  <el-checkbox
                    v-for="option in costCoveredByOptions"
                    :key="option.masterCode"
                    :label="option.masterCode"
                  >
                    {{ option.displayName }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>

            <!-- 出行和居住费用-其他（始终显示，必填） -->
            <div class="form-row">
              <el-form-item
                label="出行和居住费用-其他"
                prop="cost_covered_by_others"
                required
              >
                <el-input
                  v-model="formData.cost_covered_by_others"
                  placeholder="请详细说明其他费用承担方"
                />
              </el-form-item>
            </div>

            <!-- 申请者的支付方式 -->
            <div class="form-row">
              <el-form-item
                label="申请者的支付方式"
                prop="means_of_support"
                required
              >
                <el-checkbox-group v-model="formData.means_of_support">
                  <el-checkbox
                    v-for="option in meansOfSupportOptions"
                    :key="option.masterCode"
                    :label="option.masterCode"
                  >
                    {{ option.displayName }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>

            <!-- 支付方式-其他（始终显示，必填） -->
            <div class="form-row">
              <el-form-item
                label="申请人支付方式-其他"
                prop="means_of_support_others"
                required
              >
                <el-input
                  v-model="formData.means_of_support_others"
                  placeholder="请详细说明其他支付方式"
                />
              </el-form-item>
            </div>

            <!-- 欧盟公民家庭成员 -->
            <div class="form-row">
              <el-form-item
                label="有无是欧盟公民的家庭成员"
                prop="is_eu_citizen_family"
                required
              >
                <el-radio-group v-model="formData.is_eu_citizen_family">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="2">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 欧盟公民信息 -->
            <div
              v-if="formData.is_eu_citizen_family === 1"
              class="accommodation-section"
            >
              <h4 class="section-title">欧盟公民信息</h4>

              <div class="form-row">
                <el-form-item
                  label="欧盟公民姓"
                  prop="eu_citizen_surname"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.eu_citizen_surname"
                    placeholder="请输入欧盟公民姓"
                  />
                </el-form-item>
                <el-form-item
                  label="欧盟公民名"
                  prop="eu_citizen_given_name"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.eu_citizen_given_name"
                    placeholder="请输入欧盟公民名"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item
                  label="欧盟公民国籍"
                  prop="eu_citizen_nationality"
                  class="form-item-half"
                  required
                >
                  <el-select
                    v-model="formData.eu_citizen_nationality"
                    placeholder="请选择国籍"
                    style="width: 100%"
                    filterable
                  >
                    <el-option
                      v-for="country in countries"
                      :key="country.isoCode"
                      :label="country.nationalityName"
                      :value="country.isoCode"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="欧盟公民出生日期"
                  prop="eu_citizen_birth_date"
                  class="form-item-half"
                  required
                >
                  <el-date-picker
                    v-model="formData.eu_citizen_birth_date"
                    type="date"
                    placeholder="选择出生日期"
                    format="DD/MM/YYYY"
                    value-format="DD/MM/YYYY"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item
                  label="欧盟公民护照号码"
                  prop="eu_citizen_passport_number"
                  class="form-item-half"
                  required
                >
                  <el-input
                    v-model="formData.eu_citizen_passport_number"
                    placeholder="请输入护照号码"
                  />
                </el-form-item>
                <el-form-item
                  label="与欧盟公民的关系"
                  prop="eu_citizen_relationship"
                  class="form-item-half"
                  required
                >
                  <el-select
                    v-model="formData.eu_citizen_relationship"
                    placeholder="请选择关系"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="relation in euRelationshipOptions"
                      :key="relation.masterCode"
                      :label="relation.displayName"
                      :value="relation.masterCode"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>

            <!-- 申根国家信息 -->
            <div class="form-row">
              <el-form-item
                label="首入申根国"
                prop="first_entry_schengen"
                class="form-item-half"
                required
              >
                <el-select
                  v-model="formData.first_entry_schengen"
                  placeholder="请选择首入申根国"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="country in schengenCountryOptions"
                    :key="country.masterCode"
                    :label="country.displayName"
                    :value="country.masterCode"
                  />
                </el-select>
              </el-form-item>
            </div>

            <!-- 目的地国家 -->
            <div class="form-row">
              <el-form-item
                label="目的地国家"
                prop="destination_countries"
                required
              >
                <el-checkbox-group v-model="formData.destination_countries">
                  <div class="checkbox-grid">
                    <el-checkbox
                      v-for="country in schengenCountryOptions"
                      :key="country.masterCode"
                      :label="country.masterCode"
                      class="checkbox-item"
                    >
                      {{ country.displayName }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </el-form-item>
            </div>

            <!-- 费用由邀请人或邀请公司支付（移到最下方） -->
            <div class="form-row">
              <el-form-item
                label="费用由邀请人或邀请公司支付"
                prop="inviting_person_covered_costs"
                required
              >
                <el-radio-group
                  v-model="formData.inviting_person_covered_costs"
                >
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="2">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

            <!-- 费用承担方输入框（当邀请人不支付费用时显示，放在费用由邀请人支付下面） -->
            <div
              v-if="formData.inviting_person_covered_costs === 2"
              class="form-row"
            >
              <el-form-item label="费用承担方" prop="costsCoveredBy" required>
                <el-input
                  v-model="formData.costsCoveredBy"
                  placeholder="请输入费用承担方"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 其他步骤暂时保留原有内容 -->
        <div v-if="currentStep >= 6" class="step-panel">
          <h3 class="step-title">功能开发中...</h3>
          <p>步骤 {{ currentStep + 1 }} 正在开发中，敬请期待。</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <div class="left-actions">
          <el-button
            @click="handlePrevious"
            :disabled="currentStep === 0"
            class="prev-button"
            title="快捷键：Alt + ←"
          >
            ← 上一步
          </el-button>
          <div class="keyboard-hint">
            <span class="hint-text">快捷键：Alt + ← / →</span>
          </div>
        </div>
        <div class="right-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button @click="handleSaveDraft">保存草稿</el-button>
          <el-button
            type="primary"
            @click="handleNext"
            :loading="submitting"
            class="next-button"
            title="快捷键：Alt + →"
          >
            {{ currentStep === 5 ? '提交表单' : '下一步 →' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
// 图标导入已移除，当前未使用
import http from '@/utils/http';
import countriesData from '@/assets/country.json';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
  isEditing: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['save', 'cancel']);

// 响应式数据
const step1FormRef = ref();
const step2FormRef = ref();
const submitting = ref(false);
const uploading = ref(false);
const currentStep = ref(0);

// 国家列表数据
const countries = ref(countriesData || []);

// 表单数据
const formData = reactive({
  // 步骤1: 资格准则
  surname: '',
  given_name: '',
  gender: '',
  birth_date: '',
  nationality: 'CHN', // 默认中国
  passport_number: '',
  passport_expire_date: '',

  // 步骤2: 护照信息
  passport_surname: '',
  birth_surname: '',
  passport_given_name: '',
  passport_birth_date: '',
  birth_country: 'CHN', // 默认中国
  birth_place: '',
  current_nationality: 'CHN', // 默认中国
  birth_nationality: 'CHN', // 默认中国
  passport_gender: '',
  passport_marital_status: '',
  is_minor: 'no',
  // 监护人信息（未成年时需要）
  guardian_type: '',
  guardian_surname: '',
  guardian_given_name: '',
  guardian_address: '',
  guardian_nationality: 'CHN', // 默认中国
  passport_number_detail: '',
  passport_type: '',
  passport_issue_date_detail: '',
  issuing_country: 'CHN', // 默认中国
  passport_expire_date_detail: '',
  issuing_authority: '',

  // 步骤3: 申请信息
  applicant_country: 'CHN', // 默认中国
  applicant_address: '',
  occupation_id: '',
  occupation_others: '', // 职业其他信息
  applicant_email: '',
  applicant_phone_code: '86',
  applicant_phone: '',
  employer_name: '',
  employer_address: '',
  employer_phone: '',
  employer_city: '',
  employer_postal_code: '',
  employer_country: 'CHN', // 默认中国
  residence_other_nationality: 2, // 默认否
  residence_permit_no: '',
  residence_permit_valid_until: '',
  fingerprints_collected: 2, // 默认否
  date_of_collection: '',
  previous_application_number: '',

  // 步骤4: 旅行信息
  purpose_of_travel: '',
  purpose_of_travel_add_info: '',
  number_of_entries: '',
  is_schengen_visa_issued: 2, // 默认无
  valid_from: '',
  valid_till: '',
  is_adequate_medical_insurance: 2, // 默认无

  // 步骤5: 住宿信息
  inviting_party_type: '',
  // 酒店信息
  hotel_name: '',
  hotel_address: '',
  hotel_postal_code: '',
  hotel_email: '',
  hotel_street: '',
  hotel_city: '',
  hotel_country: 'CHN', // 默认中国
  hotel_phone_code: '86',
  hotel_phone: '',
  // 邀请人信息
  inviting_person_surname: '',
  inviting_person_given_name: '',
  inviting_person_address: '',
  inviting_person_postal_code: '',
  inviting_person_email: '',
  inviting_person_street: '',
  inviting_person_city: '',
  inviting_person_country: 'CHN', // 默认中国
  inviting_person_phone_code: '86',
  inviting_person_phone: '',
  // 邀请企业信息
  enterprise_name: '',
  enterprise_address: '',
  enterprise_postal_code: '',
  enterprise_email: '',
  enterprise_street: '',
  enterprise_city: '',
  enterprise_country: 'CHN', // 默认中国
  enterprise_phone_code: '86',
  enterprise_phone: '',
  // 邀请机构信息
  organisation_name: '',
  organisation_address: '',
  organisation_postal_code: '',
  organisation_email: '',
  organisation_street: '',
  organisation_city: '',
  organisation_country: 'CHN', // 默认中国
  organisation_phone_code: '86',
  organisation_phone: '',
  // 临时住宿信息
  temp_accommodation_address: '',
  temp_accommodation_street: '',
  temp_accommodation_city: '',
  temp_accommodation_postal_code: '',
  temp_accommodation_country: 'CHN', // 默认中国
  temp_accommodation_email: '',
  temp_accommodation_phone_code: '86',
  temp_accommodation_phone: '',
  // 瑞士居留规定信息
  regulations_address: '',
  regulations_street: '',
  regulations_city: '',
  regulations_postal_code: '',
  regulations_country: 'CHN', // 默认中国
  regulations_email: '',
  regulations_phone_code: '86',
  regulations_phone: '',

  // 步骤6: 附加信息
  is_transit_visa: 2, // 默认否
  arrival_date: '',
  departure_date: '',
  duration_of_stay: '',
  cost_covered_by: [],
  cost_covered_by_others: '',
  costsCoveredBy: '', // 新增的费用承担方输入框
  means_of_support: [],
  means_of_support_others: '',
  is_eu_citizen_family: 2, // 默认否
  eu_citizen_surname: '',
  eu_citizen_given_name: '',
  eu_citizen_nationality: '',
  eu_citizen_birth_date: '',
  eu_citizen_passport_number: '',
  eu_citizen_relationship: '',
  first_entry_schengen: '',
  destination_countries: [],
  inviting_person_covered_costs: 2, // 默认否

  // 其他步骤字段保留
  form_name: '',
  country: '',
  phone: '',
  email: '',
  address: '',
  travel_purpose: '',
  visa_type: '',
  entry_date: '',
  exit_date: '',
  travel_plan: '',
  remarks: '',
  status: 'draft',
});

// 计算属性 - 当前步骤数据（按照指定格式）
const currentStepData = computed(() => {
  if (currentStep.value === 0) {
    // 第一步：资格准则数据格式
    return {
      routeId: 1,
      applicationAlphId: '83f10eb0-7330-47ea-a554-8f071e0a0e90',
      applicantGroupAlphId: '4059d05c-8285-49ec-9c62-a762aadae69e',
      documentIdentifierId: null,
      flowSequence: [
        'eligibilityCriteria',
        'passportInformation',
        'applicantInformation',
        'travelInformation',
        'accommodationInformation',
        'vafInformation',
      ],
      webRefNo: 'KMSW30498278395/1',
      firstName: formData.given_name || '',
      lastName: formData.surname || '',
      genderId:
        formData.gender === 'M'
          ? 'MALE'
          : formData.gender === 'F'
          ? 'FEMALE'
          : '',
      dateOfBirth: parseDateToObject(formData.birth_date),
      nationalityId: formData.nationality || '',
      passportNumber: formData.passport_number || '',
      expiryDate: parseDateToObject(formData.passport_expire_date),
      isDraft: formData.status === 'draft',
      stage:
        currentStep.value === 0 ? 'eligibilityCriteria' : 'passportInformation',
    };
  } else if (currentStep.value === 1) {
    // 第二步：护照信息数据格式
    return {
      surName: formData.passport_surname || '',
      surnameAtBirth: formData.birth_surname || '',
      givenName: formData.passport_given_name || '',
      dateOfBirth: parseDateToObject(formData.passport_birth_date),
      countryOfBirth: formData.birth_country || '',
      placeOfBirth: formData.birth_place || '',
      nationalityId: formData.current_nationality || '',
      nationalityAtBirthId: formData.birth_nationality || '',
      genderId:
        formData.passport_gender === 'M'
          ? 'MALE'
          : formData.passport_gender === 'F'
          ? 'FEMALE'
          : '',
      maritalStatusId: formData.passport_marital_status || '',
      isMinorApplicant: formData.is_minor === 'yes' ? 1 : 2,
      IdNumber: formData.passport_number_detail || '',
      passportTypeId: formData.passport_type || '',
      passportNumber: formData.passport_number_detail || '',
      reenternumberOfPassport: formData.passport_number_detail || '',
      issueDate: parseDateToObject(formData.passport_issue_date_detail),
      issuedcountry: formData.issuing_country || '',
      expiryDate: parseDateToObject(formData.passport_expire_date_detail),
      issuedBy: formData.issuing_authority || '',
      isDraft: false,
      stage: 'applicantInformation',
      applicationAlphId: '83f10eb0-7330-47ea-a554-8f071e0a0e90',
    };
  } else if (currentStep.value === 2) {
    // 第三步：申请信息数据格式
    return {
      applicationDate: null,
      applicantCountry: formData.applicant_country || '',
      applicantAddress: formData.applicant_address || '',
      occupationId: formData.occupation_id || '',
      occupationOthers: formData.occupation_others || null,
      applicantEmail: formData.applicant_email || '',
      applicanttelephoneIsdCode: formatPhoneCode(formData.applicant_phone_code),
      applicanttelePhoneNumber: formData.applicant_phone || '',
      residenceOtherNationality: formData.residence_other_nationality || 2,
      residenceCountryPermitNo: formData.residence_permit_no || '',
      residenceCountryPermitValidUntil: parseDateToObject(
        formData.residence_permit_valid_until
      ),
      employerName: formData.employer_name || '',
      employerAddress: formData.employer_address || '',
      employerMobile: formData.employer_phone || '',
      employerCity: formData.employer_city || '',
      employerHomepostalCode: formData.employer_postal_code || '',
      employerHomecountry: formData.employer_country || '',
      employerNameNoOcc: null,
      employerAddressNoOcc: null,
      employerMobileNoOcc: null,
      employerCityNoOcc: null,
      employerHomepostalCodeNoOcc: null,
      employerHomecountryNoOcc: null,
      fingerprintsCollected: formData.fingerprints_collected || 2,
      dateOfCollection: parseDateToObject(formData.date_of_collection),
      previousApplicationNumber: formData.previous_application_number || '',
      isDraft: false,
      stage: 'travelInformation',
      applicantGroupAlphId: '4059d05c-8285-49ec-9c62-a762aadae69e',
      applicationAlphId: '83f10eb0-7330-47ea-a554-8f071e0a0e90',
    };
  } else if (currentStep.value === 3) {
    // 第四步：旅行信息数据格式
    return {
      purposeOfTravel: formData.purpose_of_travel || '',
      purposeOfTravelOthers: null,
      purposeOfTraveladdInfo: formData.purpose_of_travel_add_info || '',
      numberOfEntries: formData.number_of_entries || '',
      IsSchengenVisaIssued: formData.is_schengen_visa_issued || 2,
      valid_from: parseDateToObject(formData.valid_from),
      valid_till: parseDateToObject(formData.valid_till),
      isAdequateMedicalInsurance: formData.is_adequate_medical_insurance || 2,
      isDraft: false,
      stage: 'accommodationInformation',
      applicationAlphId: '83f10eb0-7330-47ea-a554-8f071e0a0e90',
    };
  } else if (currentStep.value === 4) {
    // 第五步：住宿信息数据格式
    const getInvitingPartyId = () => {
      const partyType = formData.inviting_party_type;
      const mapping = {
        InvitingPerson: 1,
        Invitingenterprise: 2,
        Hotel: 3,
        'Temporary accommodation': 4,
        InvitingOrganisation: 5,
        RegulationsstayinSwitzerland: 6,
      };
      return mapping[partyType] || null;
    };

    return {
      invitingPartyId: getInvitingPartyId(),
      // 邀请机构信息（企业和机构共用）
      nameOforganisation:
        formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_name || ''
          : null,
      addressOforganisation:
        formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_address || ''
          : null,
      nameOfenterprise:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_name || ''
          : null,
      addressOfenterprise:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_address || ''
          : null,
      postalcodeOfenterprise:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_postal_code || ''
          : formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_postal_code || ''
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formData.temp_accommodation_postal_code || ''
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formData.regulations_postal_code || ''
          : null,
      postofficeBoxenterprise: null,
      postofficeBoxnumberenterprise: null,
      emailenterprise:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_email || ''
          : formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_email || ''
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formData.temp_accommodation_email || ''
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formData.regulations_email || ''
          : null,
      streetenterprise:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_street || ''
          : formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_street || ''
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formData.temp_accommodation_street || ''
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formData.regulations_street || ''
          : null,
      cityenterprise:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_city || ''
          : formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_city || ''
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formData.temp_accommodation_city || ''
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formData.regulations_city || ''
          : null,
      countryenterprise:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_country || ''
          : formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_country || ''
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formData.temp_accommodation_country || ''
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formData.regulations_country || ''
          : null,
      enterprisetelephoneIsdCode:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formatPhoneCode(formData.enterprise_phone_code)
          : formData.inviting_party_type === 'InvitingOrganisation'
          ? formatPhoneCode(formData.organisation_phone_code)
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formatPhoneCode(formData.temp_accommodation_phone_code)
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formatPhoneCode(formData.regulations_phone_code)
          : null,
      enterprisetelePhoneNumber:
        formData.inviting_party_type === 'Invitingenterprise'
          ? formData.enterprise_phone || ''
          : formData.inviting_party_type === 'InvitingOrganisation'
          ? formData.organisation_phone || ''
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formData.temp_accommodation_phone || ''
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formData.regulations_phone || ''
          : null,
      faxNumberenterprise: null,
      surNameOfcontactpersonenterprise: null,
      firstNameOfcontactpersonenterprise: null,
      emailOfcontactpersonenterprise: null,
      // 邀请人信息
      surNameOfcontactinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_surname || ''
          : null,
      firstNameOfcontactinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_given_name || ''
          : null,
      addressOfinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_address || ''
          : null,
      postalcodeOfinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_postal_code || ''
          : null,
      postofficeBoxinvitingperson: null,
      postofficeBoxnumberinvitingperson: null,
      emailinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_email || ''
          : null,
      streetinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_street || ''
          : null,
      cityinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_city || ''
          : null,
      countryinvitingperson:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_country || ''
          : null,
      invitingpersontelephoneIsdCode:
        formData.inviting_party_type === 'InvitingPerson'
          ? formatPhoneCode(formData.inviting_person_phone_code)
          : null,
      invitingpersontelePhoneNumber:
        formData.inviting_party_type === 'InvitingPerson'
          ? formData.inviting_person_phone || ''
          : null,
      emailaddressOfcontactinvitingperson: null,
      // 酒店信息（包含临时住宿和瑞士居留规定的地址信息）
      nameOfinvitinghotel:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_name || ''
          : null,
      addressOfinvitinghotel:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_address || ''
          : formData.inviting_party_type === 'Temporary accommodation'
          ? formData.temp_accommodation_address || ''
          : formData.inviting_party_type === 'RegulationsstayinSwitzerland'
          ? formData.regulations_address || ''
          : null,
      postalcodeOfinvitinghotel:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_postal_code || ''
          : null,
      postofficeBoxinvitinghotel: null,
      postofficeBoxnumberinvitinghotel: null,
      emailinvitinghotel:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_email || ''
          : null,
      streetinvitinghotel:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_street || ''
          : null,
      cityinvitinghotel:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_city || ''
          : null,
      countryinvitinghotel:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_country || ''
          : null,
      invitinghoteltelephoneIsdCode:
        formData.inviting_party_type === 'Hotel'
          ? formatPhoneCode(formData.hotel_phone_code)
          : null,
      invotinghoteltelePhoneNumber:
        formData.inviting_party_type === 'Hotel'
          ? formData.hotel_phone || ''
          : null,
      faxNumberinvitinghotel: null,
      surNameOfcontactinvitinghotel: null,
      firstNameOfcontactinvitinghotel: null,
      emailOfcontactinvtinghotel: null,
      isDraft: false,
      stage: 'vafInformation',
      applicantGroupAlphId: '4059d05c-8285-49ec-9c62-a762aadae69e',
      applicationAlphId: '83f10eb0-7330-47ea-a554-8f071e0a0e90',
    };
  } else if (currentStep.value === 5) {
    // 第六步：附加信息数据格式
    return {
      finalDestination: formData.is_transit_visa || 2,
      IssuedBy: null,
      arrivalDate: parseDateToObject(formData.arrival_date),
      departureDate: parseDateToObject(formData.departure_date),
      durationOfStay: formData.duration_of_stay || '',
      costOfTravellingCoveredBy: formData.cost_covered_by || [],
      costOfTravellingCoveredByOthers: formData.cost_covered_by_others || '',
      costsCoveredBy: formData.costsCoveredBy || '', // 新增的费用承担方字段
      meansOfSupportId: formData.means_of_support || [],
      meansOfSupportOthers: formData.means_of_support_others || '',
      isCitizenId: formData.is_eu_citizen_family || 2,
      EuSurname: formData.eu_citizen_surname || '',
      EuFirstName: formData.eu_citizen_given_name || '',
      EuNationalityId: formData.eu_citizen_nationality || '',
      EuDateOfBirth: parseDateToObject(formData.eu_citizen_birth_date),
      EuPassportNumber: formData.eu_citizen_passport_number || '',
      EuRelationshipId: formData.eu_citizen_relationship || '',
      schengenStateFirstEntry: formData.first_entry_schengen || '',
      countryOfDestination: formData.destination_countries || [],
      invitingPersonCoveredCosts: formData.inviting_person_covered_costs || 2,
      isDraft: false,
      stage: 'ReceivedAtAC',
      applicationAlphId: '83f10eb0-7330-47ea-a554-8f071e0a0e90',
    };
  } else {
    return {
      step: currentStep.value + 1,
      message: '步骤开发中...',
    };
  }
});

// 日期解析函数 - 将DD/MM/YYYY格式转换为{year, month, day}对象
const parseDateToObject = dateStr => {
  if (!dateStr) return { year: null, month: null, day: null };

  try {
    const parts = dateStr.split('/');
    if (parts.length === 3) {
      return {
        year: parseInt(parts[2]),
        month: parseInt(parts[1]),
        day: parseInt(parts[0]),
      };
    }
  } catch (error) {
    console.error('日期解析错误:', error);
  }

  return { year: null, month: null, day: null };
};

// 为电话区号添加+前缀
const formatPhoneCode = phoneCode => {
  if (!phoneCode) return '';
  // 如果已经有+号，直接返回
  if (phoneCode.startsWith('+')) return phoneCode;
  // 否则添加+号
  return `+${phoneCode}`;
};

// 提取拼音部分的函数
const extractPinyin = text => {
  if (!text) return '';

  // 处理 "上海/SHANGHAI" 格式，提取斜杠后的拼音部分
  if (text.includes('/')) {
    const parts = text.split('/');
    return parts[parts.length - 1].trim().toUpperCase();
  }

  // 如果没有斜杠，检查是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(text);
  if (hasChinese) {
    // 如果包含中文，尝试提取英文部分
    const englishMatch = text.match(/[A-Za-z\s]+/);
    return englishMatch
      ? englishMatch[0].trim().toUpperCase()
      : text.trim().toUpperCase();
  }

  // 如果全是英文，直接返回大写
  return text.trim().toUpperCase();
};

// 检查拼音格式的函数
const validatePinyinFormat = (text, fieldName) => {
  if (!text) return true;

  // 检查是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(text);
  if (hasChinese) {
    ElMessage.warning(`${fieldName}应该只包含拼音，请检查格式`);
    return false;
  }

  // 检查是否包含斜杠（说明可能没有正确处理）
  if (text.includes('/')) {
    ElMessage.warning(`${fieldName}格式不正确，请只输入拼音部分`);
    return false;
  }

  return true;
};

// 验证邮箱格式的函数
const validateEmail = (email) => {
  if (!email) return true;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 监听姓氏变化，自动同步到出生时姓氏
watch(
  () => formData.passport_surname,
  newSurname => {
    if (newSurname && !formData.birth_surname) {
      formData.birth_surname = newSurname;
    }
  }
);

// 监听"费用由邀请人或邀请公司支付"变化，清空费用承担方字段
watch(
  () => formData.inviting_person_covered_costs,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      // 当选择"是"时，清空费用承担方输入框
      if (newValue === 1) {
        formData.costsCoveredBy = '';
      }
    }
  }
);

// 键盘快捷键支持
const handleKeydown = event => {
  // Alt + 左箭头：上一步
  if (event.altKey && event.key === 'ArrowLeft') {
    event.preventDefault();
    if (currentStep.value > 0) {
      handlePrevious();
    }
  }
  // Alt + 右箭头：下一步
  else if (event.altKey && event.key === 'ArrowRight') {
    event.preventDefault();
    if (currentStep.value < 5) {
      handleNext();
    }
  }
};

// 添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

// 移除键盘事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

// 计算停留时间
const calculateStayDuration = () => {
  if (formData.arrival_date && formData.departure_date) {
    const arrivalDate = new Date(
      formData.arrival_date.split('/').reverse().join('-')
    );
    const departureDate = new Date(
      formData.departure_date.split('/').reverse().join('-')
    );

    if (departureDate > arrivalDate) {
      const timeDiff = departureDate.getTime() - arrivalDate.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
      formData.duration_of_stay = daysDiff.toString();
    } else {
      formData.duration_of_stay = '';
    }
  } else {
    formData.duration_of_stay = '';
  }
};

// 表单验证规则
const validateStep = step => {
  const errors = [];

  if (step === 0) {
    // 第一步验证
    if (!formData.surname) errors.push('姓不能为空');
    if (!formData.given_name) errors.push('名不能为空');
    if (!formData.gender) errors.push('性别不能为空');
    if (!formData.birth_date) errors.push('出生日期不能为空');
    if (!formData.nationality) errors.push('国籍不能为空');
    if (!formData.passport_number) errors.push('护照号码不能为空');
    if (!formData.passport_expire_date) errors.push('护照有效期不能为空');
  } else if (step === 1) {
    // 第二步验证
    if (!formData.passport_surname) errors.push('护照姓不能为空');
    if (!formData.passport_given_name) errors.push('护照名不能为空');
    if (!formData.passport_birth_date) errors.push('护照出生日期不能为空');
    if (!formData.passport_gender) errors.push('护照性别不能为空');
    if (!formData.passport_marital_status) errors.push('婚姻状况不能为空');
    if (!formData.passport_number_detail) errors.push('护照号码详情不能为空');
    if (!formData.passport_type) errors.push('护照类型不能为空');
    if (!formData.passport_issue_date_detail)
      errors.push('护照签发日期不能为空');
    if (!formData.passport_expire_date_detail)
      errors.push('护照到期日期不能为空');
    if (!formData.birth_place) errors.push('出生地不能为空');
    if (!formData.issuing_authority) errors.push('签发机关不能为空');

    // 未成年申请者的监护人信息验证
    if (formData.is_minor === 'yes') {
      if (!formData.guardian_type) errors.push('家长监护权不能为空');
      if (!formData.guardian_surname) errors.push('监护人姓氏不能为空');
      if (!formData.guardian_given_name) errors.push('监护人名字不能为空');
      if (!formData.guardian_address) errors.push('监护人地址不能为空');
      if (!formData.guardian_nationality) errors.push('监护人国籍不能为空');
    }
  } else if (step === 2) {
    // 第三步验证
    if (!formData.applicant_country) errors.push('申请者居住国不能为空');
    if (!formData.applicant_address) errors.push('申请者住址不能为空');
    if (!formData.occupation_id) errors.push('当前职业不能为空');
    if (!formData.applicant_email) errors.push('申请者邮箱不能为空');
    if (!formData.applicant_phone) errors.push('申请者电话不能为空');

    // 职业其他信息验证
    if (formData.occupation_id === 'OTR' && !formData.occupation_others) {
      errors.push('职业其他信息不能为空');
    }

    // 工作单位信息验证
    if (
      formData.occupation_id &&
      formData.occupation_id !== 'NOO' &&
      formData.occupation_id !== 'NTA'
    ) {
      if (!formData.employer_name) errors.push('工作单位名称不能为空');
      if (!formData.employer_address) errors.push('工作单位地址不能为空');
      if (!formData.employer_phone) errors.push('工作单位电话不能为空');
      if (!formData.employer_city) errors.push('雇主城市不能为空');
      if (!formData.employer_postal_code) errors.push('雇主邮政编码不能为空');
      if (!formData.employer_country) errors.push('雇主国家不能为空');
    }

    // 居住在现国籍以外的国家验证
    if (formData.residence_other_nationality === 1) {
      if (!formData.residence_permit_no) errors.push('居留国许可编号不能为空');
      if (!formData.residence_permit_valid_until)
        errors.push('居留国许可结束日期不能为空');
    }

    // 指纹采集信息验证
    if (formData.fingerprints_collected === 1) {
      if (!formData.date_of_collection) errors.push('上次指纹采集日期不能为空');
      if (!formData.previous_application_number)
        errors.push('上次指纹采集时的签证贴纸号码不能为空');
    }
  } else if (step === 3) {
    // 第四步验证
    if (!formData.purpose_of_travel) errors.push('旅行目的不能为空');
    if (!formData.purpose_of_travel_add_info)
      errors.push('停留目的补充信息不能为空');
    if (!formData.number_of_entries) errors.push('申请入境次数不能为空');

    // 申根签证有效期信息验证
    if (formData.is_schengen_visa_issued === 1) {
      if (!formData.valid_from) errors.push('申根签证有效期开始日期不能为空');
      if (!formData.valid_till) errors.push('申根签证有效期结束日期不能为空');
    }
  } else if (step === 4) {
    // 第五步验证
    if (!formData.inviting_party_type) errors.push('邀请方类型不能为空');

    // 根据邀请方类型验证相应字段
    if (formData.inviting_party_type === 'Hotel') {
      if (!formData.hotel_name) errors.push('酒店名称不能为空');
      if (!formData.hotel_address) errors.push('酒店地址不能为空');
      if (!formData.hotel_email) errors.push('酒店邮箱不能为空');
      if (!formData.hotel_street) errors.push('酒店街道不能为空');
      if (!formData.hotel_city) errors.push('酒店城市不能为空');
      if (!formData.hotel_postal_code) errors.push('酒店邮编不能为空');
      if (!formData.hotel_country) errors.push('酒店国家不能为空');
    } else if (formData.inviting_party_type === 'InvitingPerson') {
      if (!formData.inviting_person_surname) errors.push('邀请人姓不能为空');
      if (!formData.inviting_person_given_name) errors.push('邀请人名不能为空');
      if (!formData.inviting_person_address) errors.push('邀请人地址不能为空');
      if (!formData.inviting_person_email) {
        errors.push('邀请人邮箱不能为空');
      } else if (!validateEmail(formData.inviting_person_email)) {
        errors.push('邀请人邮箱格式不正确');
      }
      if (!formData.inviting_person_phone) errors.push('邀请人电话不能为空');
      if (!formData.inviting_person_street) errors.push('邀请人街道不能为空');
      if (!formData.inviting_person_city) errors.push('邀请人城市不能为空');
      if (!formData.inviting_person_postal_code)
        errors.push('邀请人邮编不能为空');
      if (!formData.inviting_person_country) errors.push('邀请人国家不能为空');
    } else if (formData.inviting_party_type === 'Invitingenterprise') {
      if (!formData.enterprise_name) errors.push('邀请企业名称不能为空');
      if (!formData.enterprise_address) errors.push('邀请企业地址不能为空');
      if (!formData.enterprise_email) errors.push('邀请企业邮箱不能为空');
      if (!formData.enterprise_street) errors.push('邀请企业街道不能为空');
      if (!formData.enterprise_city) errors.push('邀请企业城市不能为空');
      if (!formData.enterprise_postal_code) errors.push('邀请企业邮编不能为空');
      if (!formData.enterprise_country) errors.push('邀请企业国家不能为空');
    } else if (formData.inviting_party_type === 'InvitingOrganisation') {
      if (!formData.organisation_name) errors.push('邀请机构名称不能为空');
      if (!formData.organisation_address) errors.push('邀请机构地址不能为空');
      if (!formData.organisation_email) errors.push('邀请机构邮箱不能为空');
      if (!formData.organisation_street) errors.push('邀请机构街道不能为空');
      if (!formData.organisation_city) errors.push('邀请机构城市不能为空');
      if (!formData.organisation_postal_code)
        errors.push('邀请机构邮编不能为空');
      if (!formData.organisation_country) errors.push('邀请机构国家不能为空');
    } else if (formData.inviting_party_type === 'Temporary accommodation') {
      if (!formData.temp_accommodation_address)
        errors.push('临时住宿地址不能为空');
      if (!formData.temp_accommodation_street)
        errors.push('临时住宿街道不能为空');
      if (!formData.temp_accommodation_city)
        errors.push('临时住宿城市不能为空');
      if (!formData.temp_accommodation_postal_code)
        errors.push('临时住宿邮编不能为空');
      if (!formData.temp_accommodation_country)
        errors.push('临时住宿国家不能为空');
      if (!formData.temp_accommodation_email)
        errors.push('临时住宿邮箱不能为空');
    } else if (
      formData.inviting_party_type === 'RegulationsstayinSwitzerland'
    ) {
      if (!formData.regulations_address)
        errors.push('瑞士居留规定地址不能为空');
      if (!formData.regulations_street) errors.push('瑞士居留规定街道不能为空');
      if (!formData.regulations_city) errors.push('瑞士居留规定城市不能为空');
      if (!formData.regulations_postal_code)
        errors.push('瑞士居留规定邮编不能为空');
      if (!formData.regulations_country)
        errors.push('瑞士居留规定国家不能为空');
      if (!formData.regulations_email) errors.push('瑞士居留规定邮箱不能为空');
    }
  } else if (step === 5) {
    // 第六步验证
    if (!formData.arrival_date) errors.push('到达申根区日期不能为空');
    if (!formData.departure_date) errors.push('离开申根区日期不能为空');
    if (formData.cost_covered_by.length === 0)
      errors.push('费用承担方不能为空');
    if (!formData.cost_covered_by_others)
      errors.push('出行和居住费用-其他不能为空');
    if (formData.means_of_support.length === 0) errors.push('支付方式不能为空');
    if (!formData.means_of_support_others)
      errors.push('申请人支付方式-其他不能为空');
    if (!formData.first_entry_schengen) errors.push('首入申根国不能为空');
    if (formData.destination_countries.length === 0)
      errors.push('目的地国家不能为空');

    // 当邀请人不支付费用时，验证费用承担方输入框
    if (formData.inviting_person_covered_costs === 2) {
      if (!formData.costsCoveredBy) {
        errors.push('费用承担方不能为空');
      }
    }

    // 欧盟公民家庭成员信息验证
    if (formData.is_eu_citizen_family === 1) {
      if (!formData.eu_citizen_surname) errors.push('欧盟公民姓不能为空');
      if (!formData.eu_citizen_given_name) errors.push('欧盟公民名不能为空');
      if (!formData.eu_citizen_nationality) errors.push('欧盟公民国籍不能为空');
      if (!formData.eu_citizen_birth_date)
        errors.push('欧盟公民出生日期不能为空');
      if (!formData.eu_citizen_passport_number)
        errors.push('欧盟公民护照号码不能为空');
      if (!formData.eu_citizen_relationship)
        errors.push('与欧盟公民的关系不能为空');
    }
  }

  return errors;
};

// 婚姻状况选项
const maritalStatusOptions = ref([
  { masterCode: 'SIGL', displayName_cn: '单身' },
  { masterCode: 'DIVC', displayName_cn: '离异' },
  { masterCode: 'WIDO', displayName_cn: '丧偶' },
  { masterCode: 'OTHR', displayName_cn: '其他' },
  { masterCode: 'MARD', displayName_cn: '已婚' },
  { masterCode: 'RPR', displayName_cn: '注册伴侣关系' },
]);

// 护照类型选项
const passportTypeOptions = ref([
  { masterCode: 'DIPT', displayName_cn: '外交护照' },
  { masterCode: 'OTH', displayName_cn: '其他旅行证件' },
  { masterCode: 'OFPT', displayName_cn: '公务护照' },
  { masterCode: 'SPPT', displayName_cn: '特别护照' },
  { masterCode: 'SEPT', displayName_cn: '公务护照' },
  { masterCode: 'ORPT', displayName_cn: '普通护照' },
]);

// 职业选项
const occupationOptions = ref([
  { id: 1, masterCode: 'SCI', displayName: '科学研究员' },
  { id: 2, masterCode: 'CHE', displayName: '化学家/化学工程师' },
  { id: 3, masterCode: 'POC', displayName: '警察/军人' },
  { id: 4, masterCode: 'ATT', displayName: '艺术家' },
  { id: 5, masterCode: 'SEA', displayName: '海员' },
  { id: 6, masterCode: 'MED', displayName: '医疗和辅助医疗专业' },
  { id: 7, masterCode: 'TRA', displayName: '商人' },
  { id: 8, masterCode: 'TEC', displayName: '教师' },
  { id: 9, masterCode: 'CMX', displayName: '公司高管' },
  { id: 10, masterCode: 'NTA', displayName: '不适用' },
  { id: 11, masterCode: 'DIP', displayName: '外交官/公务员' },
  { id: 12, masterCode: 'ASSM', displayName: '农业销售部门经理' },
  { id: 13, masterCode: 'PRS', displayName: '职业运动员' },
  { id: 14, masterCode: 'PEN', displayName: '退休人员' },
  { id: 15, masterCode: 'FAR', displayName: '农民' },
  { id: 16, masterCode: 'SLF', displayName: '自由职业者' },
  { id: 17, masterCode: 'ELE', displayName: '电子专家' },
  { id: 18, masterCode: 'NOO', displayName: '无业' },
  { id: 19, masterCode: 'CIS', displayName: '公务员' },
  { id: 20, masterCode: 'STU', displayName: '学生/实习生' },
  { id: 21, masterCode: 'MAN', displayName: '经理' },
  { id: 22, masterCode: 'POL', displayName: '政治家' },
  { id: 23, masterCode: 'OTR', displayName: '其他' },
  { id: 24, masterCode: 'MAG', displayName: '法官' },
  { id: 25, masterCode: 'ATS', displayName: '行政技术和服务人员' },
  { id: 26, masterCode: 'CLR', displayName: '神职人员/宗教人士' },
  { id: 27, masterCode: 'ARC', displayName: '建筑师' },
  { id: 28, masterCode: 'JOU', displayName: '记者' },
  { id: 29, masterCode: 'ART', displayName: '工匠' },
  { id: 30, masterCode: 'OTH', displayName: '其他技术人员' },
  { id: 31, masterCode: 'FAS', displayName: '时尚化妆品' },
  { id: 32, masterCode: 'BNK', displayName: '银行家' },
  { id: 33, masterCode: 'CMP', displayName: '计算机专家' },
  { id: 34, masterCode: 'LEG', displayName: '法律专业人士' },
  { id: 35, masterCode: 'LEG', displayName: '法律专业人士(律师)/法律顾问' },
  { id: 36, masterCode: 'WCW', displayName: '白领工人' },
  { id: 37, masterCode: 'BCW', displayName: '蓝领工人' },
  { id: 38, masterCode: 'CFL', displayName: '司机/卡车司机' },
  { id: 39, masterCode: 'DIM', displayName: '外交官' },
  { id: 40, masterCode: 'MANO', displayName: '运营经理' },
]);

// 旅行目的选项
const travelPurposeOptions = ref([
  { id: 1, masterCode: 'SPRT', displayName: '体育' },
  { id: 2, masterCode: 'BUSN', displayName: '商务' },
  { id: 3, masterCode: 'TRSM', displayName: '旅游' },
  { id: 4, masterCode: 'STDY', displayName: '学习' },
  { id: 5, masterCode: 'MED', displayName: '医疗原因' },
  { id: 6, masterCode: 'CULT', displayName: '文化原因' },
  { id: 7, masterCode: 'OTHR', displayName: '其他' },
  { id: 8, masterCode: 'OFVS', displayName: '官方访问' },
  { id: 9, masterCode: 'VIFF', displayName: '探访家人或朋友' },
  { id: 10, masterCode: 'ARTR', displayName: '机场过境' },
]);

// 申请入境次数选项
const entryOptions = ref([
  { id: 1, masterCode: 'ONE', displayName: '一次' },
  { id: 2, masterCode: 'TWO', displayName: '两次' },
  { id: 3, masterCode: 'MLT', displayName: '多次' },
]);

// 邀请方类型选项
const invitingPartyOptions = ref([
  { id: 1, masterCode: 'InvitingPerson', displayName: '邀请人' },
  { id: 2, masterCode: 'Invitingenterprise', displayName: '邀请企业' },
  { id: 3, masterCode: 'Hotel', displayName: '酒店' },
  { id: 4, masterCode: 'Temporary accommodation', displayName: '临时住宿' },
  { id: 5, masterCode: 'InvitingOrganisation', displayName: '邀请机构' },
  {
    id: 6,
    masterCode: 'RegulationsstayinSwitzerland',
    displayName: '瑞士居留规定',
  },
]);

// 费用承担方选项
const costCoveredByOptions = ref([
  { id: 1, masterCode: 'AHH', displayName: '申请者本人' },
  { id: 2, masterCode: 'BO', displayName: '其他人' },
  { id: 3, masterCode: 'HST', displayName: '接待方' },
]);

// 支付方式选项
const meansOfSupportOptions = ref([
  { id: 1, masterCode: 'TRC', displayName: '旅行支票' },
  { id: 2, masterCode: 'CC', displayName: '信用卡' },
  { id: 3, masterCode: 'OTHR', displayName: '其他' },
  { id: 4, masterCode: 'PPA', displayName: '预付住宿' },
  { id: 5, masterCode: 'CASH', displayName: '现金' },
  { id: 6, masterCode: 'AECS', displayName: '停留期间所有费用已支付' },
  { id: 7, masterCode: 'APD', displayName: '提供住宿' },
  { id: 8, masterCode: 'PAT', displayName: '预付交通' },
]);

// 与欧盟公民关系选项
const euRelationshipOptions = ref([
  { id: 1, masterCode: 'SRP', displayName: '配偶' },
  { id: 2, masterCode: 'CUYA', displayName: '子女' },
  { id: 3, masterCode: 'RPR', displayName: '注册伴侣关系' },
  { id: 4, masterCode: 'OTH', displayName: '其他' },
  { id: 5, masterCode: 'DCPE', displayName: '受抚养的直系亲属' },
  { id: 6, masterCode: 'GUOA', displayName: '孙子女' },
]);

// 申根国家选项
const schengenCountryOptions = ref([
  { id: 1, masterCode: 'CHE', displayName: '瑞士' },
  { id: 2, masterCode: 'POL', displayName: '波兰' },
  { id: 3, masterCode: 'FIN', displayName: '芬兰' },
  { id: 4, masterCode: 'ROM', displayName: '罗马尼亚' },
  { id: 5, masterCode: 'PRT', displayName: '葡萄牙' },
  { id: 6, masterCode: 'HRV', displayName: '克罗地亚' },
  { id: 7, masterCode: 'SVN', displayName: '斯洛文尼亚' },
  { id: 8, masterCode: 'BEL', displayName: '比利时' },
  { id: 9, masterCode: 'AUT', displayName: '奥地利' },
  { id: 10, masterCode: 'LVA', displayName: '拉脱维亚' },
  { id: 11, masterCode: 'ISL', displayName: '冰岛' },
  { id: 12, masterCode: 'GRC', displayName: '希腊' },
  { id: 13, masterCode: 'SWE', displayName: '瑞典' },
  { id: 14, masterCode: 'ITA', displayName: '意大利' },
  { id: 15, masterCode: 'CZE', displayName: '捷克共和国' },
  { id: 16, masterCode: 'FRA', displayName: '法国' },
  { id: 17, masterCode: 'NLD', displayName: '荷兰' },
  { id: 18, masterCode: 'LIE', displayName: '列支敦士登' },
  { id: 19, masterCode: 'NOR', displayName: '挪威' },
  { id: 20, masterCode: 'SVK', displayName: '斯洛伐克' },
  { id: 21, masterCode: 'DNK', displayName: '丹麦' },
  { id: 22, masterCode: 'BUL', displayName: '保加利亚' },
  { id: 23, masterCode: 'EST', displayName: '爱沙尼亚' },
  { id: 24, masterCode: 'LTU', displayName: '立陶宛' },
  { id: 25, masterCode: 'MLT', displayName: '马耳他' },
  { id: 26, masterCode: 'CYP', displayName: '塞浦路斯' },
  { id: 27, masterCode: 'HUN', displayName: '匈牙利' },
  { id: 28, masterCode: 'DEU', displayName: '德国' },
  { id: 29, masterCode: 'LUX', displayName: '卢森堡' },
  { id: 30, masterCode: 'ESP', displayName: '西班牙' },
]);

// 护照上传前验证
const beforeUpload = file => {
  const isImage = file.type.startsWith('image/');
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 护照上传和OCR识别
const handlePassportUpload = async options => {
  uploading.value = true;
  try {
    let file = options.file;

    // 转换为base64
    const base64Promise = new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });

    const base64Data = await base64Promise;

    // 调用OCR识别接口
    const res = await http.post('/api/ocr-passport', {
      image: base64Data,
    });

    if (res.code === 1 && res.result) {
      // 自动填充护照信息
      const ocrResult = res.result;

      // 解析姓名拼音
      if (ocrResult['姓名拼音']?.words) {
        const pinyinName = ocrResult['姓名拼音'].words.trim();
        if (pinyinName.includes(',')) {
          const parts = pinyinName.split(',');
          formData.surname = parts[0].trim();
          formData.given_name = parts[1].trim();
          formData.passport_surname = parts[0].trim();
          formData.passport_given_name = parts[1].trim();
        }
      }

      // 护照号码
      if (ocrResult['护照号码']?.words) {
        formData.passport_number = ocrResult['护照号码'].words.trim();
        formData.passport_number_detail = ocrResult['护照号码'].words.trim();
      }

      // 性别
      if (ocrResult['性别']?.words) {
        const genderText = ocrResult['性别'].words.trim();
        if (genderText.includes('女') || genderText.includes('F')) {
          formData.gender = 'F';
          formData.passport_gender = 'F';
        } else if (genderText.includes('男') || genderText.includes('M')) {
          formData.gender = 'M';
          formData.passport_gender = 'M';
        }
      }

      // 生日
      if (ocrResult['生日']?.words) {
        const birthday = ocrResult['生日'].words.trim();
        if (birthday.length === 8) {
          const year = birthday.substring(0, 4);
          const month = birthday.substring(4, 6);
          const day = birthday.substring(6, 8);
          const formattedDate = `${day}/${month}/${year}`;
          formData.birth_date = formattedDate;
          formData.passport_birth_date = formattedDate;
        }
      }

      // 有效期至
      if (ocrResult['有效期至']?.words) {
        const expiry = ocrResult['有效期至'].words.trim();
        if (expiry.length === 8) {
          const year = expiry.substring(0, 4);
          const month = expiry.substring(4, 6);
          const day = expiry.substring(6, 8);
          const formattedDate = `${day}/${month}/${year}`;
          formData.passport_expire_date = formattedDate;
          formData.passport_expire_date_detail = formattedDate;
        }
      }

      // 签发日期
      if (ocrResult['签发日期']?.words) {
        const issueDate = ocrResult['签发日期'].words.trim();
        if (issueDate.length === 8) {
          const year = issueDate.substring(0, 4);
          const month = issueDate.substring(4, 6);
          const day = issueDate.substring(6, 8);
          formData.passport_issue_date_detail = `${day}/${month}/${year}`;
        }
      }

      // 出生地点 - 自动提取拼音部分
      if (ocrResult['出生地点']?.words) {
        const birthPlace = ocrResult['出生地点'].words.trim();
        formData.birth_place = extractPinyin(birthPlace);
      }

      // 护照签发地点 - 自动提取拼音部分
      if (ocrResult['护照签发地点']?.words) {
        const issuingPlace = ocrResult['护照签发地点'].words.trim();
        formData.issuing_authority = extractPinyin(issuingPlace);
      }

      // 国籍
      if (ocrResult['国籍']?.words) {
        const nationalityText = ocrResult['国籍'].words.trim();
        if (
          nationalityText.includes('中国') ||
          nationalityText.includes('CHINESE')
        ) {
          formData.nationality = 'CHN';
          formData.current_nationality = 'CHN';
        } else {
          formData.nationality = nationalityText;
          formData.current_nationality = nationalityText;
        }
      }

      ElMessage.success('护照识别成功，信息已自动填充');
    } else {
      ElMessage.error(res.message || '识别失败');
    }
  } catch (error) {
    console.error('护照上传错误:', error);
    ElMessage.error('上传失败');
  } finally {
    uploading.value = false;
  }
};

// 验证地址输入（不包含中文字符和全角符号）
const validateAddressInput = (value) => {
  // 检查是否包含中文字符
  const chineseRegex = /[\u4e00-\u9fa5]/;
  // 检查是否包含全角字符（包括全角空格、全角标点等）
  const fullWidthRegex = /[\uff00-\uffef\u3000-\u303f]/;
  
  if (chineseRegex.test(value) || fullWidthRegex.test(value)) {
    ElMessage.warning('申请者住址不能包含中文字符或全角符号，请使用半角英文字符');
    return false;
  }
  return true;
};

// 处理地址输入
const handleAddressInput = (value) => {
  // 移除中文字符和全角字符
  // \u4e00-\u9fa5: 中文字符
  // \uff00-\uffef: 全角ASCII、全角标点
  // \u3000-\u303f: CJK标点符号（包括全角空格\u3000）
  const invalidCharsRegex = /[\u4e00-\u9fa5\uff00-\uffef\u3000-\u303f]/g;
  const cleanedValue = value.replace(invalidCharsRegex, '');
  
  // 限制长度为100字符
  if (cleanedValue.length > 100) {
    formData.applicant_address = cleanedValue.substring(0, 100);
    ElMessage.warning('申请者住址长度不能超过100个字符');
  } else {
    formData.applicant_address = cleanedValue;
  }
  
  // 如果输入包含无效字符，提示用户
  if (value !== cleanedValue) {
    ElMessage.warning('申请者住址不能包含中文字符或全角符号，请使用半角英文字符');
  }
};

// 处理电话号码输入（仅允许数字）
const handlePhoneInput = (value, fieldName) => {
  // 只保留数字
  const numericValue = value.replace(/[^0-9]/g, '');
  
  // 更新对应的字段
  formData[fieldName] = numericValue;
  
  // 如果输入包含非数字字符，提示用户
  if (value !== numericValue) {
    ElMessage.warning('电话号码只能包含数字');
  }
};

// 步骤导航方法
const handlePrevious = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
    // 滚动到顶部，确保用户看到新步骤的内容
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 提示用户已返回上一步
    const stepNames = [
      '基本资格信息',
      '护照详细信息',
      '申请人信息',
      '旅行计划信息',
      '住宿安排信息',
      '附加信息及确认',
    ];
    ElMessage.info(`已返回到：${stepNames[currentStep.value]}`);
  }
};

const handleNext = async () => {
  console.log('handleNext 被调用，当前步骤：', currentStep.value);
  console.log('当前表单数据：', JSON.stringify(formData, null, 2));

  // 验证当前步骤
  const errors = validateStep(currentStep.value);
  console.log('验证结果：', errors);

  if (errors.length > 0) {
    ElMessage.error(`请完善以下信息：${errors.join('、')}`);
    return;
  }

  // 在第一步进入第二步时，检查拼音格式
  if (currentStep.value === 0) {
    // 检查出生地拼音格式
    if (!validatePinyinFormat(formData.birth_place, '出生地')) {
      return;
    }

    // 检查签发机关拼音格式
    if (!validatePinyinFormat(formData.issuing_authority, '签发机关')) {
      return;
    }
  }

  // 在第二步进入第三步时，再次检查拼音格式
  if (currentStep.value === 1) {
    // 检查出生地拼音格式
    if (!validatePinyinFormat(formData.birth_place, '出生地')) {
      return;
    }

    // 检查签发机关拼音格式
    if (!validatePinyinFormat(formData.issuing_authority, '签发机关')) {
      return;
    }
  }

  if (currentStep.value < 5) {
    currentStep.value++;
    // 滚动到顶部，确保用户看到新步骤的内容
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // 提示用户已进入下一步
    const stepNames = [
      '基本资格信息',
      '护照详细信息',
      '申请人信息',
      '旅行计划信息',
      '住宿安排信息',
      '附加信息及确认',
    ];
    ElMessage.success(`已进入：${stepNames[currentStep.value]}`);
  } else {
    // 最后一步，提交表单
    await handleSubmit();
  }
};

// 方法
const handleCancel = () => {
  emit('cancel');
};

const handleSaveDraft = async () => {
  try {
    formData.status = 'draft';
    await saveForm();
    ElMessage.success('草稿保存成功');
    emit('save');
  } catch (error) {
    console.error('保存草稿失败:', error);
    ElMessage.error('保存草稿失败，请重试');
  }
};

const handleSubmit = async () => {
  console.log('handleSubmit 被调用');
  try {
    // 最终验证所有步骤
    let allErrors = [];
    for (let i = 0; i < 6; i++) {
      const stepErrors = validateStep(i);
      if (stepErrors.length > 0) {
        allErrors.push(`第${i + 1}步：${stepErrors.join('、')}`);
      }
    }

    if (allErrors.length > 0) {
      ElMessage.error(`请完善以下信息：\n${allErrors.join('\n')}`);
      return;
    }

    const allStepData = {};
    for (let i = 0; i < 6; i++) {
      const stepData = getStepData(i);
      allStepData[`step${i + 1}`] = stepData;
    }

    formData.status = 'submitted';
    await saveForm();
    emit('save');
  } catch (error) {
    console.error('提交失败：', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('提交失败，请重试');
    }
  }
};

// 获取指定步骤的数据
const getStepData = step => {
  const originalStep = currentStep.value;
  currentStep.value = step;
  const data = currentStepData.value;
  currentStep.value = originalStep;
  return data;
};

const saveForm = async () => {
  submitting.value = true;
  try {
    const url = props.isEditing
      ? `/api/schengen-forms/${formData.id}`
      : '/api/schengen-forms';

    const method = props.isEditing ? 'put' : 'post';

    // 创建格式化后的数据，确保电话区号包含+前缀
    const formattedData = {
      ...formData,
      // 格式化所有电话区号字段
      applicant_phone_code: formatPhoneCode(formData.applicant_phone_code),
      hotel_phone_code: formatPhoneCode(formData.hotel_phone_code),
      inviting_person_phone_code: formatPhoneCode(
        formData.inviting_person_phone_code
      ),
      enterprise_phone_code: formatPhoneCode(formData.enterprise_phone_code),
      organisation_phone_code: formatPhoneCode(
        formData.organisation_phone_code
      ),
      temp_accommodation_phone_code: formatPhoneCode(
        formData.temp_accommodation_phone_code
      ),
      regulations_phone_code: formatPhoneCode(formData.regulations_phone_code),
    };

    console.log('提交的格式化数据:', {
      applicant_phone_code: formattedData.applicant_phone_code,
      hotel_phone_code: formattedData.hotel_phone_code,
      inviting_person_phone_code: formattedData.inviting_person_phone_code,
      enterprise_phone_code: formattedData.enterprise_phone_code,
      organisation_phone_code: formattedData.organisation_phone_code,
      temp_accommodation_phone_code:
        formattedData.temp_accommodation_phone_code,
      regulations_phone_code: formattedData.regulations_phone_code,
    });

    const response = await http[method](url, formattedData);

    if (response.code !== 1) {
      throw new Error(response.message || '保存失败');
    }
  } finally {
    submitting.value = false;
  }
};

// 移除电话区号的+前缀（用于显示）
const removePhoneCodePrefix = phoneCode => {
  if (!phoneCode) return '';
  // 如果有+号，移除它
  if (phoneCode.startsWith('+')) return phoneCode.substring(1);
  // 否则直接返回
  return phoneCode;
};

// 监听props变化
watch(
  () => props.formData,
  newData => {
    if (newData) {
      Object.assign(formData, newData);

      // 处理电话区号，移除+前缀用于显示
      if (formData.applicant_phone_code) {
        formData.applicant_phone_code = removePhoneCodePrefix(
          formData.applicant_phone_code
        );
      }
      if (formData.inviting_person_phone_code) {
        formData.inviting_person_phone_code = removePhoneCodePrefix(
          formData.inviting_person_phone_code
        );
      }
      if (formData.hotel_phone_code) {
        formData.hotel_phone_code = removePhoneCodePrefix(
          formData.hotel_phone_code
        );
      }
      if (formData.enterprise_phone_code) {
        formData.enterprise_phone_code = removePhoneCodePrefix(
          formData.enterprise_phone_code
        );
      }
      if (formData.organisation_phone_code) {
        formData.organisation_phone_code = removePhoneCodePrefix(
          formData.organisation_phone_code
        );
      }
      if (formData.temp_accommodation_phone_code) {
        formData.temp_accommodation_phone_code = removePhoneCodePrefix(
          formData.temp_accommodation_phone_code
        );
      }
      if (formData.regulations_phone_code) {
        formData.regulations_phone_code = removePhoneCodePrefix(
          formData.regulations_phone_code
        );
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.schengen-form-editor {
  display: flex;
  height: 80vh;
  gap: 20px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.steps-container {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.step-panel {
  max-width: 900px;
  margin: 0 auto;
}

.step-title {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
}

.step-form {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.passport-upload-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.passport-uploader {
  width: 100%;
}

.passport-uploader :deep(.el-upload) {
  width: 100%;
}

.passport-uploader :deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.passport-uploader :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
}

.upload-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 14px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-item-half {
  flex: 1;
}

.form-item-third {
  flex: 0 0 calc(33.333% - 11px);
}

.form-item-quarter {
  flex: 0 0 calc(25% - 12px);
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  margin: 0 -20px -20px -20px;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.keyboard-hint {
  display: flex;
  align-items: center;
}

.hint-text {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.prev-button {
  font-weight: 500;
}

.prev-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.next-button {
  font-weight: 500;
  min-width: 120px;
}

/* 验证状态样式 */
.validation-status {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.step-validation {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 4px;
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
}

.step-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.step-label.has-errors {
  color: #f56c6c;
}

.status-ok {
  color: #67c23a;
  font-weight: bold;
}

.status-error {
  color: #f56c6c;
  font-size: 11px;
}

/* 当前步骤错误信息样式 */
.current-step-errors {
  margin-top: 16px;
  padding: 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

.error-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #f56c6c;
}

.error-list {
  margin: 0;
  padding-left: 16px;
}

.error-item {
  font-size: 12px;
  color: #f56c6c;
  margin-bottom: 4px;
  line-height: 1.4;
}

.error-item:last-child {
  margin-bottom: 0;
}

/* 第三步申请信息样式 */
.section-title {
  margin: 20px 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.work-info-section,
.residence-permit-section,
.fingerprint-section,
.schengen-visa-section,
.accommodation-section {
  margin-top: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.work-info-section .section-title,
.residence-permit-section .section-title,
.fingerprint-section .section-title,
.schengen-visa-section .section-title,
.accommodation-section .section-title {
  margin-top: 0;
  color: #606266;
  border-bottom-color: #909399;
}

/* 电话输入组合样式 */
.phone-input-group {
  display: flex;
  align-items: center;
  width: 100%;
}

.phone-code-input {
  display: flex;
  align-items: center;
  position: relative;
}

.phone-prefix {
  position: absolute;
  left: 6px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  color: #606266;
  font-weight: 500;
  pointer-events: none;
  font-size: 14px;
  line-height: 1;
}

.phone-code-input .el-input__wrapper {
  padding-left: 28px !important;
}

.phone-code-input .el-input__inner {
  padding-left: 28px !important;
}

.phone-code-input input {
  padding-left: 28px !important;
}

.phone-code-input .el-input input {
  padding-left: 28px !important;
}

.phone-code-input .el-input .el-input__inner {
  padding-left: 28px !important;
}

/* 更具体的选择器 */
.phone-input-group .phone-code-input .el-input__wrapper {
  padding-left: 28px !important;
}

.phone-input-group .phone-code-input input {
  padding-left: 28px !important;
}

/* 使用深度选择器确保样式生效 */
.phone-code-input :deep(.el-input__wrapper) {
  padding-left: 28px !important;
}

.phone-code-input :deep(input) {
  padding-left: 28px !important;
}

.phone-code-input :deep(.el-input__inner) {
  padding-left: 28px !important;
}

/* 目的地国家网格样式 */
.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fafafa;
}

.checkbox-item {
  margin: 0 !important;
}

/* 步骤标题优化 */
.step-title {
  color: #409eff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #409eff;
  position: relative;
}

.step-title::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

/* 表单布局优化 */
.step-form {
  margin-top: 20px;
}

.form-row {
  margin-bottom: 20px;
}

.form-row:last-child {
  margin-bottom: 0;
}

/* 必填字段标识优化 */
.el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
  font-weight: bold;
}

/* 分组区块优化 */
.section-title {
  position: relative;
  padding-left: 16px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: #409eff;
  border-radius: 2px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .form-item-half,
  .form-item-third {
    width: 100% !important;
    margin-right: 0 !important;
  }

  .checkbox-grid {
    grid-template-columns: 1fr;
  }

  .phone-input-group {
    flex-direction: column;
    gap: 8px;
  }

  .phone-input-group .el-select {
    width: 100% !important;
    margin-right: 0 !important;
  }

  .form-actions {
    flex-direction: column;
    gap: 16px;
  }

  .left-actions,
  .right-actions {
    width: 100%;
    justify-content: center;
  }

  .keyboard-hint {
    display: none; /* 移动端隐藏快捷键提示 */
  }

  .prev-button,
  .next-button {
    min-width: 100px;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .schengen-form-editor {
    flex-direction: column;
    height: auto;
  }

  .main-content {
    overflow-y: visible;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-item-half,
  .form-item-third,
  .form-item-quarter {
    flex: none;
    width: 100%;
  }

  .step-content {
    padding: 12px;
  }

  .step-form {
    padding: 16px;
  }
}
</style>
