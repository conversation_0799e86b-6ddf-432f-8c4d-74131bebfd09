<template>
  <el-drawer
    v-model="visible"
    size="900px"
    direction="ltr"
    :with-header="false"
    @closed="resetForm"
    class="new-order-drawer"
  >
    <!-- 自定义头部 -->
    <div class="drawer-header">
      <div class="header-content">
        <!-- 第一行：标题和关闭按钮 -->
        <div class="header-row-1">
          <div class="title-section">
            <h2 class="drawer-title">
              <el-icon class="title-icon"><DocumentAdd /></el-icon>
              {{ editingOrderId ? '编辑订单' : '新增订单' }}
            </h2>
          </div>
          <el-button
            type="info"
            text
            @click="emit('update:visible', false)"
            class="close-btn"
            size="large"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>

        <!-- 第二行：步骤指示器 -->
        <div class="header-row-2">
          <div class="header-steps">
            <el-steps
              :active="currentStep"
              finish-status="success"
              align-center
              class="header-steps-component"
            >
              <el-step title="签证类型" description="选择签证类型">
                <template #icon>
                  <el-icon><Document /></el-icon>
                </template>
              </el-step>
              <el-step title="客户信息" description="填写详细信息">
                <template #icon>
                  <el-icon><User /></el-icon>
                </template>
              </el-step>
              <el-step title="预约配置" description="设置预约选项">
                <template #icon>
                  <el-icon><Setting /></el-icon>
                </template>
              </el-step>
            </el-steps>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="drawer-content">
      <!-- 签证预约信息步骤 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-form label-width="140px" class="visa-form">
          <!-- 签证类型选择 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Location /></el-icon>
                签证类型选择
              </h4>
            </div>
            <el-form-item label="目标国家" required>
              <el-cascader
                v-model="visaType"
                :options="props.visaOptions"
                :props="cprops"
                placeholder="请选择签证类型"
                clearable
                class="visa-cascader"
                style="width: 500px"
                @change="handleVisaTypeChange"
              />

              <!-- 多选提示信息 -->
              <div
                v-if="visaType && visaType.length > 1"
                class="visa-selection-tip"
              >
                <el-alert
                  :title="`已选择 ${visaType.length} 个签证类型，将会创建 ${visaType.length} 个独立订单`"
                  type="info"
                  show-icon
                  :closable="false"
                  style="margin-top: 10px"
                >
                  <template #default>
                    <div
                      style="font-size: 12px; color: #909399; margin-top: 5px"
                    >
                      每个签证类型将生成一个独立的订单，便于分别管理和跟踪
                    </div>
                    <div
                      style="font-size: 11px; color: #a8abb2; margin-top: 3px"
                    >
                      选择的签证类型：
                      <span
                        v-for="(visa, index) in visaType"
                        :key="index"
                        style="margin-left: 5px"
                      >
                        {{ visa[0] }}-{{ visa[2]
                        }}{{ index < visaType.length - 1 ? '，' : '' }}
                      </span>
                    </div>
                    <div
                      v-if="
                        visaType.some(
                          v => v && v[0] && v[0].toLowerCase().includes('spain')
                        )
                      "
                      style="font-size: 11px; color: #e6a23c; margin-top: 3px"
                    >
                      注意：只能选择一个西班牙签证类型
                    </div>
                  </template>
                </el-alert>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>

      <!-- 客户信息步骤 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="clients-section">
          <div class="clients-header">
            <span class="clients-count">共 {{ clients.length }} 位申请人</span>
            <el-button type="primary" size="small" @click="addClient">
              <el-icon><Plus /></el-icon>
              添加申请人
            </el-button>
          </div>

          <el-collapse v-model="activeNames" accordion class="client-collapse">
            <el-collapse-item
              v-for="(client, index) in clients"
              :key="index"
              :name="String(index)"
              class="client-item"
            >
              <template #title>
                <div class="client-title">
                  <div class="client-info">
                    <el-avatar :size="36" class="client-avatar">
                      {{ index + 1 }}
                    </el-avatar>
                    <div class="client-details">
                      <span class="client-name">
                        {{ client.name || `申请人 ${index + 1}` }}
                      </span>
                      <span
                        class="client-status"
                        :class="getClientStatusClass(client)"
                      >
                        {{ getClientStatusText(client) }}
                      </span>
                    </div>
                  </div>
                  <div class="client-actions">
                    <el-tag v-if="index === 0" type="primary" size="small"
                      >主申请人</el-tag
                    >
                    <el-tag v-else type="info" size="small">同行人</el-tag>
                    <el-button
                      v-if="clients.length > 1"
                      type="danger"
                      text
                      size="small"
                      @click.stop="removeClientByIndex(index)"
                      class="remove-btn"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>

              <el-form
                :model="client"
                label-width="140px"
                :rules="rules"
                :ref="el => (formRefs[index] = el)"
                class="client-form"
              >
                <!-- 图片上传区域（紧凑版） -->
                <div class="upload-section-compact">
                  <!-- 护照上传 -->
                  <div
                    class="upload-item-row"
                    :class="{ 'drag-over': dragStates[`passport-${index}`] }"
                    @dragenter="handleDragEnter($event, `passport-${index}`)"
                    @dragover="handleDragOver($event, `passport-${index}`)"
                    @dragleave="handleDragLeave($event, `passport-${index}`)"
                    @drop="handleDrop($event, index, 'passport')"
                  >
                    <div class="upload-label">
                      <el-icon><Camera /></el-icon>
                      <span>护照照片</span>
                      <el-tag size="small" type="danger">必需</el-tag>
                    </div>
                    <div class="upload-action">
                      <div
                        v-if="!client.passport_image"
                        class="upload-drop-zone"
                      >
                        <div class="drop-hint">
                          <el-icon><Upload /></el-icon>
                          <span>拖拽文件到这里或</span>
                        </div>
                        <el-upload
                          :http-request="
                            options => customUpload(options, index)
                          "
                          name="file"
                          :show-file-list="false"
                          accept=".jpg,.jpeg,.png"
                          :loading="uploading"
                          :disabled="uploading"
                        >
                          <el-button
                            type="primary"
                            size="small"
                            :loading="uploading"
                          >
                            点击上传
                          </el-button>
                        </el-upload>
                      </div>
                      <div v-if="client.passport_image" class="uploaded-info">
                        <el-image
                          :src="`http://visa.qianwuyouvisa.com:5005/api/passport_image/${client.passport_image}`"
                          fit="cover"
                          class="preview-thumb"
                          @click="showPassportDialog(client.passport_image)"
                        />
                        <div class="upload-actions">
                          <el-button
                            type="primary"
                            text
                            size="small"
                            @click="showPassportDialog(client.passport_image)"
                          >
                            <el-icon><View /></el-icon>
                            查看
                          </el-button>
                          <el-button
                            type="warning"
                            text
                            size="small"
                            @click="reuploadPassport(index)"
                          >
                            <el-icon><Refresh /></el-icon>
                            重传
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 头像照片上传 (仅西班牙签证) -->
                <div
                  v-if="hasSpainVisa(visaType)"
                  class="upload-section-compact"
                >
                  <div
                    class="upload-item-row"
                    :class="{ 'drag-over': dragStates[`avatar-${index}`] }"
                    @dragenter="handleDragEnter($event, `avatar-${index}`)"
                    @dragover="handleDragOver($event, `avatar-${index}`)"
                    @dragleave="handleDragLeave($event, `avatar-${index}`)"
                    @drop="handleDrop($event, index, 'avatar')"
                  >
                    <div class="upload-label">
                      <el-icon><Avatar /></el-icon>
                      <span>头像照片</span>
                      <el-tag size="small" type="info">可选</el-tag>
                      <el-tag size="small" type="warning">自动压缩至100KB</el-tag>
                    </div>
                    <div class="upload-action">
                      <div v-if="!client.avatar_image" class="upload-drop-zone">
                        <div class="drop-hint">
                          <el-icon><Upload /></el-icon>
                          <span>拖拽文件到这里或</span>
                        </div>
                        <el-upload
                          :http-request="
                            options => handleAvatarUpload(options, index)
                          "
                          name="file"
                          :show-file-list="false"
                          accept=".jpg,.jpeg,.png"
                          :loading="uploading"
                          :disabled="uploading"
                        >
                          <el-button
                            type="primary"
                            size="small"
                            :loading="uploading"
                          >
                            点击上传
                          </el-button>
                        </el-upload>
                      </div>
                      <div v-if="client.avatar_image" class="uploaded-info">
                        <el-image
                          :src="`http://visa.qianwuyouvisa.com:5005/api/avatar_image/${client.avatar_image}`"
                          fit="cover"
                          class="preview-thumb"
                          @click="showAvatarDialog(client.avatar_image)"
                        />
                        <div class="upload-actions">
                          <el-button
                            type="primary"
                            text
                            size="small"
                            @click="showAvatarDialog(client.avatar_image)"
                          >
                            <el-icon><View /></el-icon>
                            查看
                          </el-button>
                          <el-button
                            type="warning"
                            text
                            size="small"
                            @click="reuploadAvatar(index)"
                          >
                            <el-icon><Refresh /></el-icon>
                            重传
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 基本信息 -->
                <div class="form-section">
                  <h4 class="section-title">
                    <el-icon><User /></el-icon>
                    基本信息
                  </h4>
                  <div class="form-row">
                    <el-form-item
                      label="姓名"
                      prop="name"
                      class="el-form-item--large"
                    >
                      <el-input
                        v-model="client.name"
                        placeholder="请输入中文姓名"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </div>
                  <div class="form-row">
                    <el-form-item label="姓拼音" prop="surname_pinyin">
                      <el-input
                        v-model="client.surname_pinyin"
                        placeholder="如：ZHANG"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item label="名拼音" prop="firstname_pinyin">
                      <el-input
                        v-model="client.firstname_pinyin"
                        placeholder="如：WEI"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </div>
                  <div class="form-row">
                    <el-form-item
                      label="性别"
                      prop="gender"
                      class="el-form-item--small"
                    >
                      <el-select
                        v-model="client.gender"
                        placeholder="请选择性别"
                        style="width: 100%"
                      >
                        <el-option label="男" value="男" />
                        <el-option label="女" value="女" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="出生日期" prop="dob">
                      <el-date-picker
                        v-model="client.dob"
                        type="date"
                        placeholder="选择出生日期"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </div>
                </div>

                <!-- 护照信息 -->
                <div class="form-section">
                  <h4 class="section-title">
                    <el-icon><Document /></el-icon>
                    护照信息
                  </h4>
                  <div class="form-row">
                    <el-form-item label="护照号码" prop="passport">
                      <el-input
                        v-model="client.passport"
                        placeholder="如：*********"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </div>

                  <!-- 护照日期信息 -->
                  <div class="form-row">
                    <el-form-item
                      label="护照签发日期"
                      prop="passport_date"
                      required
                    >
                      <el-date-picker
                        v-model="client.passport_date"
                        type="date"
                        placeholder="选择护照签发日期"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item
                      label="护照过期日期"
                      prop="passport_expire"
                      required
                    >
                      <el-date-picker
                        v-model="client.passport_expire"
                        type="date"
                        placeholder="选择护照过期日期"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </div>

                  <!-- 国籍选择 -->
                  <div class="form-row">
                    <el-form-item label="国籍" prop="nationality" required>
                      <el-select
                        v-model="client.nationality"
                        placeholder="请选择国籍"
                        style="width: 100%"
                        filterable
                      >
                        <el-option
                          v-for="country in countries"
                          :key="country.isoCode"
                          :label="country.nationalityName"
                          :value="country.isoCode"
                        />
                      </el-select>
                    </el-form-item>
                  </div>

                  <!-- 西班牙签证额外字段 -->
                  <template v-if="hasSpainVisa(visaType)">
                    <div class="form-row">
                      <el-form-item label="出生地点" prop="bornplace" required>
                        <el-input
                          v-model="client.bornplace"
                          placeholder="如：北京/BEIJING"
                          style="width: 100%"
                        />
                      </el-form-item>
                      <el-form-item
                        label="签发地点"
                        prop="sign_location"
                        required
                      >
                        <el-input
                          v-model="client.sign_location"
                          placeholder="如：北京/BEIJING"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </div>

                    <div class="form-row">
                      <el-form-item
                        label="婚姻状况"
                        prop="marital_status"
                        class="el-form-item--small"
                        required
                      >
                        <el-select
                          v-model="client.marital_status"
                          placeholder="请选择婚姻状况"
                          style="width: 100%"
                        >
                          <el-option label="离异" value="D"></el-option>
                          <el-option label="已婚" value="C"></el-option>
                          <el-option label="单身" value="SO"></el-option>
                          <el-option label="丧偶" value="V"></el-option>
                          <el-option label="分居" value="SE"></el-option>
                          <el-option label="其他" value="OT"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </template>
                </div>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <!-- 订单详情步骤 -->
      <div v-if="currentStep === 2" class="step-content">
        <el-form
          ref="orderDetailsFormRef"
          label-width="140px"
          class="order-details-form"
          :model="{ travel_date }"
        >
          <!-- 期望预约日期 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Calendar /></el-icon>
                期望预约日期
              </h4>
              <el-button
                type="primary"
                size="small"
                @click="addDateRange"
                :disabled="hasSpainVisa(visaType) && dateRangeList.length >= 1"
              >
                <el-icon><Plus /></el-icon>
                添加日期范围
              </el-button>
            </div>

            <div class="date-ranges">
              <div
                v-for="(range, idx) in hasSpainVisa(visaType)
                  ? dateRangeList.slice(0, 1)
                  : dateRangeList"
                :key="idx"
                class="date-range-item"
              >
                <div class="date-range-header">
                  <span class="range-label">日期范围 {{ idx + 1 }}</span>
                  <el-button
                    v-if="dateRangeList.length > 1"
                    type="danger"
                    text
                    size="small"
                    @click="removeDateRange(idx)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
                <el-date-picker
                  v-model="dateRangeList[idx]"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="date-picker"
                />
              </div>
            </div>
          </div>

          <!-- 预约选项 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Setting /></el-icon>
                预约选项
              </h4>
            </div>
            <div class="form-row">
              <el-form-item label="是否接受隔天">
                <el-switch v-model="accept_next_day" />
              </el-form-item>
              <el-form-item v-if="hasSpainVisa(visaType)" label="是否接受VIP">
                <el-switch v-model="accept_vip" />
              </el-form-item>
            </div>
            <el-form-item
              v-if="hasSpainVisa(visaType)"
              label="出行日期"
              prop="travel_date"
              :rules="[
                {
                  required: true,
                  message: '请选择出行日期',
                  trigger: 'change',
                },
              ]"
            >
              <el-date-picker
                v-model="travel_date"
                type="date"
                placeholder="选择出行日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="date-picker"
              />
            </el-form-item>
          </div>

          <!-- 客户来源 - 所有用户可见 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><User /></el-icon>
                客户信息
              </h4>
            </div>
            <el-form-item label="客户来源">
              <el-input
                v-model="customer"
                placeholder="请输入客户来源（选填）"
              />
            </el-form-item>
          </div>

          <!-- 管理员选项 -->
          <div
            v-if="permission == 'admin' || permission == 'kefu'"
            class="form-section"
          >
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Setting /></el-icon>
                管理选项
              </h4>
            </div>
            <el-form-item label="价格">
              <el-input v-model="price" placeholder="请输入价格" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input
                v-model="remark"
                type="textarea"
                placeholder="请输入备注信息"
                :rows="3"
              />
            </el-form-item>
          </div>

          <!-- 非管理员用户的备注 -->
          <div
            v-if="permission !== 'admin' && permission !== 'kefu'"
            class="form-section"
          >
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><EditPen /></el-icon>
                备注信息
              </h4>
            </div>
            <el-form-item label="备注">
              <el-input
                v-model="remark"
                type="textarea"
                placeholder="请输入备注信息"
                :rows="3"
              />
            </el-form-item>
          </div>

          <!-- 拆分添加选项 - 只有多个客户时才显示 -->
          <div v-if="clients.length > 1" class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Operation /></el-icon>
                创建选项
              </h4>
            </div>
            <el-form-item :label="editingOrderId ? '拆分订单' : '拆分添加'">
              <el-checkbox v-model="splitCreate">
                {{
                  editingOrderId
                    ? '将当前订单拆分为每个客户的独立订单'
                    : visaType.length > 1
                    ? `为每个签证类型和客户组合创建独立订单（将创建 ${
                        visaType.length * clients.length
                      } 个订单）`
                    : '为每个客户创建单独的订单'
                }}
              </el-checkbox>
              <div class="split-help-text">
                {{
                  editingOrderId
                    ? '勾选后将删除当前订单，并为每个申请人创建独立订单'
                    : visaType.length > 1 && clients.length > 1
                    ? `每个签证类型与每个客户都会创建独立订单（${
                        visaType.length
                      } 个签证类型 × ${clients.length} 个客户 = ${
                        visaType.length * clients.length
                      } 个订单）`
                    : '勾选后将为每个申请人创建独立订单，便于单独管理'
                }}
              </div>

              <!-- 拆分警告提示 -->
              <div v-if="splitCreate && editingOrderId" class="split-warning">
                <el-icon><Warning /></el-icon>
                <strong>注意：</strong
                >拆分操作将永久删除当前订单，此操作不可撤销。请确认后再提交。
              </div>
            </el-form-item>
          </div>

          <!-- 创建进度 -->
          <div v-if="showProgress" class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Loading /></el-icon>
                创建进度
              </h4>
            </div>
            <div class="progress-container">
              <div
                v-for="(progress, index) in createProgress"
                :key="index"
                class="progress-item"
                :class="{ cancelled: progress.status === 'cancelled' }"
              >
                <div class="progress-header">
                  <span class="client-name">{{ progress.clientName }}</span>
                  <el-tag
                    :type="getProgressTagType(progress.status)"
                    size="small"
                  >
                    {{
                      getProgressStatusText(
                        progress.status,
                        index === 0 && editingOrderId
                      )
                    }}
                  </el-tag>
                </div>
                <el-progress
                  :percentage="progress.percentage"
                  :status="getProgressStatus(progress.status)"
                  :stroke-width="8"
                />
                <div v-if="progress.message" class="progress-message">
                  {{ progress.message }}
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <div class="footer-content">
          <div class="footer-left"></div>
          <div class="footer-right">
            <el-button
              v-if="currentStep > 0"
              @click="currentStep--"
              size="large"
            >
              <el-icon><ArrowLeft /></el-icon>
              上一步
            </el-button>
            <el-button
              v-if="currentStep === 0"
              type="primary"
              @click="validateVisaInfo"
              size="large"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button
              v-else-if="currentStep === 1"
              type="primary"
              @click="validateClientInfo"
              size="large"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button
              v-else
              type="primary"
              @click="submitForm"
              :loading="submitting"
              size="large"
            >
              <el-icon><Check /></el-icon>
              {{ getSubmitButtonText() }}
            </el-button>
          </div>
        </div>
      </div>
    </template>
  </el-drawer>

  <!-- 图片预览 Dialog -->
  <el-dialog
    v-model="passportDialogVisible"
    :title="dialogTitle"
    width="90%"
    :draggable="true"
    :close-on-click-modal="false"
    :fullscreen="false"
    :destroy-on-close="true"
    class="passport-preview-dialog"
  >
    <div class="passport-dialog-content">
      <el-image
        v-if="currentPassportImage"
        :src="getImageUrl(currentPassportImage, imageType)"
        fit="contain"
        class="passport-dialog-image"
      />
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick } from 'vue';
import { onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import http from '@/utils/http';
import {
  DocumentAdd,
  Close,
  Document,
  User,
  Location,
  Setting,
  Calendar,
  Plus,
  Delete,
  ArrowLeft,
  ArrowRight,
  Check,
  Camera,
  Upload,
  Avatar,
  Refresh,
  EditPen,
  Operation,
  Loading,
  Warning,
} from '@element-plus/icons-vue';
import countriesData from '@/assets/country.json';
import { useUserStore } from '@/stores/user';

const userStore = useUserStore();

// 国家列表数据
const countries = ref(countriesData || []);
const permission = userStore.permission;

const cprops = {
  multiple: true, // 改回多选
  value: 'code',
  label: 'name',
};

const props = defineProps({
  visible: Boolean,
  visaOptions: Array,
  editingOrder: Object,
});

const visible = defineModel('visible');
const emit = defineEmits(['update:visible']);
const formRefs = ref([]);
const orderDetailsFormRef = ref(null);
const clients = ref([
  {
    name: '',
    surname_pinyin: '',
    firstname_pinyin: '',
    gender: '',
    dob: '',
    passport: '',
    passport_expire: '',
    passport_image: '',
    avatar_image: '',
    nationality: '',
    passport_date: '',
    sign_location: '',
    bornplace: '',
    marital_status: '',
  },
]);

const activeNames = ref(['0']);
const currentStep = ref(0);
const visaType = ref([]); // 改回数组
const dateRangeList = ref([[]]);
const accept_next_day = ref(false);
const accept_vip = ref(false);
const travel_date = ref('');
const customer = ref('');

// 拖拽状态管理
const dragStates = reactive({});
const price = ref('');
const remark = ref('');
const uploading = ref(false);
const submitting = ref(false);
const editingOrderId = ref(null);

// 拆分添加相关
const splitCreate = ref(false);
const showProgress = ref(false);
const createProgress = ref([]);

// 图片预览 Dialog
const passportDialogVisible = ref(false);
const currentPassportImage = ref('');
const dialogTitle = ref('图片预览');
const imageType = ref('passport'); // 'passport' 或 'avatar'

// 动态验证规则
const getRules = () => {
  const baseRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    surname_pinyin: [
      { required: true, message: '请输入姓拼音', trigger: 'blur' },
    ],
    firstname_pinyin: [
      { required: true, message: '请输入名拼音', trigger: 'blur' },
    ],
    passport: [{ required: true, message: '请输入护照号码', trigger: 'blur' }],
    passport_date: [
      { required: true, message: '请选择护照签发日期', trigger: 'change' },
    ],
    passport_expire: [
      { required: true, message: '请选择护照过期日期', trigger: 'change' },
    ],
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    dob: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
    nationality: [{ required: true, message: '请选择国籍', trigger: 'change' }],
  };

  // 如果选择的是西班牙签证，添加额外必填项
  const hasSpain = hasSpainVisa(visaType.value);
  if (hasSpain) {
    baseRules.sign_location = [
      { required: true, message: '请输入签发地点', trigger: 'blur' },
    ];
    baseRules.bornplace = [
      { required: true, message: '请输入出生地点', trigger: 'blur' },
    ];
    baseRules.marital_status = [
      { required: true, message: '请选择婚姻状况', trigger: 'change' },
    ];
  }

  return baseRules;
};

// 使用计算属性来动态获取验证规则
const rules = computed(() => getRules());

// 护照OCR结果解析函数
const parsePassportOCR = ocrResult => {
  const passportInfo = {
    name: '',
    surname_pinyin: '',
    firstname_pinyin: '',
    passport: '',
    gender: '',
    dob: '',
    passport_expire: '',
    nationality: '',
    passport_date: '',
    sign_location: '',
    bornplace: '',
  };

  if (!ocrResult || typeof ocrResult !== 'object') {
    return passportInfo;
  }

  // 百度OCR护照识别返回的是一个对象，每个字段都有对应的key
  try {
    // 姓名
    if (ocrResult['姓名']?.words) {
      passportInfo.name = ocrResult['姓名'].words.trim();
    }

    // 姓名拼音 - 格式通常是 "CAI,YIYANG"
    if (ocrResult['姓名拼音']?.words) {
      const pinyinName = ocrResult['姓名拼音'].words.trim();
      if (pinyinName.includes(',')) {
        const parts = pinyinName.split(',');
        passportInfo.surname_pinyin = parts[0].trim();
        passportInfo.firstname_pinyin = parts[1].trim();
      } else {
        // 如果没有逗号分隔，尝试空格分隔
        const parts = pinyinName.split(/\s+/);
        if (parts.length >= 2) {
          passportInfo.surname_pinyin = parts[0];
          passportInfo.firstname_pinyin = parts.slice(1).join(' ');
        }
      }
    }

    // 护照号码
    if (ocrResult['护照号码']?.words) {
      passportInfo.passport = ocrResult['护照号码'].words.trim();
    }

    // 性别 - 格式通常是 "女/F" 或 "男/M"
    if (ocrResult['性别']?.words) {
      const genderText = ocrResult['性别'].words.trim();
      if (genderText.includes('女') || genderText.includes('F')) {
        passportInfo.gender = '女';
      } else if (genderText.includes('男') || genderText.includes('M')) {
        passportInfo.gender = '男';
      }
    }

    // 生日 - 格式通常是 "19860422"
    if (ocrResult['生日']?.words) {
      const birthday = ocrResult['生日'].words.trim();
      passportInfo.dob = formatDateFromOCR(birthday);
    }

    // 有效期至 - 格式通常是 "20280326"
    if (ocrResult['有效期至']?.words) {
      const expiry = ocrResult['有效期至'].words.trim();
      passportInfo.passport_expire = formatDateFromOCR(expiry);
    }

    // 签发日期 - 格式通常是 "20180327"
    if (ocrResult['签发日期']?.words) {
      const issueDate = ocrResult['签发日期'].words.trim();
      passportInfo.passport_date = formatDateFromOCR(issueDate);
    }

    // 护照签发地点
    if (ocrResult['护照签发地点']?.words) {
      passportInfo.sign_location = ocrResult['护照签发地点'].words.trim();
    }

    // 出生地点
    if (ocrResult['出生地点']?.words) {
      passportInfo.bornplace = ocrResult['出生地点'].words.trim();
    }

    // 国籍 - 需要映射到对应的ISO代码
    if (ocrResult['国籍']?.words) {
      const nationalityText = ocrResult['国籍'].words.trim();
      // 如果包含"中国"或"CHINESE"，设置为CHN
      if (
        nationalityText.includes('中国') ||
        nationalityText.includes('CHINESE')
      ) {
        passportInfo.nationality = 'CHN';
      } else {
        // 其他情况保留原文，后续可以手动选择
        passportInfo.nationality = nationalityText;
      }
    }
  } catch (error) {
    console.error('解析护照OCR结果时出错:', error);
  }

  return passportInfo;
};

// OCR日期格式化函数 - 处理 "20180327" 格式
const formatDateFromOCR = dateStr => {
  if (!dateStr || dateStr.length !== 8) {
    return dateStr;
  }

  // 将 "20180327" 转换为 "2018-03-27"
  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);

  return `${year}-${month}-${day}`;
};

// 通用日期格式化函数
const formatDate = dateStr => {
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return dateStr; // 已经是 YYYY-MM-DD 格式
  }
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
    // DD/MM/YYYY 转换为 YYYY-MM-DD
    const parts = dateStr.split('/');
    return `${parts[2]}-${parts[1]}-${parts[0]}`;
  }
  return dateStr;
};

// 获取客户状态文本
const getClientStatusText = client => {
  if (client.name && client.passport) {
    return '信息完整';
  } else if (client.name || client.passport) {
    return '信息不完整';
  } else {
    return '待填写信息';
  }
};

// 获取客户状态样式类
const getClientStatusClass = client => {
  if (client.name && client.passport) {
    return 'status-complete';
  } else if (client.name || client.passport) {
    return 'status-partial';
  } else {
    return 'status-empty';
  }
};

// 辅助函数：确保 visaType 是正确的数组格式
const ensureVisaTypeArray = visaTypeValue => {
  console.log('ensureVisaTypeArray 输入:', visaTypeValue);

  if (!visaTypeValue) return [];

  // 如果不是数组，包装成数组
  if (!Array.isArray(visaTypeValue)) {
    console.log('不是数组，包装成数组');
    return [visaTypeValue];
  }

  // Element Plus cascader 在多选模式下的特殊处理
  // 单选时可能返回: [["che","CDU","C","C11"]] (正确)
  // 多选时可能返回: [["che","CDU","C","C11"], ["fra","PAR","B","B12"]] (正确)
  // 但有时候单选会返回: [[["che","CDU","C","C11"]]] (错误，三层嵌套)

  if (visaTypeValue.length === 0) {
    console.log('空数组，直接返回');
    return [];
  }

  // 检查第一个元素
  const firstElement = visaTypeValue[0];
  console.log(
    '第一个元素:',
    firstElement,
    '是否数组:',
    Array.isArray(firstElement)
  );

  if (Array.isArray(firstElement)) {
    // 第一个元素是数组，检查是否是三层嵌套
    if (firstElement.length > 0 && Array.isArray(firstElement[0])) {
      const firstSubElement = firstElement[0];
      console.log(
        '第一个子元素:',
        firstSubElement,
        '是否数组:',
        Array.isArray(firstSubElement)
      );

      // 如果第一个子元素还是数组，且包含字符串，说明是三层嵌套
      if (
        Array.isArray(firstSubElement) &&
        firstSubElement.length > 0 &&
        typeof firstSubElement[0] === 'string'
      ) {
        console.log('检测到三层嵌套，修正为二层嵌套');
        // 三层嵌套 [[[...]]] -> [[...]]
        return firstElement;
      }
    }

    // 正常的二层嵌套，直接返回
    console.log('正常的二层嵌套，直接返回');
    return visaTypeValue;
  } else {
    // 第一个元素不是数组，说明是一维数组，需要包装
    console.log('一维数组，包装成二维数组');
    return [visaTypeValue];
  }
};

// 辅助函数：检查是否包含西班牙签证
const hasSpainVisa = visaTypeValue => {
  const visaArray = ensureVisaTypeArray(visaTypeValue);
  return visaArray.some(v => v && v[0] && v[0].toLowerCase().includes('spain'));
};

// 处理签证类型变化
const handleVisaTypeChange = value => {
  console.log('=== Cascader Change Event ===');
  console.log('Cascader 原始值:', JSON.stringify(value, null, 2));
  console.log('原始值类型:', typeof value);
  console.log('是否数组:', Array.isArray(value));

  if (Array.isArray(value) && value.length > 0) {
    console.log('第一个元素:', JSON.stringify(value[0], null, 2));
    console.log('第一个元素类型:', typeof value[0]);
    console.log('第一个元素是否数组:', Array.isArray(value[0]));

    if (Array.isArray(value[0]) && value[0].length > 0) {
      console.log('第二层第一个元素:', JSON.stringify(value[0][0], null, 2));
      console.log('第二层第一个元素类型:', typeof value[0][0]);
      console.log('第二层第一个元素是否数组:', Array.isArray(value[0][0]));
    }
  }

  // 直接赋值，不进行任何转换，先看看原始数据是什么样的
  visaType.value = value;

  console.log(
    '赋值后 visaType.value:',
    JSON.stringify(visaType.value, null, 2)
  );
  console.log('=== End Cascader Change Event ===');
};

// 添加客户
const addClient = () => {
  try {
    // 确保 visaType 是有效的数组格式
    const currentVisaTypes = ensureVisaTypeArray(visaType.value);

    // 检查是否选择了签证类型
    if (currentVisaTypes.length === 0) {
      ElMessage.warning('请先选择签证类型再添加客户');
      return;
    }

    clients.value.push({
      name: '',
      surname_pinyin: '',
      firstname_pinyin: '',
      gender: '',
      dob: '',
      passport: '',
      passport_expire: '',
      passport_image: '',
      avatar_image: '',
      nationality: '',
      passport_date: '',
      sign_location: '',
      bornplace: '',
      marital_status: '',
    });
    activeNames.value = [String(clients.value.length - 1)];

    console.log('添加客户成功', {
      客户数量: clients.value.length,
      签证类型: currentVisaTypes,
      是否西班牙签证: hasSpainVisa(visaType.value),
    });
  } catch (error) {
    console.error('添加客户时出错:', error);
    ElMessage.error('添加客户失败，请重试');
  }
};

// 删除客户
const removeClientByIndex = index => {
  if (clients.value.length > 1) {
    clients.value.splice(index, 1);
    activeNames.value = ['0'];
  }
};

// 添加日期范围
const addDateRange = () => {
  if (!hasSpainVisa(visaType.value)) {
    dateRangeList.value.push([]);
  }
};

// 删除日期范围
const removeDateRange = index => {
  if (dateRangeList.value.length > 1) {
    dateRangeList.value.splice(index, 1);
  }
};

// 图片压缩函数 - 压缩到指定大小以下
const compressImageToSize = (file, maxSizeKB = 100) => {
  return new Promise((resolve, reject) => {
    const maxSize = maxSizeKB * 1024; // 转换为字节
    
    // 如果文件已经小于目标大小，直接返回
    if (file.size <= maxSize) {
      resolve(file);
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // 设置画布尺寸
        canvas.width = img.width;
        canvas.height = img.height;

        // 绘制白色背景
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制图片
        ctx.drawImage(img, 0, 0);

        // 二分法查找合适的压缩质量
        let minQuality = 0.1;
        let maxQuality = 0.95;
        let attempts = 0;
        const maxAttempts = 10;

        const tryCompress = (quality) => {
          canvas.toBlob(
            blob => {
              attempts++;
              
              if (!blob) {
                reject(new Error('图片压缩失败'));
                return;
              }

              console.log(`压缩尝试 ${attempts}: 质量=${quality.toFixed(2)}, 大小=${(blob.size/1024).toFixed(1)}KB`);

              if (blob.size <= maxSize || attempts >= maxAttempts) {
                // 达到目标大小或达到最大尝试次数
                const originalName = file.name.replace(/\.[^/.]+$/, '');
                const compressedFile = new File([blob], `${originalName}.jpg`, {
                  type: 'image/jpeg',
                  lastModified: Date.now(),
                });

                console.log(`头像压缩完成: ${file.name} -> ${compressedFile.name}`, {
                  原始大小: `${(file.size / 1024).toFixed(1)}KB`,
                  压缩后大小: `${(compressedFile.size / 1024).toFixed(1)}KB`,
                  压缩质量: quality.toFixed(2),
                  压缩比: `${((1 - compressedFile.size / file.size) * 100).toFixed(1)}%`
                });

                URL.revokeObjectURL(img.src);
                resolve(compressedFile);
              } else if (blob.size > maxSize) {
                // 文件还是太大，降低质量
                maxQuality = quality;
                const newQuality = (minQuality + maxQuality) / 2;
                tryCompress(newQuality);
              } else {
                // 文件太小，可以尝试提高质量（可选）
                URL.revokeObjectURL(img.src);
                resolve(new File([blob], `${file.name.replace(/\.[^/.]+$/, '')}.jpg`, {
                  type: 'image/jpeg',
                  lastModified: Date.now(),
                }));
              }
            },
            'image/jpeg',
            quality
          );
        };

        // 开始压缩
        tryCompress(0.8);
      } catch (error) {
        URL.revokeObjectURL(img.src);
        reject(new Error(`图片处理失败: ${error.message}`));
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new Error('图片加载失败，可能文件已损坏'));
    };

    // 创建图片URL
    img.src = URL.createObjectURL(file);
  });
};

// 图片格式转换工具函数
const convertImageToJpeg = file => {
  return new Promise((resolve, reject) => {
    // 检查文件大小，如果太大则降低质量
    const maxSize = 5 * 1024 * 1024; // 5MB
    const quality = file.size > maxSize ? 0.85 : 0.95;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // 设置画布尺寸
        canvas.width = img.width;
        canvas.height = img.height;

        // 绘制白色背景（PNG透明背景转为白色）
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制图片
        ctx.drawImage(img, 0, 0);

        // 转换为JPEG格式的Blob
        canvas.toBlob(
          blob => {
            // 清理资源
            URL.revokeObjectURL(img.src);

            if (blob) {
              // 创建新的File对象，文件名改为.jpg
              const originalName = file.name.replace(/\.[^/.]+$/, '');
              const convertedFile = new File([blob], `${originalName}.jpg`, {
                type: 'image/jpeg',
                lastModified: Date.now(),
              });

              console.log(
                `图片转换成功: ${file.name} -> ${convertedFile.name}`,
                {
                  原始大小: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
                  转换后大小: `${(convertedFile.size / 1024 / 1024).toFixed(
                    2
                  )}MB`,
                  压缩质量: quality,
                }
              );

              resolve(convertedFile);
            } else {
              reject(new Error('图片转换失败'));
            }
          },
          'image/jpeg',
          quality
        );
      } catch (error) {
        URL.revokeObjectURL(img.src);
        reject(new Error(`图片处理失败: ${error.message}`));
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new Error('图片加载失败，可能文件已损坏'));
    };

    // 创建图片URL
    img.src = URL.createObjectURL(file);
  });
};

// 护照上传和OCR识别
const customUpload = async (options, index) => {
  uploading.value = true;
  try {
    let file = options.file;

    // 如果是PNG格式，转换为JPG
    if (file.type === 'image/png') {
      console.log('检测到PNG格式，正在转换为JPG...');
      try {
        file = await convertImageToJpeg(file);
        console.log('PNG转JPG成功');
        ElMessage.success('PNG图片已自动转换为JPG格式');
      } catch (error) {
        console.error('PNG转JPG失败:', error);
        ElMessage.warning('图片格式转换失败，将使用原始格式上传');
      }
    }

    // 将文件转换为base64
    const reader = new FileReader();

    const base64Promise = new Promise((resolve, reject) => {
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });

    const base64Data = await base64Promise;

    // 调用OCR识别接口
    console.log('调用护照OCR接口...');
    const res = await http.post('/api/ocr-passport', {
      image: base64Data,
    });
    console.log('OCR接口响应:', res);

    if (res.code === 1) {
      // 保存文件名
      if (res.filename) {
        clients.value[index].passport_image = res.filename;
      }

      // 自动填充护照信息
      if (res.result) {
        const ocrResult = res.result;
        console.log('OCR识别结果:', ocrResult);

        // 解析护照信息
        const passportInfo = parsePassportOCR(ocrResult);
        console.log('解析后的护照信息:', passportInfo);
        console.log('当前签证类型:', visaType.value);

        // 自动填充表单
        if (passportInfo.name) {
          clients.value[index].name = passportInfo.name;
        }
        if (passportInfo.nationality) {
          clients.value[index].nationality = passportInfo.nationality;
        }
        if (passportInfo.passport_date) {
          clients.value[index].passport_date = passportInfo.passport_date;
        }
        if (passportInfo.sign_location) {
          clients.value[index].sign_location = passportInfo.sign_location;
        }
        if (passportInfo.bornplace) {
          clients.value[index].bornplace = passportInfo.bornplace;
        }
        if (passportInfo.surname_pinyin) {
          clients.value[index].surname_pinyin = passportInfo.surname_pinyin;
        }
        if (passportInfo.firstname_pinyin) {
          clients.value[index].firstname_pinyin = passportInfo.firstname_pinyin;
        }
        if (passportInfo.passport) {
          clients.value[index].passport = passportInfo.passport;
        }
        if (passportInfo.gender) {
          clients.value[index].gender = passportInfo.gender;
        }
        if (passportInfo.dob) {
          clients.value[index].dob = passportInfo.dob;
        }
        if (passportInfo.passport_expire) {
          clients.value[index].passport_expire = passportInfo.passport_expire;
        }
        if (passportInfo.nationality) {
          clients.value[index].nationality = passportInfo.nationality;
        }

        ElMessage.success('护照识别成功，信息已自动填充');
      } else {
        ElMessage.success('护照上传成功');
      }
    } else {
      ElMessage.error(res.message || '识别失败');
    }
  } catch (error) {
    console.error('护照上传错误:', error);
    ElMessage.error('上传失败');
  } finally {
    uploading.value = false;
  }
};

// 头像上传
const handleAvatarUpload = async (options, index) => {
  uploading.value = true;
  try {
    let file = options.file;
    let processSteps = [];

    // 1. 如果是PNG格式，转换为JPG
    if (file.type === 'image/png') {
      console.log('检测到PNG格式头像，正在转换为JPG...');
      processSteps.push('格式转换');
      try {
        file = await convertImageToJpeg(file);
        console.log('头像PNG转JPG成功');
      } catch (error) {
        console.error('头像PNG转JPG失败:', error);
        ElMessage.warning('头像格式转换失败，将使用原始格式上传');
      }
    }

    // 2. 检查文件大小，如果大于100KB则压缩
    const fileSizeKB = file.size / 1024;
    if (fileSizeKB > 100) {
      console.log(`头像文件大小 ${fileSizeKB.toFixed(1)}KB，需要压缩到100KB以下...`);
      processSteps.push('大小压缩');
      try {
        file = await compressImageToSize(file, 100);
        console.log('头像压缩成功');
      } catch (error) {
        console.error('头像压缩失败:', error);
        ElMessage.warning('头像压缩失败，将使用原始文件上传');
      }
    }

    // 显示处理步骤的提示
    if (processSteps.length > 0) {
      ElMessage.success(`头像处理完成: ${processSteps.join(' + ')}`);
    }

    const formData = new FormData();
    formData.append('file', file);

    const res = await http.post('/api/upload-avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (res.code === 1 && res.filename) {
      clients.value[index].avatar_image = res.filename;
      ElMessage.success(`头像上传成功 (${(file.size / 1024).toFixed(1)}KB)`);
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('上传失败');
  } finally {
    uploading.value = false;
  }
};

// 显示护照预览 Dialog
const showPassportDialog = filename => {
  currentPassportImage.value = filename;
  dialogTitle.value = '护照预览';
  imageType.value = 'passport';
  passportDialogVisible.value = true;
};

// 重新上传护照
const reuploadPassport = index => {
  clients.value[index].passport_image = '';
  ElMessage.info('请重新上传护照照片');
};

// 重新上传头像
const reuploadAvatar = index => {
  clients.value[index].avatar_image = '';
  ElMessage.info('请重新上传头像照片');
};

// 拖拽事件处理
const handleDragEnter = (event, key) => {
  event.preventDefault();
  dragStates[key] = true;
};

const handleDragOver = (event, key) => {
  event.preventDefault();
  dragStates[key] = true;
};

const handleDragLeave = (event, key) => {
  event.preventDefault();
  // 只有当离开的是当前元素本身时才移除拖拽状态
  if (event.target === event.currentTarget) {
    dragStates[key] = false;
  }
};

const handleDrop = async (event, clientIndex, uploadType) => {
  event.preventDefault();
  const key = `${uploadType}-${clientIndex}`;
  dragStates[key] = false;

  const files = event.dataTransfer.files;
  if (files.length === 0) return;

  const file = files[0];

  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('只支持 JPG、JPEG、PNG 格式的图片');
    return;
  }

  // 检查文件大小 (10MB)
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过 10MB');
    return;
  }

  // 创建文件对象模拟上传
  const uploadOptions = {
    file: file,
    filename: file.name,
  };

  try {
    if (uploadType === 'passport') {
      await customUpload(uploadOptions, clientIndex);
    } else if (uploadType === 'avatar') {
      await handleAvatarUpload(uploadOptions, clientIndex);
    }
  } catch (error) {
    console.error('拖拽上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 显示头像预览 Dialog
const showAvatarDialog = filename => {
  currentPassportImage.value = filename;
  dialogTitle.value = '头像预览';
  imageType.value = 'avatar';
  passportDialogVisible.value = true;
};

// 获取图片URL
const getImageUrl = (filename, type) => {
  const baseUrl = 'http://visa.qianwuyouvisa.com:5005/api';
  if (type === 'avatar') {
    return `${baseUrl}/avatar_image/${filename}`;
  } else {
    return `${baseUrl}/passport_image/${filename}`;
  }
};

// 签证信息校验
const validateVisaInfo = () => {
  // 确保 visaType 格式正确
  visaType.value = ensureVisaTypeArray(visaType.value);

  console.log('签证信息校验 - 格式检查:', {
    原始值: visaType.value,
    是否数组: Array.isArray(visaType.value),
    长度: visaType.value?.length,
    第一个元素: visaType.value?.[0],
  });

  if (!visaType.value.length) {
    ElMessage.warning('请选择签证类型');
    return;
  }
  currentStep.value = 1;
};

// 获取提交按钮文案
const getSubmitButtonText = () => {
  if (editingOrderId.value) {
    return splitCreate.value ? '拆分订单' : '更新订单';
  } else {
    return splitCreate.value ? '拆分创建' : '创建订单';
  }
};

// 进度相关辅助函数
const getProgressTagType = status => {
  switch (status) {
    case 'success':
      return 'success';
    case 'error':
      return 'danger';
    case 'processing':
      return 'warning';
    case 'cancelled':
      return '';
    default:
      return 'info';
  }
};

const getProgressStatusText = (status, isDeleteStep = false) => {
  if (isDeleteStep) {
    switch (status) {
      case 'success':
        return '删除成功';
      case 'error':
        return '删除失败';
      case 'processing':
        return '删除中...';
      case 'cancelled':
        return '已取消';
      default:
        return '等待删除';
    }
  } else {
    switch (status) {
      case 'success':
        return '创建成功';
      case 'error':
        return '创建失败';
      case 'processing':
        return '创建中...';
      case 'cancelled':
        return '已取消';
      default:
        return '等待中';
    }
  }
};

const getProgressStatus = status => {
  switch (status) {
    case 'success':
      return 'success';
    case 'error':
      return 'exception';
    case 'cancelled':
      return 'exception';
    default:
      return null;
  }
};

// 客户信息校验
const validateClientInfo = async () => {
  // 使用 Element Plus 表单验证
  let allValid = true;

  for (let i = 0; i < formRefs.value.length; i++) {
    const form = formRefs.value[i];
    if (form) {
      try {
        await form.validate();
      } catch (error) {
        allValid = false;
        // 展开有错误的表单项
        activeNames.value = [String(i)];
        ElMessage.error(`请完善第${i + 1}个客户的信息`);
        break;
      }
    }
  }

  if (allValid) {
    currentStep.value = 2;
  }
};

// 提交表单
const submitForm = async () => {
  // 确保 visaType 格式正确
  visaType.value = ensureVisaTypeArray(visaType.value);

  console.log('提交表单 - 签证类型格式检查:', {
    原始值: visaType.value,
    是否数组: Array.isArray(visaType.value),
    长度: visaType.value?.length,
    第一个元素: visaType.value?.[0],
  });

  // 验证期望日期
  if (
    !dateRangeList.value.length ||
    dateRangeList.value.some(r => !r || r.length !== 2)
  ) {
    ElMessage.warning('请填写完整的期望日期段');
    return;
  }

  // 验证第三步表单（包括出行日期）
  if (orderDetailsFormRef.value) {
    try {
      await orderDetailsFormRef.value.validate();
    } catch (error) {
      ElMessage.warning('请完善表单信息');
      return;
    }
  }

  submitting.value = true;

  try {
    if (editingOrderId.value) {
      // 编辑模式，使用原有逻辑
      await updateOrder();
    } else if (
      splitCreate.value &&
      visaType.value.length > 1 &&
      clients.value.length > 1
    ) {
      // 同时选择了多个签证类型、多个客户，并且勾选了拆分，创建所有组合
      await createSplitOrdersByCombination();
    } else if (visaType.value && visaType.value.length > 1) {
      // 多选签证类型时，按签证类型拆分
      await createSplitOrdersByVisaType();
    } else if (splitCreate.value) {
      // 单选签证类型但手动选择拆分时，按客户拆分
      await createSplitOrders();
    } else {
      // 普通创建模式
      await createSingleOrder();
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('网络错误，请重试');
  } finally {
    submitting.value = false;
  }
};

// 更新订单
const updateOrder = async () => {
  if (splitCreate.value) {
    // 拆分订单模式：先删除当前订单，再创建多个独立订单
    await splitExistingOrder();
  } else {
    // 普通更新模式
    await updateSingleOrder();
  }
};

// 普通更新订单
const updateSingleOrder = async () => {
  console.log(
    'updateSingleOrder - visaType.value:',
    JSON.stringify(visaType.value, null, 2)
  );

  const orderData = {
    order_id: editingOrderId.value,
    visaType: visaType.value || [], // 直接使用，不再包装额外数组层
    dateRangeList: dateRangeList.value,
    accept_next_day: accept_next_day.value,
    accept_vip: accept_vip.value,
    travel_date: travel_date.value,
    customer: customer.value,
    price: price.value,
    remark: remark.value,
    clients: clients.value,
  };

  const res = await http.post('/api/edit_order', orderData);

  if (res.code === 1) {
    ElMessage.success('订单更新成功');
    emit('update:visible', false);
    resetForm();
  } else {
    ElMessage.error(res.message || '更新失败');
  }
};

// 拆分现有订单
const splitExistingOrder = async () => {
  showProgress.value = true;

  // 初始化进度，包含删除步骤
  createProgress.value = [
    {
      clientName: '删除原订单',
      status: 'waiting',
      percentage: 0,
      message: '',
    },
    ...clients.value.map((client, index) => ({
      clientName: client.name || `申请人 ${index + 1}`,
      status: 'waiting',
      percentage: 0,
      message: '',
    })),
  ];

  let successCount = 0;
  let failCount = 0;

  try {
    // 第一步：删除原订单
    createProgress.value[0].status = 'processing';
    createProgress.value[0].percentage = 50;
    createProgress.value[0].message = '正在删除原订单...';

    const deleteRes = await http.post('/api/delete_order', {
      order_id: editingOrderId.value,
    });

    if (deleteRes.code === 1) {
      createProgress.value[0].status = 'success';
      createProgress.value[0].percentage = 100;
      createProgress.value[0].message = '原订单删除成功';
    } else {
      createProgress.value[0].status = 'error';
      createProgress.value[0].percentage = 100;
      createProgress.value[0].message = deleteRes.message || '删除原订单失败';

      // 删除失败时，将所有后续步骤标记为取消
      for (let i = 1; i < createProgress.value.length; i++) {
        createProgress.value[i].status = 'cancelled';
        createProgress.value[i].percentage = 0;
        createProgress.value[i].message = '因删除原订单失败而取消';
      }

      ElMessage.error('删除原订单失败，拆分操作终止');
      return;
    }

    // 延迟一下让用户看到删除进度
    await new Promise(resolve => setTimeout(resolve, 500));

    // 第二步：为每个客户创建新订单
    for (let i = 0; i < clients.value.length; i++) {
      const client = clients.value[i];
      const progressIndex = i + 1; // 因为第0个是删除进度

      // 更新进度为处理中
      createProgress.value[progressIndex].status = 'processing';
      createProgress.value[progressIndex].percentage = 50;
      createProgress.value[progressIndex].message = '正在创建订单...';

      try {
        const orderData = {
          visaType: visaType.value,
          dateRangeList: dateRangeList.value,
          accept_next_day: accept_next_day.value,
          accept_vip: accept_vip.value,
          travel_date: travel_date.value,
          customer: customer.value,
          price: price.value,
          remark: remark.value,
          clients: [client], // 只包含当前客户
        };

        const res = await http.post('/api/add_order', orderData);

        if (res.code === 1) {
          createProgress.value[progressIndex].status = 'success';
          createProgress.value[progressIndex].percentage = 100;
          createProgress.value[progressIndex].message = `订单创建成功 (${
            res.data?.order_id || ''
          })`;
          successCount++;
        } else {
          createProgress.value[progressIndex].status = 'error';
          createProgress.value[progressIndex].percentage = 100;
          createProgress.value[progressIndex].message =
            res.message || '创建失败';
          failCount++;
        }
      } catch (error) {
        createProgress.value[progressIndex].status = 'error';
        createProgress.value[progressIndex].percentage = 100;
        createProgress.value[progressIndex].message = '网络错误';
        failCount++;
      }

      // 添加延迟，让用户看到进度变化
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 显示最终结果
    if (failCount === 0) {
      ElMessage.success(`订单拆分成功！共创建 ${successCount} 个独立订单`);
      setTimeout(() => {
        emit('update:visible', false);
        resetForm();
      }, 2000);
    } else if (successCount === 0) {
      ElMessage.error(`所有订单创建失败！原订单已删除`);
    } else {
      ElMessage.warning(
        `部分订单创建成功！成功 ${successCount} 个，失败 ${failCount} 个`
      );
    }
  } catch (error) {
    createProgress.value[0].status = 'error';
    createProgress.value[0].percentage = 100;
    createProgress.value[0].message = '删除原订单时发生错误';

    // 网络错误时，也将所有后续步骤标记为取消
    for (let i = 1; i < createProgress.value.length; i++) {
      createProgress.value[i].status = 'cancelled';
      createProgress.value[i].percentage = 0;
      createProgress.value[i].message = '因删除原订单失败而取消';
    }

    ElMessage.error('拆分订单失败：' + (error.message || '网络错误'));
  }
};

// 创建单个订单
const createSingleOrder = async () => {
  console.log(
    'createSingleOrder - visaType.value:',
    JSON.stringify(visaType.value, null, 2)
  );

  const orderData = {
    visaType: visaType.value || [], // 直接使用，不再包装额外数组层
    dateRangeList: dateRangeList.value,
    accept_next_day: accept_next_day.value,
    accept_vip: accept_vip.value,
    travel_date: travel_date.value,
    customer: customer.value,
    price: price.value,
    remark: remark.value,
    clients: clients.value,
  };

  const res = await http.post('/api/add_order', orderData);

  if (res.code === 1) {
    ElMessage.success('订单创建成功');
    emit('update:visible', false);
    resetForm();
  } else {
    ElMessage.error(res.message || '创建失败');
  }
};

// 拆分创建订单
const createSplitOrders = async () => {
  showProgress.value = true;

  // 初始化进度
  createProgress.value = clients.value.map((client, index) => ({
    clientName: client.name || `申请人 ${index + 1}`,
    status: 'waiting',
    percentage: 0,
    message: '',
  }));

  let successCount = 0;
  let failCount = 0;

  for (let i = 0; i < clients.value.length; i++) {
    const client = clients.value[i];

    // 更新进度为处理中
    createProgress.value[i].status = 'processing';
    createProgress.value[i].percentage = 50;
    createProgress.value[i].message = '正在创建订单...';

    try {
      const orderData = {
        visaType: visaType.value,
        dateRangeList: dateRangeList.value,
        accept_next_day: accept_next_day.value,
        accept_vip: accept_vip.value,
        travel_date: travel_date.value,
        customer: customer.value,
        price: price.value,
        remark: remark.value,
        clients: [client], // 只包含当前客户
      };

      const res = await http.post('/api/add_order', orderData);

      if (res.code === 1) {
        createProgress.value[i].status = 'success';
        createProgress.value[i].percentage = 100;
        createProgress.value[i].message = `订单创建成功 (${
          res.data?.order_id || ''
        })`;
        successCount++;
      } else {
        createProgress.value[i].status = 'error';
        createProgress.value[i].percentage = 100;
        createProgress.value[i].message = res.message || '创建失败';
        failCount++;
      }
    } catch (error) {
      createProgress.value[i].status = 'error';
      createProgress.value[i].percentage = 100;
      createProgress.value[i].message = '网络错误';
      failCount++;
    }

    // 添加延迟，让用户看到进度变化
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // 显示最终结果
  if (failCount === 0) {
    ElMessage.success(`所有订单创建成功！共创建 ${successCount} 个订单`);
    setTimeout(() => {
      emit('update:visible', false);
      resetForm();
    }, 2000);
  } else if (successCount === 0) {
    ElMessage.error(`所有订单创建失败！`);
  } else {
    ElMessage.warning(
      `部分订单创建成功！成功 ${successCount} 个，失败 ${failCount} 个`
    );
  }
};

// 按签证类型拆分创建订单
const createSplitOrdersByVisaType = async () => {
  showProgress.value = true;

  // 初始化进度
  createProgress.value = visaType.value.map((visa, index) => ({
    clientName: `${visa[0]} - ${visa[2]}`, // 显示国家代码和签证类型
    status: 'waiting',
    percentage: 0,
    message: '',
  }));

  let successCount = 0;
  let failCount = 0;

  for (let i = 0; i < visaType.value.length; i++) {
    const currentVisaType = visaType.value[i];

    // 更新进度为处理中
    createProgress.value[i].status = 'processing';
    createProgress.value[i].percentage = 50;
    createProgress.value[i].message = '正在创建订单...';

    try {
      const orderData = {
        visaType: [currentVisaType], // 每个订单只包含一个签证类型
        dateRangeList: dateRangeList.value,
        accept_next_day: accept_next_day.value,
        accept_vip: accept_vip.value,
        travel_date: travel_date.value,
        customer: customer.value,
        price: price.value,
        remark: remark.value,
        clients: clients.value, // 所有客户
      };

      const res = await http.post('/api/add_order', orderData);

      if (res.code === 1) {
        createProgress.value[i].status = 'success';
        createProgress.value[i].percentage = 100;
        createProgress.value[i].message = `订单创建成功 (${
          res.data?.order_id || ''
        })`;
        successCount++;
      } else {
        createProgress.value[i].status = 'error';
        createProgress.value[i].percentage = 100;
        createProgress.value[i].message = res.message || '创建失败';
        failCount++;
      }
    } catch (error) {
      createProgress.value[i].status = 'error';
      createProgress.value[i].percentage = 100;
      createProgress.value[i].message = '网络错误';
      failCount++;
    }

    // 添加延迟，让用户看到进度变化
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // 显示最终结果
  if (failCount === 0) {
    ElMessage.success(`所有 ${successCount} 个订单创建成功！`);
    emit('update:visible', false);
    resetForm();
  } else if (successCount === 0) {
    ElMessage.error(`所有订单创建失败！`);
  } else {
    ElMessage.warning(
      `部分订单创建成功！成功 ${successCount} 个，失败 ${failCount} 个`
    );
  }
};

// 按签证类型和客户组合拆分创建订单
const createSplitOrdersByCombination = async () => {
  showProgress.value = true;

  // 初始化进度 - 每个签证类型×每个客户的组合
  const combinations = [];
  for (let i = 0; i < visaType.value.length; i++) {
    for (let j = 0; j < clients.value.length; j++) {
      combinations.push({
        visa: visaType.value[i],
        client: clients.value[j],
        visaIndex: i,
        clientIndex: j,
      });
    }
  }

  createProgress.value = combinations.map((combo, index) => ({
    clientName: `${combo.visa[0]}-${combo.visa[2]} / ${
      combo.client.name || `申请人${combo.clientIndex + 1}`
    }`,
    status: 'waiting',
    percentage: 0,
    message: '',
  }));

  let successCount = 0;
  let failCount = 0;

  for (let i = 0; i < combinations.length; i++) {
    const combo = combinations[i];

    // 更新进度为处理中
    createProgress.value[i].status = 'processing';
    createProgress.value[i].percentage = 50;
    createProgress.value[i].message = '正在创建订单...';

    try {
      const orderData = {
        visaType: [combo.visa], // 单个签证类型
        dateRangeList: dateRangeList.value,
        accept_next_day: accept_next_day.value,
        accept_vip: accept_vip.value,
        travel_date: travel_date.value,
        customer: customer.value,
        price: price.value,
        remark: remark.value,
        clients: [combo.client], // 单个客户
      };

      const res = await http.post('/api/add_order', orderData);

      if (res.code === 1) {
        createProgress.value[i].status = 'success';
        createProgress.value[i].percentage = 100;
        createProgress.value[i].message = `订单创建成功 (${
          res.data?.order_id || ''
        })`;
        successCount++;
      } else {
        createProgress.value[i].status = 'error';
        createProgress.value[i].percentage = 100;
        createProgress.value[i].message = res.message || '创建失败';
        failCount++;
      }
    } catch (error) {
      createProgress.value[i].status = 'error';
      createProgress.value[i].percentage = 100;
      createProgress.value[i].message = '网络错误';
      failCount++;
    }

    // 添加延迟，让用户看到进度变化
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  // 显示最终结果
  if (failCount === 0) {
    ElMessage.success(`所有 ${successCount} 个订单创建成功！`);
    emit('update:visible', false);
    resetForm();
  } else if (successCount === 0) {
    ElMessage.error(`所有订单创建失败！`);
  } else {
    ElMessage.warning(
      `部分订单创建成功！成功 ${successCount} 个，失败 ${failCount} 个`
    );
  }
};

// 重置表单
const resetForm = () => {
  currentStep.value = 0;
  visaType.value = []; // 改回数组
  dateRangeList.value = [[]];
  accept_next_day.value = false;
  accept_vip.value = false;
  travel_date.value = '';
  customer.value = '';
  price.value = '';
  remark.value = '';
  clients.value = [
    {
      name: '',
      surname_pinyin: '',
      firstname_pinyin: '',
      gender: '',
      dob: '',
      passport: '',
      passport_expire: '',
      passport_image: '',
      avatar_image: '',
      nationality: '',
      passport_date: '',
      sign_location: '',
      bornplace: '',
      marital_status: '',
    },
  ];
  activeNames.value = ['0'];
  editingOrderId.value = null;

  // 重置拆分创建相关
  splitCreate.value = false;
  showProgress.value = false;
  createProgress.value = [];

  // 清空表单验证
  nextTick(() => {
    // 清空客户表单验证
    formRefs.value.forEach(form => {
      if (form) {
        form.clearValidate();
      }
    });

    // 清空订单详情表单验证
    if (orderDetailsFormRef.value) {
      orderDetailsFormRef.value.clearValidate();
    }
  });
};

// 监听签证类型变化，如果选择了多个西班牙签证，只保留第一个
watch(
  () => visaType.value,
  newVisaTypes => {
    const visaArray = ensureVisaTypeArray(newVisaTypes);

    if (visaArray.length > 0) {
      const spainVisas = visaArray.filter(
        visa => visa && visa[0] && visa[0].toLowerCase().includes('spain')
      );

      // 如果有多个西班牙签证，只保留第一个
      if (spainVisas.length > 1) {
        const firstSpainVisa = spainVisas[0];
        const nonSpainVisas = visaArray.filter(
          visa => !visa || !visa[0] || !visa[0].toLowerCase().includes('spain')
        );

        // 创建新的数组，只包含第一个西班牙签证和所有非西班牙签证
        const filteredVisaTypes = [firstSpainVisa, ...nonSpainVisas];

        // 如果过滤后的数组与原数组不同，更新visaType
        if (filteredVisaTypes.length !== visaArray.length) {
          visaType.value = filteredVisaTypes;
          ElMessage.warning('只能选择一个西班牙签证类型，已自动保留第一个');
        }
      }
    }
  },
  { deep: true }
);

// 监听客户数量变化，当只有一个客户时重置拆分选项
watch(
  () => clients.value.length,
  newLength => {
    if (newLength <= 1) {
      splitCreate.value = false;
    }
  }
);

// 监听编辑订单数据
watch(
  () => props.editingOrder,
  val => {
    if (val) {
      if (val.clients) {
        clients.value = JSON.parse(JSON.stringify(val.clients));
        activeNames.value = val.clients.map((_, i) => String(i));
      }
      if (val.visaType) {
        visaType.value = JSON.parse(JSON.stringify(val.visaType));
      }
      if (val.dateRangeList) {
        dateRangeList.value = JSON.parse(JSON.stringify(val.dateRangeList));
      }
      if (val.accept_vip !== undefined) {
        accept_vip.value = val.accept_vip;
      }
      if (val.accept_next_day !== undefined) {
        accept_next_day.value = val.accept_next_day;
      }
      if (val.travel_date) {
        travel_date.value = val.travel_date;
      }
      if (val.customer) {
        customer.value = val.customer;
      }
      if (val.price) {
        price.value = val.price;
      }
      if (val.remark) {
        remark.value = val.remark;
      }
      editingOrderId.value = val.order_id;
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (props.editingOrder?.order_id) {
    editingOrderId.value = props.editingOrder.order_id;
  }
});
</script>

<style scoped>
/* ==================== 抽屉整体样式 ==================== */
.new-order-drawer {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
}

/* ==================== 头部样式 ==================== */
.drawer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  min-height: 80px;
  position: sticky;
  top: 0;
  z-index: 10;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 8px;
}

.header-row-1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-row-2 {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.title-section {
  flex-shrink: 0;
}

.drawer-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  white-space: nowrap;
}

.title-icon {
  font-size: 22px;
}

.close-btn {
  color: white;
  font-size: 20px;
  padding: 8px;
  flex-shrink: 0;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

/* ==================== Header中的步骤指示器 ==================== */
.header-steps {
  width: 100%;
  max-width: 600px;
}

.header-steps-component :deep(.el-step__title) {
  font-weight: 600;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.2;
}

.header-steps-component :deep(.el-step__description) {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
  margin-top: 2px;
}

.header-steps-component :deep(.el-step__icon) {
  width: 28px;
  height: 28px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.header-steps-component :deep(.el-step__icon.is-text) {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.header-steps-component :deep(.el-step.is-finish .el-step__icon) {
  background: rgba(76, 175, 80, 0.8);
  border-color: rgba(76, 175, 80, 1);
  color: white;
}

.header-steps-component :deep(.el-step.is-process .el-step__icon) {
  background: rgba(33, 150, 243, 0.8);
  border-color: rgba(33, 150, 243, 1);
  color: white;
}

.header-steps-component :deep(.el-step__line) {
  background: rgba(255, 255, 255, 0.3);
}

.header-steps-component :deep(.el-step.is-finish .el-step__line) {
  background: rgba(76, 175, 80, 0.6);
}

.header-steps-component :deep(.el-step__main) {
  margin-left: 8px;
}

.header-steps-component :deep(.el-step) {
  margin-bottom: 0;
}

.header-steps-component {
  width: 100%;
}

/* ==================== 主要内容区域 ==================== */
.drawer-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: #f8f9fa;
  height: calc(100vh - 80px);
}

.step-content {
  padding: 16px 20px;
  max-width: 750px;
  margin: 0 auto;
}

/* ==================== 表单样式 ==================== */
.visa-form {
  background: white;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 16px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 签证级联选择器样式 */
.visa-cascader {
  width: 100%;
}

.form-row {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.form-row .el-form-item {
  flex: 1;
  min-width: 0; /* 防止flex项目溢出 */
}

/* 单列布局的表单项 */
.form-row.single-column {
  display: block;
}

.form-row.single-column .el-form-item {
  width: 100%;
  margin-bottom: 16px;
}

/* 输入框样式优化 */
.form-row .el-input,
.form-row .el-select,
.form-row .el-date-picker {
  width: 100%;
}

/* 特殊宽度设置 */
.form-row .el-form-item--large {
  flex: 2; /* 占用更多空间 */
}

.form-row .el-form-item--small {
  flex: 0.8; /* 占用较少空间 */
}

.date-picker {
  width: 100%;
}

/* ==================== 拆分创建相关样式 ==================== */
.split-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.progress-container {
  margin-top: 16px;
}

.progress-item {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.client-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.progress-message {
  margin-top: 8px;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* 删除原订单进度项特殊样式 */
.progress-item:first-child {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.progress-item:first-child .client-name {
  color: #856404;
  font-weight: 700;
}

/* 拆分模式下的特殊提示 */
.split-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #856404;
  font-size: 13px;
  line-height: 1.5;
}

.split-warning .el-icon {
  margin-right: 6px;
  color: #f39c12;
}

/* 取消状态的进度项样式 */
.progress-item.cancelled {
  background: #f8f9fa;
  border-color: #dee2e6;
  opacity: 0.6;
}

.progress-item.cancelled .client-name {
  color: #6c757d;
  text-decoration: line-through;
}

.progress-item.cancelled .progress-message {
  color: #6c757d;
  font-style: italic;
}

/* ==================== 日期范围样式 ==================== */
.date-ranges {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.date-range-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
}

.date-range-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.range-label {
  font-weight: 600;
  color: #495057;
}

/* ==================== 客户信息样式 ==================== */
.clients-section {
  background: white;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.clients-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.clients-count {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.client-collapse {
  border: none;
}

.client-collapse :deep(.el-collapse-item__header) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 8px;
  font-weight: 600;
}

.client-collapse :deep(.el-collapse-item__content) {
  padding: 12px;
  background: white;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 6px 6px;
  margin-bottom: 8px;
}

.client-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.client-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.client-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.client-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  height: 16px;
  line-height: 16px;
}

.client-status {
  font-size: 12px;
  height: 12px;
  line-height: 12px;
}

.client-status.status-complete {
  color: var(--success-color);
}

.client-status.status-partial {
  color: var(--warning-color);
}

.client-status.status-empty {
  color: var(--info-color);
}

.client-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* ==================== 上传区域样式 ==================== */
.upload-section {
  margin-bottom: 16px;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.upload-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.upload-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 6px;
}

.upload-badge {
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.upload-badge.required {
  background: #fef0f0;
  color: var(--danger-color);
}

.upload-badge.optional {
  background: #f0f9ff;
  color: var(--info-color);
}

/* ==================== 护照上传布局 ==================== */
.passport-upload-section {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 16px;
}

.passport-preview-card.full-width {
  width: 100%;
  max-width: 350px;
}

/* 头像上传布局优化 */
.avatar-upload-section {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 16px;
}

.avatar-preview-card.full-width {
  width: 100%;
  max-width: 350px;
}

.upload-card.compact {
  flex: 0 0 200px;
  min-height: 160px;
  height: auto;
}

/* ==================== 头像上传布局 ==================== */

.upload-area {
  width: 100%;
}

.upload-area.compact :deep(.el-upload-dragger) {
  width: 100%;
  min-height: 120px;
  height: auto;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: white;
  transition: all 0.3s ease;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: 160px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
}

.upload-area :deep(.el-upload-dragger:hover) {
  border-color: var(--primary-color);
  background: #f0f8ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.upload-content.compact {
  padding: 10px;
}

.upload-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 12px;
}

.upload-content.compact .upload-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 0 0 4px 0;
  color: #606266;
}

.upload-content.compact .upload-text p {
  font-size: 12px;
  margin: 0 0 2px 0;
}

.upload-text em {
  color: var(--primary-color);
  font-style: normal;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.upload-content.compact .upload-tip {
  font-size: 10px;
}

.upload-preview {
  position: relative;
  width: 100%;
  height: 160px;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-preview:hover .preview-overlay {
  opacity: 1;
}

.preview-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.preview-actions .el-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.preview-actions .el-button:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* ==================== 紧凑上传组件样式 ==================== */
.upload-section-compact {
  margin-bottom: 16px;
}

.upload-item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
  min-height: 60px;
}

.upload-item-row:last-child {
  border-bottom: none;
}

.upload-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.upload-label .el-icon {
  font-size: 16px;
  color: #409eff;
}

.upload-action {
  display: flex;
  align-items: center;
  gap: 12px;
}

.uploaded-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-thumb {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  object-fit: cover;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preview-thumb:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.upload-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
  height: auto;
  line-height: 1;
}

.upload-actions .el-button .el-icon {
  font-size: 12px;
}

/* ==================== 拖拽上传样式 ==================== */
.upload-drop-zone {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.drop-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #909399;
}

.drop-hint .el-icon {
  font-size: 14px;
  color: #409eff;
}

.upload-item-row.drag-over {
  background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
  border: 2px dashed #409eff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  transform: scale(1.02);
  transition: all 0.3s ease;
}

.upload-item-row.drag-over .upload-label {
  color: #409eff;
  font-weight: 600;
}

.upload-item-row.drag-over .drop-hint {
  color: #409eff;
  font-weight: 500;
}

.upload-item-row.drag-over .drop-hint .el-icon {
  color: #409eff;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* ==================== 护照预览卡片 ==================== */
.passport-preview-card {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: white;
  overflow: hidden;
  min-height: 160px;
  height: auto;
  max-width: 300px;
}

/* ==================== 头像预览卡片 ==================== */
.avatar-preview-card {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: white;
  overflow: hidden;
  min-height: 160px;
  height: auto;
  max-width: 300px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.preview-header .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.passport-preview-image {
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
  background: #fafafa;
}

.avatar-preview-image {
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;
  background: #fafafa;
}

.preview-img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain; /* 确保图片按比例完整显示 */
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.preview-img:hover {
  transform: scale(1.02);
}

/* ==================== 护照预览 Dialog ==================== */
.passport-preview-dialog {
  user-select: none;
}

.passport-preview-dialog :deep(.el-dialog__header) {
  cursor: move;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
}

.passport-preview-dialog :deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.passport-preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  background: #fafafa;
}

.passport-dialog-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  padding: 20px;
  background: #fff;
}

.passport-dialog-image {
  max-width: 600px;
  max-height: 80vh;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ==================== 底部操作区域 ==================== */
.drawer-footer {
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 24px;
  position: sticky;
  bottom: 0;
  z-index: 10;
  flex-shrink: 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 12px;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .drawer-header {
    padding: 20px 24px;
  }

  .drawer-title {
    font-size: 20px;
  }

  .steps-container {
    padding: 24px 20px;
  }

  .step-content {
    padding: 24px 20px;
  }

  .visa-form,
  .clients-section {
    padding: 24px 20px;
  }

  .upload-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 16px;
  }

  .footer-left {
    order: 2;
  }

  .footer-right {
    order: 1;
    width: 100%;
    justify-content: center;
  }
}

/* ==================== 动画效果 ==================== */
.upload-area,
.client-item,
.date-range-item {
  transition: all 0.3s ease;
}

.client-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.date-range-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}
</style>

<style>
/* 全局样式覆盖 Element Plus Drawer */
.new-order-drawer .el-drawer__body {
  padding: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  overflow: hidden !important;
}
</style>
