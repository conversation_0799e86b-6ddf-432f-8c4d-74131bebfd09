<template>
  <div class="visa-query-container">
    <div class="visa-query-header">
      <div class="filter-bar">
        <label class="label">筛选国家：</label>
        <el-select
          v-model="selectedCountry"
          placeholder="请选择国家"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="country in countryList"
            :key="country"
            :label="getCountryName(country)"
            :value="country"
          />
        </el-select>
      </div>
    </div>

    <div class="visa-query-content">
      <div class="card-list">
        <div
          class="vfs-card"
          v-for="(items, country) in groupedByCountry"
          :key="country"
          :data-country="country"
        >
          <div class="vfs-header">
            <img :src="getFlagUrl(country)" alt="flag" class="card-flag" />
            <span class="card-country">{{ getCountryName(country) }}</span>
            <el-button
              size="small"
              type="text"
              icon="el-icon-camera"
              @click="exportSingleCountry(country)"
              style="margin-left: auto"
              >导出</el-button
            >
          </div>
          <div class="vfs-body">
            <div class="center-grid">
              <div
                class="center-entry"
                v-for="(itemsByCenter, center) in groupByCenter(items)"
                :key="center"
              >
                <p class="line-center">
                  <span class="label">签证中心：</span
                  ><span class="value">{{ getCityName(center) }}</span>
                </p>
                <div
                  v-for="entry in itemsByCenter"
                  :key="entry.type"
                  class="entry-type-block"
                >
                  <p class="line-type">
                    <span class="label">签证类型：</span
                    ><span class="type">{{ getVisaType(entry.type) }}</span>
                  </p>
                  <p class="line-date">
                    <span class="label">可约日期：</span>
                    <span
                      :class="[
                        'date',
                        { 'no-date': entry.date === '暂无位置' },
                      ]"
                      >{{ entry.date }}</span
                    >
                  </p>
                  <p class="line-time">
                    <span class="label">更新时间：</span
                    ><span class="time">{{ entry.update }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, computed } from 'vue';
import html2canvas from 'html2canvas';
import http from '@/utils/http';

const selectedCountry = ref('');
const vfsData = ref([]);

const cityMap = {
  BEIJING: '北京',
  SHANGHAI: '上海',
  GUANGZHOU: '广州',
  SHENZHEN: '深圳',
  CHENGDU: '成都',
  NANJING: '南京',
  HANGZHOU: '杭州',
  SHENYANG: '沈阳',
  FUZHOU: '福州',
};

const countryMap = {
  switzerland: '瑞士',
  iceland: '冰岛',
  finland: '芬兰',
  belgium: '比利时',
  austria: '奥地利',
  hungary: '匈牙利',
  portugal: '葡萄牙',
  poland: '波兰',
  czech: '捷克共和国',
  bulgaria: '保加利亚',
  germany: '德国',
  italy: '意大利',
  brazil: '巴西',
  spain: '西班牙',
  ireland: '爱尔兰',
  slovenia: '斯洛文尼亚',
  norway: '挪威',
  luxembourg: '卢森堡',
  sweden: '瑞典',
  malta: '马耳他',
  croatia: '克罗地亚',
};

const visaTypeMap = {
  Tourism: '旅游',
  Business: '商务',
  'Family Reunion': '探亲',
};

const getCombinedData = async () => {
  try {
    const [vfsRes, blsRes] = await Promise.all([
      http.post('/get_vfs_date', {}),
      http.post('/get_bls_date', {}),
    ]);

    // 合并两个结果，并将 BLS 数据先做 convert 处理
    const blsData = blsRes.data.map(convertItem);

    // 统一合并并赋值
    vfsData.value = [...vfsRes.data, ...blsData];
  } catch (error) {
    console.error('合并请求失败', error);
  }
};

onMounted(() => {
  getCombinedData();
});

const convertItem = raw => {
  return {
    country: raw.country,
    center: raw.centerCode,
    type: raw.visaType + (raw.isVIP ? ' VIP' : ''),
    date: raw.dates && raw.dates.length > 0 ? raw.dates[0] : '暂无位置',
    update: formatTimestamp(raw.updateTime),
  };
};

const groupedVfsData = computed(() =>
  vfsData.value.filter(
    item => !selectedCountry.value || item.country === selectedCountry.value
  )
);

const groupedByCountry = computed(() => {
  const result = {};
  groupedVfsData.value.forEach(item => {
    if (!result[item.country]) result[item.country] = [];
    result[item.country].push(item);
  });
  return result;
});

const countryList = computed(() => {
  const set = new Set();
  vfsData.value.forEach(item => set.add(item.country));
  return Array.from(set).sort();
});

const groupByCenter = items => {
  const map = {};
  items.forEach(item => {
    if (!map[item.center]) map[item.center] = [];
    map[item.center].push(item);
  });
  return map;
};

const getCityName = code => cityMap[code] || code;
const getCountryName = code => countryMap[code] || code;
const getVisaType = type => visaTypeMap[type] || type;

const formatTimestamp = ts => {
  const d = new Date(ts * 1000);
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(
    d.getHours()
  )}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
};

const pad = n => (n < 10 ? '0' + n : n);

const getFlagUrl = countryCode => {
  const flagMap = {
    瑞士: 'https://flagcdn.com/w40/ch.png',
    冰岛: 'https://flagcdn.com/w40/is.png',
    芬兰: 'https://flagcdn.com/w40/fi.png',
    比利时: 'https://flagcdn.com/w40/be.png',
    奥地利: 'https://flagcdn.com/w40/at.png',
    匈牙利: 'https://flagcdn.com/w40/hu.png',
    葡萄牙: 'https://flagcdn.com/w40/pt.png',
    波兰: 'https://flagcdn.com/w40/pl.png',
    捷克共和国: 'https://flagcdn.com/w40/cz.png',
    保加利亚: 'https://flagcdn.com/w40/bg.png',
    德国: 'https://flagcdn.com/w40/de.png',
    意大利: 'https://flagcdn.com/w40/it.png',
    巴西: 'https://flagcdn.com/w40/br.png',
    西班牙: 'https://flagcdn.com/w40/es.png',
    爱尔兰: 'https://flagcdn.com/w40/ie.png',
    斯洛文尼亚: 'https://flagcdn.com/w40/si.png',
    挪威: 'https://flagcdn.com/w40/no.png',
    卢森堡: 'https://flagcdn.com/w40/lu.png',
    瑞典: 'https://flagcdn.com/w40/se.png',
    马耳他: 'https://flagcdn.com/w40/mt.png',
    克罗地亚: 'https://flagcdn.com/w40/hr.png',
  };
  return flagMap[getCountryName(countryCode)] || '';
};

const exportSingleCountry = code => {
  const el = document.querySelector(`.vfs-card[data-country="${code}"]`);
  if (!el) return;
  html2canvas(el, { scale: 2 }).then(canvas => {
    const link = document.createElement('a');
    link.download = `visa_${code}_${Date.now()}.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();
  });
};
</script>

<style scoped>
/* ==================== 主容器布局 ==================== */
.visa-query-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
}

.visa-query-header {
  flex-shrink: 0;
  padding: 20px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.visa-query-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 24px;
}

.warning_row_class {
  background-color: #e6a23c !important;
}

@media (max-width: 768px) {
  .card-list {
    flex-direction: column;
  }

  .vfs-card {
    padding: 10px;
  }

  .center-grid {
    flex-direction: column;
    gap: 10px;
  }

  .center-entry {
    flex: 1 1 100%;
    padding: 8px;
  }

  .entry {
    width: 100%;
    margin-left: 0;
  }
}

.no-date {
  color: #ff4d4f !important;
  font-weight: bold;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.advanceMode {
  padding: 15px;
  width: calc(100% - 32px);
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #65636622;
}
.uploaded {
  color: blue;
}
.entry {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  width: 360px;
  margin-left: 20px;
}
.entry-container {
  display: flex;
  align-items: center;
}
.flag-container {
  margin-right: 15px;
}
.flag {
  width: 50px;
  height: auto;
  border-radius: 4px;
}
.entry-details {
  flex-grow: 1;
}
.city,
.country,
.visa-type,
.vip-status,
.dates,
.update-time {
  margin: 5px 0;
}
.highlight {
  font-weight: bold;
  color: #0056b3;
}
.visa-type {
  color: #d9534f;
}
.vip {
  color: #ff0000;
  font-weight: bold;
}
.normal {
  color: #28a745;
  font-weight: bold;
}
.date {
  color: #6c757d;
}
.time {
  color: #6c757d;
  font-style: italic;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 40px; /* 底部留白，避免内容被截断 */
}

.vfs-card {
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #e9ecef;
  border-radius: 16px;
  background: #ffffff;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vfs-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
  opacity: 0.8;
}

.vfs-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.vfs-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f8f9fa;
}

.card-country {
  font-size: 18px;
  font-weight: 700;
  color: #212529;
  flex: 1;
}

.card-flag {
  width: 32px;
  height: auto;
  margin-right: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.vfs-body p {
  margin: 6px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #444;
}

.country-title {
  font-size: 20px;
  font-weight: bold;
  margin-top: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.center-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.center-entry {
  flex: 0 0 calc(25% - 15px);
  box-sizing: border-box;
  border: 1px dashed #ccc;
  border-radius: 6px;
  padding: 10px;
  background-color: #fafafa;
}

/* .center-entry p::before {
  content: '';
  margin-right: 6px;
  font-size: 14px;
} */

.entry-type-block {
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid #eaeaea;
}

.label {
  font-weight: bold;
  color: #666;
  margin-right: 4px;
  flex-shrink: 0;
}

.value {
  color: #333;
}

.type {
  color: #007bff;
  font-weight: 500;
}

.date {
  color: #28a745;
  font-weight: 500;
}

.time {
  color: #888;
  font-style: italic;
}

.center-entry p {
  display: flex;
  align-items: center;
}

.line-center::before {
  content: '🏙️';
  margin-right: 6px;
  font-size: 14px;
}
.line-type::before {
  content: '🛂';
  margin-right: 6px;
  font-size: 14px;
}
.line-date::before {
  content: '📅';
  margin-right: 6px;
  font-size: 14px;
}
.line-time::before {
  content: '⏰';
  margin-right: 6px;
  font-size: 14px;
}
.action-bar {
  margin-bottom: 20px;
  padding-left: 16px;
}
.filter-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-bar .label {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0;
}

/* ==================== 滚动条美化 ==================== */
.visa-query-content::-webkit-scrollbar {
  width: 8px;
}

.visa-query-content::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 4px;
}

.visa-query-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #007bff, #0056b3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.visa-query-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #0056b3, #004085);
}

/* ==================== 响应式优化 ==================== */
@media (max-width: 768px) {
  .visa-query-header {
    padding: 16px;
  }

  .visa-query-content {
    padding: 16px;
  }

  .filter-bar {
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .vfs-card {
    padding: 16px;
  }

  .card-country {
    font-size: 16px;
  }
}
</style>
