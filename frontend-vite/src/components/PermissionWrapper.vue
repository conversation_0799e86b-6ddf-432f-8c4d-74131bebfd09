<template>
  <div v-if="hasAccess" class="permission-wrapper">
    <slot />
  </div>
  <div v-else-if="showFallback" class="permission-fallback">
    <slot name="fallback">
      <el-empty 
        description="权限不足" 
        :image-size="120"
        class="permission-denied"
      >
        <template #description>
          <span class="permission-denied-text">
            您没有访问此内容的权限
          </span>
        </template>
        <template #extra>
          <el-button type="primary" @click="$router.go(-1)">
            返回上一页
          </el-button>
        </template>
      </el-empty>
    </slot>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { usePermissions } from '@/composables/usePermissions';

const props = defineProps({
  // 需要的权限列表（满足任意一个即可）
  permissions: {
    type: Array,
    default: () => [],
  },
  // 需要的所有权限（必须全部满足）
  allPermissions: {
    type: Array,
    default: () => [],
  },
  // 是否显示无权限时的占位内容
  showFallback: {
    type: Boolean,
    default: false,
  },
  // 角色检查（admin, kefu, user）
  roles: {
    type: Array,
    default: () => [],
  },
});

const { 
  checkAnyPermission, 
  checkAllPermissions,
  isAdmin,
  isCustomerService,
  isUser 
} = usePermissions();

// 检查权限访问
const hasAccess = computed(() => {
  // 如果没有配置任何权限要求，默认允许访问
  if (props.permissions.length === 0 && 
      props.allPermissions.length === 0 && 
      props.roles.length === 0) {
    return true;
  }
  
  // 检查角色权限
  if (props.roles.length > 0) {
    const roleCheck = props.roles.some(role => {
      switch (role) {
        case 'admin':
          return isAdmin.value;
        case 'kefu':
          return isCustomerService.value;
        case 'user':
          return isUser.value;
        default:
          return false;
      }
    });
    
    if (!roleCheck) return false;
  }
  
  // 检查权限（任意一个）
  if (props.permissions.length > 0) {
    if (!checkAnyPermission(props.permissions)) {
      return false;
    }
  }
  
  // 检查权限（全部必须）
  if (props.allPermissions.length > 0) {
    if (!checkAllPermissions(props.allPermissions)) {
      return false;
    }
  }
  
  return true;
});
</script>

<style scoped>
.permission-wrapper {
  width: 100%;
  height: 100%;
}

.permission-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.permission-denied {
  background: transparent;
}

.permission-denied-text {
  color: #909399;
  font-size: 14px;
}

.permission-denied :deep(.el-empty__image) {
  opacity: 0.6;
}
</style>