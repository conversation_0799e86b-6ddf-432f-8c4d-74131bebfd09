import { createRouter, createWebHistory } from 'vue-router';
import Login from '../views/Login.vue';
import Dashboard from '../views/Dashboard.vue';
import VisaCardList from '@/components/VisaCardList.vue';
import UserProfile from '@/views/dashboard/UserProfile.vue';
import PendingOrders from '@/views/dashboard/PendingOrders.vue';
import BookedOrders from '@/views/dashboard/BookedOrders.vue';
import WaitPayOrders from '@/views/dashboard/WaitPayOrders.vue';
import UserManagement from '@/views/UserManagement.vue';
import RoleManagement from '@/views/RoleManagement.vue';
import SchengenForm from '@/views/dashboard/SchengenForm.vue';

const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', component: Login },
  {
    path: '/dashboard',
    component: Dashboard,
    children: [
      { path: '', redirect: '/dashboard/profile' },
      { path: 'profile', component: UserProfile },
      { path: 'pending', component: PendingOrders },
      { path: 'completed', component: BookedOrders },
      { path: 'waitpay', component: WaitPayOrders },
      {
        path: 'visa',
        name: 'VisaCardList',
        component: VisaCardList,
      },
      {
        path: 'schengen-form',
        name: 'SchengenForm',
        component: SchengenForm,
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: UserManagement,
        meta: { requiresAdmin: true },
      },
      {
        path: 'roles',
        name: 'RoleManagement',
        component: RoleManagement,
        meta: { requiresAdmin: true },
      },
    ],
  },
];

import { useUserStore } from '@/stores/user';
import { hasRoutePermission } from '@/utils/permissions';

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, _from, next) => {
  const userStore = useUserStore();

  // 如果是登录页面，直接放行
  if (to.path === '/login') {
    next();
    return;
  }

  // 检查是否已登录
  if (!userStore.isLoggedIn) {
    next('/login');
    return;
  }

  // 找到用户有权限访问的默认页面
  const findAccessibleRoute = () => {
    const permissions = userStore.permissions || [];

    // 个人中心应该总是可访问的，先检查这个
    if (hasRoutePermission(permissions, '/dashboard/profile')) {
      return '/dashboard/profile';
    }

    // 按优先级检查用户可以访问的其他路由
    const routesToCheck = [
      '/dashboard/pending', // 待预约订单
      '/dashboard/completed', // 已完成订单
      '/dashboard/visa', // 签证查询
      '/dashboard/schengen-form', // 申根表单创建
    ];

    for (const route of routesToCheck) {
      if (hasRoutePermission(permissions, route)) {
        return route;
      }
    }

    // 如果连个人中心都无法访问，直接返回个人中心并放行
    return '/dashboard/profile';
  };

  // 检查是否需要管理员权限
  if (to.meta?.requiresAdmin && userStore.permission !== 'admin') {
    const fallbackRoute = findAccessibleRoute();
    if (to.path !== fallbackRoute) {
      next(fallbackRoute);
      return;
    }
  }

  // 检查路由权限（排除个人中心，个人中心应该总是可访问）
  if (
    to.path !== '/dashboard/profile' &&
    !hasRoutePermission(userStore.permissions || [], to.path)
  ) {
    const fallbackRoute = findAccessibleRoute();
    if (to.path !== fallbackRoute) {
      next(fallbackRoute);
      return;
    }
  }

  next();
});

export default router;
