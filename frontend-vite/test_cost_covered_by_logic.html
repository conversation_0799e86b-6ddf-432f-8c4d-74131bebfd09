<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>费用承担方条件显示逻辑测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .form-group.hidden {
            display: none;
        }
        .form-group.visible {
            display: block;
            background-color: #f0f8ff;
            border-color: #409eff;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="radio"] {
            margin-right: 8px;
        }
        input[type="checkbox"] {
            margin-right: 8px;
        }
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.show {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.hide {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        h1, h2 {
            color: #333;
        }
        .logic-explanation {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>费用承担方条件显示逻辑测试</h1>
        
        <div class="logic-explanation">
            <h3>📋 业务逻辑说明</h3>
            <p><strong>规则</strong>：只有当"费用由邀请人或邀请公司支付"选择"否"时，才需要填写"费用承担方"。</p>
            <ul>
                <li>选择"是" → 隐藏费用承担方字段</li>
                <li>选择"否" → 显示费用承担方字段，且为必填</li>
            </ul>
        </div>

        <!-- 费用由邀请人或邀请公司支付 -->
        <div class="form-group">
            <label>费用由邀请人或邀请公司支付</label>
            <div>
                <label>
                    <input type="radio" name="inviting_person_covered_costs" value="1" onchange="updateCostCoveredBy()">
                    是
                </label>
                <label>
                    <input type="radio" name="inviting_person_covered_costs" value="2" onchange="updateCostCoveredBy()" checked>
                    否
                </label>
            </div>
            <div id="status1" class="status show">当前选择：否 → 显示费用承担方字段</div>
        </div>

        <!-- 费用承担方（条件显示） -->
        <div id="costCoveredByGroup" class="form-group visible">
            <label>出行和居住费用的承担方 <span style="color: red;">*</span></label>
            <div class="checkbox-group">
                <label>
                    <input type="checkbox" value="AHH" onchange="updateOthersField()">
                    申请者本人
                </label>
                <label>
                    <input type="checkbox" value="BO" onchange="updateOthersField()">
                    其他人
                </label>
                <label>
                    <input type="checkbox" value="HST" onchange="updateOthersField()">
                    接待方
                </label>
            </div>
        </div>

        <!-- 费用承担方-其他（条件显示） -->
        <div id="costCoveredByOthersGroup" class="form-group hidden">
            <label>出行和居住费用-其他 <span style="color: red;">*</span></label>
            <input type="text" placeholder="请详细说明其他费用承担方" style="width: 100%; padding: 8px;">
        </div>

        <div class="test-result">
            <h2>🧪 测试结果</h2>
            <div id="testResult">
                <p><strong>当前状态</strong>：费用由邀请人支付 = 否</p>
                <p><strong>费用承担方字段</strong>：显示 ✅</p>
                <p><strong>验证逻辑</strong>：正确 ✅</p>
            </div>
        </div>
    </div>

    <script>
        function updateCostCoveredBy() {
            const radios = document.getElementsByName('inviting_person_covered_costs');
            const costGroup = document.getElementById('costCoveredByGroup');
            const othersGroup = document.getElementById('costCoveredByOthersGroup');
            const status = document.getElementById('status1');
            const testResult = document.getElementById('testResult');
            
            let selectedValue = null;
            for (const radio of radios) {
                if (radio.checked) {
                    selectedValue = radio.value;
                    break;
                }
            }
            
            if (selectedValue === '1') {
                // 选择"是" - 隐藏费用承担方
                costGroup.className = 'form-group hidden';
                othersGroup.className = 'form-group hidden';
                status.className = 'status hide';
                status.textContent = '当前选择：是 → 隐藏费用承担方字段';
                
                // 清空选择
                const checkboxes = costGroup.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(cb => cb.checked = false);
                
                testResult.innerHTML = `
                    <p><strong>当前状态</strong>：费用由邀请人支付 = 是</p>
                    <p><strong>费用承担方字段</strong>：隐藏 ✅</p>
                    <p><strong>验证逻辑</strong>：正确 ✅</p>
                `;
            } else {
                // 选择"否" - 显示费用承担方
                costGroup.className = 'form-group visible';
                status.className = 'status show';
                status.textContent = '当前选择：否 → 显示费用承担方字段';
                
                testResult.innerHTML = `
                    <p><strong>当前状态</strong>：费用由邀请人支付 = 否</p>
                    <p><strong>费用承担方字段</strong>：显示 ✅</p>
                    <p><strong>验证逻辑</strong>：正确 ✅</p>
                `;
            }
        }
        
        function updateOthersField() {
            const othersCheckbox = document.querySelector('input[value="BO"]');
            const othersGroup = document.getElementById('costCoveredByOthersGroup');
            
            if (othersCheckbox.checked) {
                othersGroup.className = 'form-group visible';
            } else {
                othersGroup.className = 'form-group hidden';
            }
        }
        
        // 初始化
        updateCostCoveredBy();
    </script>
</body>
</html>
