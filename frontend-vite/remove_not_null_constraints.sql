-- =====================================================
-- PostgreSQL 数据库脚本：移除第六步字段的非空约束
-- 解决保存草稿时第六步未填写导致的数据库约束错误
-- =====================================================

-- 开始事务（确保原子性）
BEGIN;

-- 移除 schengen_additional_info 表中日期字段的非空约束
-- 这些字段在保存草稿时可能为空

-- 1. 移除到达日期字段的非空约束
ALTER TABLE schengen_additional_info 
ALTER COLUMN arrival_date_year DROP NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN arrival_date_month DROP NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN arrival_date_day DROP NOT NULL;

-- 2. 移除离开日期字段的非空约束
ALTER TABLE schengen_additional_info 
ALTER COLUMN departure_date_year DROP NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN departure_date_month DROP NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN departure_date_day DROP NOT NULL;

-- 3. 移除欧盟公民出生日期字段的非空约束（如果存在）
ALTER TABLE schengen_additional_info 
ALTER COLUMN eu_date_of_birth_year DROP NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN eu_date_of_birth_month DROP NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN eu_date_of_birth_day DROP NOT NULL;

-- 提交事务
COMMIT;

\echo '=== 非空约束移除完成 ==='

-- =====================================================
-- 验证脚本：检查约束是否已移除
-- =====================================================

-- 查看表结构，确认字段现在允许NULL
\d schengen_additional_info

-- 查询字段的约束信息
SELECT 
    column_name,
    is_nullable,
    data_type,
    column_default
FROM information_schema.columns 
WHERE table_name = 'schengen_additional_info' 
AND column_name IN (
    'arrival_date_year', 'arrival_date_month', 'arrival_date_day',
    'departure_date_year', 'departure_date_month', 'departure_date_day',
    'eu_date_of_birth_year', 'eu_date_of_birth_month', 'eu_date_of_birth_day'
)
ORDER BY column_name;

-- =====================================================
-- 测试脚本：验证可以插入NULL值
-- =====================================================

-- 测试插入NULL值（可选，注释掉以避免测试数据）
/*
-- 测试插入
INSERT INTO schengen_additional_info (
    application_id,
    final_destination,
    arrival_date_year,
    arrival_date_month, 
    arrival_date_day,
    departure_date_year,
    departure_date_month,
    departure_date_day,
    duration_of_stay,
    cost_of_travelling_covered_by,
    means_of_support_id,
    is_citizen_id,
    inviting_person_covered_costs,
    costs_covered_by
) VALUES (
    'TEST_NULL_CONSTRAINT_999999',
    2,  -- 默认否
    NULL,  -- arrival_date_year
    NULL,  -- arrival_date_month
    NULL,  -- arrival_date_day
    NULL,  -- departure_date_year
    NULL,  -- departure_date_month
    NULL,  -- departure_date_day
    '',    -- duration_of_stay
    '[]',  -- cost_of_travelling_covered_by
    '[]',  -- means_of_support_id
    2,     -- is_citizen_id (默认否)
    2,     -- inviting_person_covered_costs (默认否)
    ''     -- costs_covered_by
);

-- 验证插入成功
SELECT * FROM schengen_additional_info 
WHERE application_id = 'TEST_NULL_CONSTRAINT_999999';

-- 清理测试数据
DELETE FROM schengen_additional_info 
WHERE application_id = 'TEST_NULL_CONSTRAINT_999999';
*/

-- =====================================================
-- 回滚脚本（如果需要恢复非空约束）
-- =====================================================
/*
-- 注意：只有在所有相关字段都有有效值时才能执行回滚
-- 否则会因为现有的NULL值而失败

BEGIN;

-- 恢复到达日期字段的非空约束
ALTER TABLE schengen_additional_info 
ALTER COLUMN arrival_date_year SET NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN arrival_date_month SET NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN arrival_date_day SET NOT NULL;

-- 恢复离开日期字段的非空约束
ALTER TABLE schengen_additional_info 
ALTER COLUMN departure_date_year SET NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN departure_date_month SET NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN departure_date_day SET NOT NULL;

-- 恢复欧盟公民出生日期字段的非空约束
ALTER TABLE schengen_additional_info 
ALTER COLUMN eu_date_of_birth_year SET NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN eu_date_of_birth_month SET NOT NULL;

ALTER TABLE schengen_additional_info 
ALTER COLUMN eu_date_of_birth_day SET NOT NULL;

COMMIT;

\echo 'Rollback completed - NOT NULL constraints restored'
*/

-- =====================================================
-- 说明文档
-- =====================================================

/*
## 修改说明

### 问题描述
当用户在第六步未填写完整信息时点击"保存草稿"，会出现以下错误：
```
null value in column "arrival_date_year" of relation "schengen_additional_info" violates not-null constraint
```

### 解决方案
移除以下字段的非空约束，允许在保存草稿时这些字段为NULL：
- arrival_date_year, arrival_date_month, arrival_date_day
- departure_date_year, departure_date_month, departure_date_day  
- eu_date_of_birth_year, eu_date_of_birth_month, eu_date_of_birth_day

### 影响评估
- ✅ 正面影响：用户可以在任何阶段保存草稿，提升用户体验
- ✅ 数据完整性：正式提交时仍会进行前端验证
- ✅ 向后兼容：不影响现有数据和功能

### 注意事项
1. 前端验证逻辑保持不变，正式提交时仍需填写必要字段
2. 后端在读取数据时需要处理NULL值
3. 报表和统计功能需要考虑NULL值的情况

## 执行步骤

1. 备份数据库（推荐）
2. 在测试环境先执行此脚本
3. 验证保存草稿功能正常
4. 在生产环境执行

## 验证方法

1. 填写前5步表单信息
2. 不填写第6步，直接点击"保存草稿"
3. 确认保存成功，无数据库错误
4. 重新加载表单，确认数据正确显示
*/
