#!/usr/bin/env python3
"""
批量清洗西班牙签证数据脚本
从Redis的spainUserDatas中读取包含order_id的数据，
从PostgreSQL数据库中获取对应的passport_image和avatar_image，
并更新到Redis中。
"""
import json
import redis
import psycopg2
from datetime import datetime
import traceback

# 配置
REDIS_CONFIG = {
    "host": "localhost",
    "port": 6379,
    "db": 0,
    "password": "TicketsCache#2023",
    "decode_responses": True
}

DATABASE_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "dbname": "user_db",
    "user": "qwyvisa",
    "password": "HAfhqh1fn0fbua8vb7v!aa"
}

class SpainDataCleaner:
    """西班牙签证数据清洗器"""
    
    def __init__(self):
        self.redis_client = None
        self.pg_conn = None
        self.stats = {
            "total_records": 0,
            "records_with_order_id": 0,
            "successful_updates": 0,
            "failed_updates": 0,
            "missing_orders": 0
        }
    
    def connect_redis(self):
        """连接Redis"""
        try:
            self.redis_client = redis.Redis(**REDIS_CONFIG)
            self.redis_client.ping()
            print("✅ Redis连接成功")
            return True
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            return False
    
    def connect_postgres(self):
        """连接PostgreSQL"""
        try:
            self.pg_conn = psycopg2.connect(**DATABASE_CONFIG)
            print("✅ PostgreSQL连接成功")
            return True
        except Exception as e:
            print(f"❌ PostgreSQL连接失败: {e}")
            return False
    
    def get_spain_data_from_redis(self):
        """从Redis获取所有西班牙签证数据"""
        try:
            spain_data = self.redis_client.hgetall("spainUserDatas")
            print(f"📊 从Redis获取到 {len(spain_data)} 条西班牙签证数据")
            return spain_data
        except Exception as e:
            print(f"❌ 获取Redis数据失败: {e}")
            return {}
    
    def get_images_from_postgres(self, order_id):
        """从PostgreSQL获取订单的图片信息"""
        try:
            with self.pg_conn.cursor() as cur:
                cur.execute("""
                    SELECT passport_image, avatar_image 
                    FROM clients 
                    WHERE order_id = %s
                    LIMIT 1
                """, (order_id,))
                
                result = cur.fetchone()
                if result:
                    return {
                        "passport_image": result[0],
                        "avatar_image": result[1]
                    }
                else:
                    return None
        except Exception as e:
            print(f"❌ 查询订单 {order_id} 的图片信息失败: {e}")
            return None
    
    def update_spain_data_in_redis(self, redis_key, updated_data):
        """更新Redis中的西班牙签证数据"""
        try:
            self.redis_client.hset("spainUserDatas", redis_key, json.dumps(updated_data))
            return True
        except Exception as e:
            print(f"❌ 更新Redis数据失败: {e}")
            return False
    
    def clean_single_record(self, redis_key, spain_data_str):
        """清洗单条记录"""
        try:
            # 解析JSON数据
            spain_data = json.loads(spain_data_str)
            
            # 检查是否包含order_id
            order_id = spain_data.get("order_id")
            if not order_id:
                return False, "无order_id"
            
            self.stats["records_with_order_id"] += 1
            
            # 从数据库获取图片信息
            images = self.get_images_from_postgres(order_id)
            if images is None:
                self.stats["missing_orders"] += 1
                return False, f"订单 {order_id} 不存在"
            
            # 更新数据
            updated = False
            if images["passport_image"] and images["passport_image"] != spain_data.get("passport_image"):
                spain_data["passport_image"] = images["passport_image"]
                updated = True
            
            if images["avatar_image"] and images["avatar_image"] != spain_data.get("avatar_image"):
                spain_data["avatar_image"] = images["avatar_image"]
                updated = True
            
            # 如果有更新，保存到Redis
            if updated:
                if self.update_spain_data_in_redis(redis_key, spain_data):
                    self.stats["successful_updates"] += 1
                    return True, f"已更新图片信息"
                else:
                    self.stats["failed_updates"] += 1
                    return False, "Redis更新失败"
            else:
                return True, "无需更新"
                
        except json.JSONDecodeError as e:
            self.stats["failed_updates"] += 1
            return False, f"JSON解析失败: {e}"
        except Exception as e:
            self.stats["failed_updates"] += 1
            return False, f"处理失败: {e}"
    
    def run_cleaning(self, dry_run=False):
        """执行清洗操作"""
        print(f"🚀 开始批量清洗西班牙签证数据 {'(预览模式)' if dry_run else ''}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 连接服务
        if not self.connect_redis() or not self.connect_postgres():
            return False
        
        # 获取所有数据
        spain_data_all = self.get_spain_data_from_redis()
        if not spain_data_all:
            print("❌ 没有找到西班牙签证数据")
            return False
        
        self.stats["total_records"] = len(spain_data_all)
        
        # 处理每条记录
        print("\n📝 开始处理记录:")
        for i, (redis_key, spain_data_str) in enumerate(spain_data_all.items(), 1):
            try:
                success, message = self.clean_single_record(redis_key, spain_data_str)
                status = "✅" if success else "❌"
                
                # 解析基本信息用于显示
                try:
                    data = json.loads(spain_data_str)
                    order_id = data.get("order_id", "无")
                    passport_no = data.get("passportNO", "无")
                except:
                    order_id = "解析失败"
                    passport_no = "解析失败"
                
                print(f"   {i:3d}. {status} {redis_key[:20]:20} | 订单: {order_id:20} | 护照: {passport_no:12} | {message}")
                
                # 每处理50条记录显示一次进度
                if i % 50 == 0:
                    print(f"   📊 已处理 {i}/{len(spain_data_all)} 条记录...")
                
            except Exception as e:
                print(f"   ❌ 处理记录 {redis_key} 时发生错误: {e}")
                self.stats["failed_updates"] += 1
        
        # 显示统计信息
        self.print_statistics()
        
        return True
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "=" * 60)
        print("📊 清洗统计报告:")
        print(f"   总记录数: {self.stats['total_records']}")
        print(f"   包含order_id的记录: {self.stats['records_with_order_id']}")
        print(f"   成功更新的记录: {self.stats['successful_updates']}")
        print(f"   更新失败的记录: {self.stats['failed_updates']}")
        print(f"   订单不存在的记录: {self.stats['missing_orders']}")
        print(f"   成功率: {(self.stats['successful_updates'] / max(self.stats['records_with_order_id'], 1) * 100):.1f}%")
        print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def close_connections(self):
        """关闭连接"""
        if self.redis_client:
            self.redis_client.close()
        if self.pg_conn:
            self.pg_conn.close()
        print("🔌 连接已关闭")

def main():
    """主函数"""
    cleaner = SpainDataCleaner()
    
    try:
        # 询问是否预览模式
        print("西班牙签证数据批量清洗工具")
        print("=" * 40)
        mode = input("请选择模式 (1=预览模式, 2=执行模式): ").strip()
        
        dry_run = mode != "2"
        if dry_run:
            print("⚠️  预览模式：只显示将要进行的操作，不会实际修改数据")
        else:
            confirm = input("⚠️  确认要执行实际的数据更新操作吗？(输入 'YES' 确认): ").strip()
            if confirm != "YES":
                print("❌ 操作已取消")
                return
        
        # 执行清洗
        success = cleaner.run_cleaning(dry_run=dry_run)
        
        if success:
            print("🎉 批量清洗完成！")
        else:
            print("💥 批量清洗失败！")
            
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
    except Exception as e:
        print(f"💥 发生未预期的错误: {e}")
        traceback.print_exc()
    finally:
        cleaner.close_connections()

if __name__ == "__main__":
    main()
