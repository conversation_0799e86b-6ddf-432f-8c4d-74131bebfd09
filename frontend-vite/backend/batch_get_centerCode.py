# -*- coding: utf-8 -*-
from urllib.parse import quote
from curl_cffi import requests
import json
import time
import random
from RedisClient import RedisClient
from datetime import datetime, timedelta
import threading
redis_client = RedisClient()

# # 创建 RedisClient 实例


allcountry = [
    {
        "name": "奥地利",
        "url": "https://visa.vfsglobal.com/chn/zh/aut",
        "contact_url": "http://www.austriavisa-china.com/contact_us.html",
        "flag": "flag/img_map_austria.gif"
    },
    {
        "name": "保加利亚",
        "url": "https://visa.vfsglobal.com/chn/zh/bgr",
        "contact_url": "https://www.vfsglobal.cn/Bulgaria/china/contact-us.html",
        "flag": "flag/img_map_bulgaria.gif"
    },
    {
        "name": "克罗地亚",
        "url": "https://visa.vfsglobal.com/chn/zh/hrv",
        "contact_url": "https://www.vfsglobal.cn/croatia/china/contact_us.html",
        "flag": "flag/img_map_croatia.gif"
    },
    {
        "name": "捷克共和国",
        "url": "https://visa.vfsglobal.com/chn/zh/cze",
        "contact_url": "https://www.vfsglobal.cn/czechrepublic/China/contact_us.html",
        "flag": "flag/img_map_czech_republic.gif"
    },
    {
        "name": "芬兰",
        "url": "https://visa.vfsglobal.com/chn/zh/fin",
        "contact_url": "https://visa.finland.eu/china/contact_us.html",
        "flag": "flag/img_map_finland.gif"
    },
    {
        "name": "德国",
        "url": "https://visa.vfsglobal.com/chn/zh/deu",
        "contact_url": "https://www.vfsglobal.cn/Germany/China/contact-us.html",
        "flag": "flag/img_map_germany.gif"
    },
    {
        "name": "匈牙利",
        "url": "https://visa.vfsglobal.com/chn/zh/hun",
        "contact_url": "https://www.vfsglobal.cn/Hungary/China/English/contact-us.html",
        "flag": "flag/img_map_hungary.gif"
    },
    {
        "name": "冰岛",
        "url": "https://visa.vfsglobal.com/chn/zh/isl",
        "contact_url": "https://www.vfsglobal.cn/Iceland/China/contact_us.html",
        "flag": "flag/img_map_iceland.gif"
    },
    {
        "name": "爱尔兰",
        "url": "https://visa.vfsglobal.com/chn/en/irl",
        "contact_url": "http://www.vfsglobal.cn/ireland/china/visa_application_centre.html",
        "flag": "flag/img_map_ireland.gif"
    },
    {
        "name": "意大利",
        "url": "https://visa.vfsglobal.com/chn/en/ita",
        "contact_url": "http://www.vfsglobal.cn/italy/china/",
        "flag": "flag/img_map_italy.gif"
    },
    {
        "name": "卢森堡",
        "url": "https://visa.vfsglobal.com/chn/zh/lux",
        "contact_url": "https://visa.vfsglobal.com/chn/zh/lux/contact-us",
        "flag": "flag/luxembourg.gif"
    },
    {
        "name": "马耳他短期停留签证",
        "url": "https://visa.vfsglobal.com/chn/zh/mlt",
        "contact_url": "http://www.vfsglobal.cn/Malta/China/contact-us.html",
        "flag": "flag/img_map_malta.gif"
    },
    {
        "name": "挪威",
        "url": "https://visa.vfsglobal.com/chn/zh/nor",
        "contact_url": "http://www.vfsglobal.cn/Norway/China/",
        "flag": "flag/img_map_norway.gif"
    },
    {
        "name": "波兰",
        "url": "https://visa.vfsglobal.com/chn/zh/pol",
        "contact_url": "http://www.vfsglobal.cn/poland/china/contact-us.html",
        "flag": "flag/img_map_poland.gif"
    },
    {
        "name": "葡萄牙",
        "url": "https://visa.vfsglobal.com/chn/zh/prt",
        "contact_url": "http://www.vfsglobal.cn/portugal/china/contact_us.html",
        "flag": "flag/img_map_portugal.gif"
    },
    {
        "name": "斯洛文尼亚",
        "url": "https://visa.vfsglobal.com/chn/zh/svn",
        "contact_url": "http://www.vfsglobal.cn/Slovenia/China/contact-us.html",
        "flag": "flag/img_map_slovania.gif"
    },
    {
        "name": "瑞典",
        "url": "https://visa.vfsglobal.com/chn/zh/swe",
        "contact_url": "https://www.vfsglobal.se/china/contact_us.html",
        "flag": "flag/img_map_sweden.gif"
    },
    {
        "name": "瑞士",
        "url": "https://visa.vfsglobal.com/chn/zh/che",
        "contact_url": "https://visa.vfsglobal.com/chn/zh/che/contact-us",
        "flag": "flag/img_map_switzerland.gif"
    }
]


def get_tomorrow_date():
    tomorrow = datetime.now() + timedelta(days=1)
    day = str(tomorrow.day).zfill(2)
    month = str(tomorrow.month).zfill(2)
    year = str(tomorrow.year)
    return f"{day}/{month}/{year}"


def keep_keys(dct, keys_to_keep):
    # 创建一个新字典，只包含要保留的键值对
    new_dict = {key: dct[key] for key in keys_to_keep}
    # 清空原始字典
    dct.clear()
    # 将新字典的键值对添加到原始字典中
    dct.update(new_dict)


def attempt_get_result(url, max_retries=30, retry_interval=1):
    for _ in range(max_retries):
        result = get_result(url)
        if result:
            return result
        print('失败重试')
        time.sleep(retry_interval)
    return False


def get_result(url):
    try:
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "macOS",
            "origin": "https://visa.vfsglobal.com",
            "sec-fetch-site": "same-site",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": "https://visa.vfsglobal.com/",
            "accept-encoding": "gzip, deflate, br",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        }
        proxies = {
            "http": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:22225",
            "https": "http://brd-customer-hl_c9d2b997-zone-residential_proxy1-country-in:<EMAIL>:22225",
        }
        # 记录请求开始时间
        response = requests.get(
            url, proxies=proxies, headers=headers, impersonate=random.choice(['safari17_2_ios', 'safari17_0']), verify=False
        )
        if response.status_code == 200:

            return response
        else:
            print(response.status_code)
            return False
    except Exception as e:
        print(e)
        return False


def extract_allocation_ids_and_time_slots(data):
    results = []
    for item in data:
        raw_date = item["date"]
        counters = item["counters"]
        for counter in counters:
            groups = counter["groups"]
            for group in groups:
                time_slots = group["timeSlots"]
                for slot in time_slots:
                    allocation_id = slot["allocationId"]
                    time = slot["timeSlot"]
                    remainingSeats = slot["remainingSeats"]
                    totalSeats = slot["totalSeats"]
                    results.append(
                        {
                            "allocationId": allocation_id,
                            "time": time,
                            "date": raw_date,
                            "remainingSeats": remainingSeats,
                            "totalSeats": totalSeats,
                        }
                    )
    return results


def extract_date(dict_item):
    date_str = dict_item["date"]
    return datetime.strptime(date_str, "%m/%d/%Y")


def get_options(center):
    missionCode = center.get('url').split('/')[-1]
    try:
        url = f"https://lift-apicn.vfsglobal.com/master/center/{missionCode}/chn/zh-CN"
        response = attempt_get_result(url)
        data = response.json()
        print(data)
        tempcenteroption = []
        for center_option in data:
            url = f"https://lift-apicn.vfsglobal.com/master/visacategory/{center_option['missionCode']}/chn/{quote(center_option['isoCode'])}/zh-CN"
            response = attempt_get_result(url)
            catedata = response.json()
            print(catedata)
            tempcatedata = []
            for cate_option in catedata:
                if cate_option.get('error') == None:
                    url = f"https://lift-apicn.vfsglobal.com/master/subvisacategory/{center_option['missionCode']}/chn/{quote(center_option['isoCode'])}/{quote(cate_option['code'])}/zh-CN"
                    response = attempt_get_result(url)
                    cateiddata = response.json()
                    print(cateiddata)
                    for d1 in cateiddata:
                        if d1.get("isApplicantOTPEnabled") == True:
                            a_c = (
                                f"{center_option.get('missionCode')}-{center_option.get('isoCode')}-{d1.get('code')}"
                            )
                            redis_client.hset(
                                "need_otp",
                                f"{a_c.strip()}",
                                f"{a_c.strip()}",
                            )
                            print(a_c, 1)
                        else:
                            a_c = (
                                f"{center_option.get('missionCode')}-{center_option.get('isoCode')}-{d1.get('code')}"
                            )
                            redis_client.hdel("need_otp", f"{a_c.strip()}")
                            print(a_c)
                    cate_option["sub"] = cateiddata
                    keep_keys(cate_option, ["sub", "code", "name"])
                    tempcatedata.append(cate_option)
            center_option["sub"] = tempcatedata
            keep_keys(center_option, ["sub", "isoCode", "centerName"])
            tempcenteroption.append(center_option)
        redis_client.hset(
            "vfs_center_data",
            missionCode,
            json.dumps(
                {
                    "missionCode": missionCode,
                    "missionCodeName": center["name"],
                    "data": tempcenteroption,
                }
            ),
        )
        print("done")

    except Exception as e:
        print(e)


for center in allcountry:
    print(f"正在扫描,{center.get('name')}")
    thread = threading.Thread(target=get_options, args=(center,),)
    thread.start()
