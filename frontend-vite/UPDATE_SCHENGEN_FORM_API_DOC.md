# 更新申根签证表单接口文档

## 📋 接口概述

更新现有的申根签证表单数据，支持分步更新和完整更新。

## 🔗 接口信息

### 基本信息
- **接口路径**: `/api/schengen-forms/{form_id}`
- **请求方法**: `PUT`
- **认证要求**: 需要JWT票据验证
- **接口标签**: 申根签证

### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `form_id` | string | 是 | 申请表ID |

### 请求体
```json
{
  // 基本信息
  "surname": "GONG",
  "given_name": "GAOHU", 
  "gender": "M",
  "birth_date": "26/02/1981",
  "nationality": "CHN",
  "passport_number": "*********",
  "passport_expire_date": "30/03/2035",
  "status": "draft",
  
  // 护照详细信息（第二步）
  "passport_surname": "GONG",
  "birth_surname": "GONG",
  "passport_given_name": "GAOHU",
  "passport_birth_date": "26/02/1981",
  "birth_country": "CHN",
  "birth_place": "HUBEI",
  "current_nationality": "CHN",
  "birth_nationality": "CHN",
  "passport_gender": "M",
  "passport_marital_status": "DIVC",
  "is_minor": "no",
  "guardian_type": "",
  "guardian_surname": "",
  "guardian_given_name": "",
  "guardian_address": "",
  "guardian_nationality": "CHN",
  "passport_number_detail": "*********",
  "passport_type": "OFPT",
  "passport_issue_date_detail": "31/03/2026",
  "issuing_country": "CHN",
  "passport_expire_date_detail": "30/03/2035",
  "issuing_authority": "SHANGHAI",
  
  // 申请人信息（第三步）
  "applicant_country": "CHN",
  "applicant_address": "123123",
  "occupation_id": "CHE",
  "occupation_others": "",
  "applicant_email": "<EMAIL>",
  "applicant_phone_code": "+2",
  "applicant_phone": "3123123",
  "employer_name": "12312",
  "employer_address": "3123",
  "employer_phone": "312",
  "employer_city": "123",
  "employer_postal_code": "123123",
  "employer_country": "CHN",
  "residence_other_nationality": 2,
  "residence_permit_no": "",
  "residence_permit_valid_until": "",
  "fingerprints_collected": 2,
  "date_of_collection": "",
  "previous_application_number": "",
  
  // 旅行信息（第四步）
  "purpose_of_travel": "TRSM",
  "purpose_of_travel_add_info": "*********",
  "number_of_entries": "ONE",
  "is_schengen_visa_issued": 2,
  "valid_from": "",
  "valid_till": "",
  "is_adequate_medical_insurance": 2,
  
  // 住宿信息（第五步）
  "inviting_party_type": "RegulationsstayinSwitzerland",
  "hotel_name": "",
  "hotel_address": "",
  "hotel_postal_code": "",
  "hotel_email": "",
  "hotel_street": "",
  "hotel_city": "",
  "hotel_country": "CHN",
  "hotel_phone_code": "+86",
  "hotel_phone": "",
  "inviting_person_surname": "",
  "inviting_person_given_name": "",
  "inviting_person_address": "",
  "inviting_person_postal_code": "",
  "inviting_person_email": "",
  "inviting_person_street": "",
  "inviting_person_city": "",
  "inviting_person_country": "CHN",
  "inviting_person_phone_code": "+86",
  "inviting_person_phone": "",
  "enterprise_name": "",
  "enterprise_address": "",
  "enterprise_postal_code": "",
  "enterprise_email": "",
  "enterprise_street": "",
  "enterprise_city": "",
  "enterprise_country": "CHN",
  "enterprise_phone_code": "+86",
  "enterprise_phone": "",
  "organisation_name": "",
  "organisation_address": "",
  "organisation_postal_code": "",
  "organisation_email": "",
  "organisation_street": "",
  "organisation_city": "",
  "organisation_country": "CHN",
  "organisation_phone_code": "+86",
  "organisation_phone": "",
  "temp_accommodation_address": "",
  "temp_accommodation_street": "",
  "temp_accommodation_city": "",
  "temp_accommodation_postal_code": "",
  "temp_accommodation_country": "CHN",
  "temp_accommodation_email": "",
  "temp_accommodation_phone_code": "+86",
  "temp_accommodation_phone": "",
  "regulations_address": "123123",
  "regulations_street": "123123",
  "regulations_city": "123123",
  "regulations_postal_code": "123123",
  "regulations_country": "CHN",
  "regulations_email": "<EMAIL>",
  "regulations_phone_code": "+555",
  "regulations_phone": "*********213",
  
  // 附加信息（第六步）
  "is_transit_visa": 2,
  "arrival_date": "",
  "departure_date": "",
  "duration_of_stay": "",
  "cost_covered_by": [],
  "cost_covered_by_others": "",
  "costsCoveredBy": "",
  "means_of_support": [],
  "means_of_support_others": "",
  "is_eu_citizen_family": 2,
  "eu_citizen_surname": "",
  "eu_citizen_given_name": "",
  "eu_citizen_nationality": "",
  "eu_citizen_birth_date": "",
  "eu_citizen_passport_number": "",
  "eu_citizen_relationship": "",
  "first_entry_schengen": "",
  "destination_countries": [],
  "inviting_person_covered_costs": 2
}
```

## 🔐 权限控制

### 用户权限检查
- **普通用户**: 只能更新自己创建的表单
- **客服/管理员**: 可以更新所有表单

### 权限验证逻辑
```sql
-- 普通用户权限检查
SELECT id FROM schengen_visa_applications
WHERE application_id = %s AND user_id = %s

-- 管理员/客服权限检查  
SELECT id FROM schengen_visa_applications
WHERE application_id = %s
```

## 📊 数据更新逻辑

### 1. 主表更新 (schengen_visa_applications)
```sql
UPDATE schengen_visa_applications
SET first_name = %s, last_name = %s, gender_id = %s,
    date_of_birth_year = %s, date_of_birth_month = %s, date_of_birth_day = %s,
    nationality_id = %s, passport_number = %s,
    expiry_date_year = %s, expiry_date_month = %s, expiry_date_day = %s,
    status = %s, updated_at = CURRENT_TIMESTAMP
WHERE application_id = %s AND user_id = %s
```

### 2. 护照信息更新 (schengen_passport_info)
- 检查记录是否存在，存在则UPDATE，不存在则跳过
- 包含监护人信息处理（未成年申请者）

### 3. 申请人信息更新 (schengen_applicant_info)
- 更新联系方式、职业、雇主信息等
- 处理电话区号格式化

### 4. 旅行信息更新 (schengen_travel_info)
- 更新旅行目的、入境次数、签证历史等

### 5. 住宿信息更新 (schengen_accommodation_info)
- 根据邀请方类型动态更新相应字段
- 支持6种邀请方类型的数据映射

### 6. 附加信息更新 (schengen_additional_info)
- 更新到达/离开日期、费用承担、支持方式等
- 处理JSON数组字段

## 📤 响应格式

### 成功响应
```json
{
  "code": 1,
  "message": "success",
  "data": null
}
```

### 错误响应
```json
{
  "detail": "表单不存在"
}
```

```json
{
  "detail": "更新表单失败: 具体错误信息"
}
```

## 🎯 核心特性

### 1. 分步更新支持
- 可以只更新部分步骤的数据
- 根据请求数据中的字段自动判断需要更新的表

### 2. 邀请方类型映射
```javascript
const inviting_party_mapping = {
  'InvitingPerson': 1,
  'Invitingenterprise': 2, 
  'Hotel': 3,
  'Temporary accommodation': 4,
  'InvitingOrganisation': 5,
  'RegulationsstayinSwitzerland': 6
};
```

### 3. 日期格式处理
- 支持 `DD/MM/YYYY` 格式
- 自动拆分为年、月、日字段存储

### 4. 监护人信息处理
- 只有未成年申请者才处理监护人信息
- 条件：`is_minor === 'yes'`

### 5. 电话区号处理
- 支持各种电话区号格式
- 默认值为 `+86`

## 🧪 测试用例

### 测试用例1：更新基本信息
```bash
PUT /api/schengen-forms/SCHENGEN_123456
Content-Type: application/json
Authorization: Bearer <token>

{
  "surname": "WANG",
  "given_name": "MING",
  "status": "submitted"
}
```

### 测试用例2：更新住宿信息
```bash
PUT /api/schengen-forms/SCHENGEN_123456
Content-Type: application/json
Authorization: Bearer <token>

{
  "inviting_party_type": "Hotel",
  "hotel_name": "Grand Hotel",
  "hotel_address": "123 Main St",
  "hotel_phone_code": "+1",
  "hotel_phone": "2025551234"
}
```

## ⚠️ 注意事项

### 1. 权限控制
- 普通用户只能更新自己的表单
- 管理员/客服可以更新所有表单

### 2. 数据完整性
- 更新操作不会删除现有数据
- 只更新请求中包含的字段

### 3. 事务处理
- 所有更新操作在同一个事务中执行
- 任何步骤失败都会回滚整个更新

### 4. 日志记录
- 记录详细的更新操作日志
- 包含用户ID、表单ID、更新字段等信息

## ✅ 接口特点

- ✅ **权限控制**: 基于用户角色的权限验证
- ✅ **分步更新**: 支持部分字段更新
- ✅ **数据映射**: 复杂的邀请方类型映射
- ✅ **格式处理**: 自动处理日期和电话格式
- ✅ **事务安全**: 确保数据一致性
- ✅ **详细日志**: 完整的操作记录

这个接口提供了完整的申根表单更新功能，支持所有6个步骤的数据更新！
